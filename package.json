{"name": "metamorphic-reactor", "displayName": "Metamorphic Reactor", "description": "Multi-agent LLM orchestration with autonomous consensus-driven task completion", "version": "1.0.0", "publisher": "metamorphic-labs", "engines": {"vscode": "^1.74.0"}, "enabledApiProposals": [], "categories": ["AI", "Other"], "keywords": ["ai", "llm", "agents", "automation", "consensus"], "activationEvents": ["onCommand:metamorphic-reactor.startTask"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "metamorphic-reactor.startTask", "title": "Start Metamorphic Task", "category": "Metamorphic"}], "configuration": {"title": "Metamorphic Reactor", "properties": {"metamorphic-reactor.maxAgents": {"type": "number", "default": 2, "minimum": 2, "maximum": 5, "description": "Maximum number of agents to run (2=Free, 3=Pro, 5=Dev+)"}, "metamorphic-reactor.apiKeys": {"type": "object", "description": "API keys for LLM providers", "properties": {"gemini": {"type": "array", "items": {"type": "string"}}, "claude": {"type": "array", "items": {"type": "string"}}, "openai": {"type": "array", "items": {"type": "string"}}}}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "vsce package"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "eslint": "^8.28.0", "typescript": "^4.9.4", "@vscode/test-electron": "^2.2.0", "vsce": "^2.15.0", "webpack": "^5.76.0", "webpack-cli": "^5.0.0", "ts-loader": "^9.4.0"}, "dependencies": {"axios": "^1.6.0"}}