<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  
  const dispatch = createEventDispatcher();
  
  export let selectedModel: string = 'gpt-4';
  export let showDescription: boolean = true;
  export let showProvider: boolean = true;
  export let size: 'small' | 'medium' | 'large' = 'medium';
  export let layout: 'dropdown' | 'grid' | 'list' = 'dropdown';
  export let filterByProvider: string | null = null;
  export let disabled: boolean = false;
  
  interface ModelInfo {
    value: string;
    label: string;
    provider: string;
    description: string;
    category: 'general' | 'code' | 'creative' | 'analytical';
    capabilities: string[];
    contextWindow: number;
    pricing: 'low' | 'medium' | 'high';
    speed: 'fast' | 'medium' | 'slow';
    recommended?: boolean;
  }
  
  const modelOptions: ModelInfo[] = [
    // OpenAI Models
    {
      value: 'gpt-4',
      label: 'GPT-4',
      provider: 'OpenAI',
      description: 'Most capable model for complex reasoning, coding, and creative tasks',
      category: 'general',
      capabilities: ['reasoning', 'coding', 'creative_writing', 'analysis', 'math'],
      contextWindow: 8192,
      pricing: 'high',
      speed: 'medium',
      recommended: true
    },
    {
      value: 'gpt-4-turbo',
      label: 'GPT-4 Turbo',
      provider: 'OpenAI',
      description: 'Faster GPT-4 with improved efficiency and larger context window',
      category: 'general',
      capabilities: ['reasoning', 'coding', 'creative_writing', 'analysis', 'math'],
      contextWindow: 128000,
      pricing: 'high',
      speed: 'fast',
      recommended: true
    },
    {
      value: 'gpt-3.5-turbo',
      label: 'GPT-3.5 Turbo',
      provider: 'OpenAI',
      description: 'Fast and cost-effective model for most tasks',
      category: 'general',
      capabilities: ['conversation', 'coding', 'analysis', 'summarization'],
      contextWindow: 16384,
      pricing: 'low',
      speed: 'fast'
    },
    
    // Anthropic Models
    {
      value: 'claude-3-opus',
      label: 'Claude 3 Opus',
      provider: 'Anthropic',
      description: 'Most powerful Claude model with exceptional reasoning capabilities',
      category: 'analytical',
      capabilities: ['reasoning', 'analysis', 'coding', 'creative_writing', 'math'],
      contextWindow: 200000,
      pricing: 'high',
      speed: 'slow',
      recommended: true
    },
    {
      value: 'claude-3-sonnet',
      label: 'Claude 3 Sonnet',
      provider: 'Anthropic',
      description: 'Balanced performance and speed for most use cases',
      category: 'general',
      capabilities: ['reasoning', 'analysis', 'coding', 'conversation'],
      contextWindow: 200000,
      pricing: 'medium',
      speed: 'medium',
      recommended: true
    },
    {
      value: 'claude-3-haiku',
      label: 'Claude 3 Haiku',
      provider: 'Anthropic',
      description: 'Fastest Claude model for quick responses',
      category: 'general',
      capabilities: ['conversation', 'summarization', 'analysis'],
      contextWindow: 200000,
      pricing: 'low',
      speed: 'fast'
    },
    
    // Google Models
    {
      value: 'gemini-pro',
      label: 'Gemini Pro',
      provider: 'Google',
      description: 'Google\'s most capable model with strong reasoning abilities',
      category: 'general',
      capabilities: ['reasoning', 'coding', 'analysis', 'math'],
      contextWindow: 32768,
      pricing: 'medium',
      speed: 'medium'
    },
    {
      value: 'gemini-pro-vision',
      label: 'Gemini Pro Vision',
      provider: 'Google',
      description: 'Multimodal model with vision capabilities',
      category: 'creative',
      capabilities: ['vision', 'reasoning', 'analysis', 'description'],
      contextWindow: 32768,
      pricing: 'medium',
      speed: 'medium'
    }
  ];
  
  // Filter models by provider if specified
  $: filteredModels = filterByProvider 
    ? modelOptions.filter(model => model.provider === filterByProvider)
    : modelOptions;
  
  $: selectedModelInfo = modelOptions.find(m => m.value === selectedModel);
  
  // Group models by provider for dropdown
  $: modelsByProvider = filteredModels.reduce((acc, model) => {
    if (!acc[model.provider]) {
      acc[model.provider] = [];
    }
    acc[model.provider].push(model);
    return acc;
  }, {} as Record<string, ModelInfo[]>);
  
  let isOpen = false;
  
  function selectModel(modelValue: string) {
    selectedModel = modelValue;
    isOpen = false;
    dispatch('modelSelected', {
      model: modelValue,
      info: modelOptions.find(m => m.value === modelValue)
    });
  }
  
  function toggleDropdown() {
    if (!disabled) {
      isOpen = !isOpen;
    }
  }
  
  function getProviderColor(provider: string): string {
    switch (provider) {
      case 'OpenAI': return 'text-green-400';
      case 'Anthropic': return 'text-blue-400';
      case 'Google': return 'text-yellow-400';
      default: return 'text-gray-400';
    }
  }
  
  function getProviderIcon(provider: string): string {
    switch (provider) {
      case 'OpenAI': return '🤖';
      case 'Anthropic': return '🧠';
      case 'Google': return '🔍';
      default: return '⚡';
    }
  }
  
  function getPricingColor(pricing: string): string {
    switch (pricing) {
      case 'low': return 'text-green-400';
      case 'medium': return 'text-yellow-400';
      case 'high': return 'text-red-400';
      default: return 'text-gray-400';
    }
  }
  
  function getSpeedColor(speed: string): string {
    switch (speed) {
      case 'fast': return 'text-green-400';
      case 'medium': return 'text-yellow-400';
      case 'slow': return 'text-red-400';
      default: return 'text-gray-400';
    }
  }
  
  // Size classes
  $: sizeClasses = {
    small: 'text-sm py-1 px-2',
    medium: 'text-base py-2 px-3',
    large: 'text-lg py-3 px-4'
  }[size];
</script>

<div class="model-selector relative">
  {#if layout === 'dropdown'}
    <!-- Dropdown Layout -->
    <div class="relative">
      <button
        type="button"
        class="w-full {sizeClasses} bg-gray-800 border border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors text-left flex items-center justify-between {disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-700'}"
        on:click={toggleDropdown}
        {disabled}
      >
        <div class="flex items-center gap-2">
          {#if showProvider}
            <span class="text-lg">{getProviderIcon(selectedModelInfo?.provider || '')}</span>
          {/if}
          <div>
            <div class="font-medium">{selectedModelInfo?.label || selectedModel}</div>
            {#if showProvider && selectedModelInfo}
              <div class="text-xs {getProviderColor(selectedModelInfo.provider)}">{selectedModelInfo.provider}</div>
            {/if}
          </div>
        </div>
        <div class="transform transition-transform {isOpen ? 'rotate-180' : ''}">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </div>
      </button>
      
      {#if isOpen}
        <div class="absolute z-50 w-full mt-1 bg-gray-800 border border-gray-700 rounded-md shadow-lg max-h-80 overflow-y-auto">
          {#each Object.entries(modelsByProvider) as [provider, models]}
            <div class="p-2 border-b border-gray-700">
              <div class="text-xs font-medium {getProviderColor(provider)} mb-1 flex items-center gap-1">
                <span>{getProviderIcon(provider)}</span>
                {provider}
              </div>
              {#each models as model}
                <button
                  type="button"
                  class="w-full text-left p-2 rounded hover:bg-gray-700 transition-colors {selectedModel === model.value ? 'bg-blue-600' : ''}"
                  on:click={() => selectModel(model.value)}
                >
                  <div class="flex items-center justify-between">
                    <div class="flex-1">
                      <div class="font-medium flex items-center gap-1">
                        {model.label}
                        {#if model.recommended}
                          <span class="text-xs bg-green-600 text-white px-1 rounded">Recommended</span>
                        {/if}
                      </div>
                      {#if showDescription}
                        <div class="text-xs text-gray-400 mt-1">{model.description}</div>
                      {/if}
                    </div>
                    <div class="text-xs text-gray-400 text-right ml-2">
                      <div class="{getPricingColor(model.pricing)}">{model.pricing}</div>
                      <div class="{getSpeedColor(model.speed)}">{model.speed}</div>
                    </div>
                  </div>
                </button>
              {/each}
            </div>
          {/each}
        </div>
      {/if}
    </div>
    
  {:else if layout === 'grid'}
    <!-- Grid Layout -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
      {#each filteredModels as model}
        <button
          type="button"
          class="p-3 text-left border rounded-lg transition-colors {selectedModel === model.value ? 'bg-blue-600 border-blue-500' : 'bg-gray-800 border-gray-700 hover:bg-gray-700'} {disabled ? 'opacity-50 cursor-not-allowed' : ''}"
          on:click={() => selectModel(model.value)}
          {disabled}
        >
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center gap-2">
              {#if showProvider}
                <span class="text-sm">{getProviderIcon(model.provider)}</span>
              {/if}
              <div class="font-medium">{model.label}</div>
              {#if model.recommended}
                <span class="text-xs bg-green-600 text-white px-1 rounded">★</span>
              {/if}
            </div>
            {#if showProvider}
              <div class="text-xs {getProviderColor(model.provider)}">{model.provider}</div>
            {/if}
          </div>
          {#if showDescription}
            <div class="text-xs text-gray-400 mb-2">{model.description}</div>
          {/if}
          <div class="flex justify-between text-xs">
            <div class="flex gap-2">
              <span class="{getPricingColor(model.pricing)}">💰 {model.pricing}</span>
              <span class="{getSpeedColor(model.speed)}">⚡ {model.speed}</span>
            </div>
            <div class="text-gray-400">{model.contextWindow.toLocaleString()} ctx</div>
          </div>
        </button>
      {/each}
    </div>
    
  {:else if layout === 'list'}
    <!-- List Layout -->
    <div class="space-y-2">
      {#each filteredModels as model}
        <button
          type="button"
          class="w-full p-3 text-left border rounded-lg transition-colors {selectedModel === model.value ? 'bg-blue-600 border-blue-500' : 'bg-gray-800 border-gray-700 hover:bg-gray-700'} {disabled ? 'opacity-50 cursor-not-allowed' : ''}"
          on:click={() => selectModel(model.value)}
          {disabled}
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              {#if showProvider}
                <span class="text-lg">{getProviderIcon(model.provider)}</span>
              {/if}
              <div>
                <div class="font-medium flex items-center gap-2">
                  {model.label}
                  {#if model.recommended}
                    <span class="text-xs bg-green-600 text-white px-1 rounded">Recommended</span>
                  {/if}
                </div>
                {#if showDescription}
                  <div class="text-xs text-gray-400 mt-1">{model.description}</div>
                {/if}
                {#if showProvider}
                  <div class="text-xs {getProviderColor(model.provider)} mt-1">{model.provider}</div>
                {/if}
              </div>
            </div>
            <div class="text-xs text-gray-400 text-right">
              <div class="flex gap-2 mb-1">
                <span class="{getPricingColor(model.pricing)}">💰 {model.pricing}</span>
                <span class="{getSpeedColor(model.speed)}">⚡ {model.speed}</span>
              </div>
              <div>{model.contextWindow.toLocaleString()} tokens</div>
            </div>
          </div>
        </button>
      {/each}
    </div>
  {/if}
</div>

<!-- Model Info Panel (optional) -->
{#if selectedModelInfo && showDescription && layout !== 'dropdown'}
  <div class="mt-4 p-3 bg-gray-800 border border-gray-700 rounded-lg">
    <h4 class="font-medium mb-2 flex items-center gap-2">
      <span>{getProviderIcon(selectedModelInfo.provider)}</span>
      {selectedModelInfo.label}
      {#if selectedModelInfo.recommended}
        <span class="text-xs bg-green-600 text-white px-1 rounded">Recommended</span>
      {/if}
    </h4>
    <p class="text-sm text-gray-400 mb-2">{selectedModelInfo.description}</p>
    <div class="flex flex-wrap gap-4 text-xs">
      <div class="flex items-center gap-1">
        <span class="{getProviderColor(selectedModelInfo.provider)}">🏢</span>
        {selectedModelInfo.provider}
      </div>
      <div class="flex items-center gap-1">
        <span class="{getPricingColor(selectedModelInfo.pricing)}">💰</span>
        {selectedModelInfo.pricing} cost
      </div>
      <div class="flex items-center gap-1">
        <span class="{getSpeedColor(selectedModelInfo.speed)}">⚡</span>
        {selectedModelInfo.speed} speed
      </div>
      <div class="flex items-center gap-1">
        <span class="text-gray-400">📄</span>
        {selectedModelInfo.contextWindow.toLocaleString()} tokens
      </div>
    </div>
    <div class="mt-2">
      <div class="text-xs text-gray-400 mb-1">Capabilities:</div>
      <div class="flex flex-wrap gap-1">
        {#each selectedModelInfo.capabilities as capability}
          <span class="text-xs bg-gray-700 text-gray-300 px-2 py-1 rounded">{capability}</span>
        {/each}
      </div>
    </div>
  </div>
{/if}

<style>
  .model-selector {
    position: relative;
  }
  
  /* Custom scrollbar for dropdown */
  .model-selector :global(.overflow-y-auto) {
    scrollbar-width: thin;
    scrollbar-color: #4B5563 #1F2937;
  }
  
  .model-selector :global(.overflow-y-auto::-webkit-scrollbar) {
    width: 6px;
  }
  
  .model-selector :global(.overflow-y-auto::-webkit-scrollbar-track) {
    background: #1F2937;
  }
  
  .model-selector :global(.overflow-y-auto::-webkit-scrollbar-thumb) {
    background: #4B5563;
    border-radius: 3px;
  }
  
  .model-selector :global(.overflow-y-auto::-webkit-scrollbar-thumb:hover) {
    background: #6B7280;
  }
</style>