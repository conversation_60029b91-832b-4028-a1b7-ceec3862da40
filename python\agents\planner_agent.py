"""
Planner Agent implementation for Metamorphic Reactor
Responsible for analyzing tasks and creating comprehensive implementation plans
"""

import logging
from typing import List
from datetime import datetime

from .base_agent import BaseAgent
from .agent_types import AgentConfig, AgentResponse, TaskContext, ConversationHistory

logger = logging.getLogger(__name__)

class PlannerAgent(BaseAgent):
    """
    Planner Agent - Creates detailed implementation plans for tasks
    
    Responsibilities:
    - Analyze task requirements and constraints
    - Break down complex tasks into manageable steps
    - Identify potential risks and dependencies
    - Create comprehensive implementation roadmaps
    - Propose resource allocation and timelines
    """
    
    def __init__(self, config: AgentConfig):
        super().__init__(config)
        self.planning_templates = {
            "software_development": self._get_software_dev_template(),
            "analysis": self._get_analysis_template(),
            "problem_solving": self._get_problem_solving_template(),
            "research": self._get_research_template()
        }
    
    async def generate_response(
        self, 
        task_context: TaskContext, 
        conversation_history: ConversationHistory,
        current_proposals: List[AgentResponse]
    ) -> AgentResponse:
        """Generate a comprehensive implementation plan"""
        
        logger.info(f"Planner {self.agent_id} analyzing task: {task_context.description[:50]}...")
        
        # Analyze task type and select appropriate template
        task_type = self._classify_task(task_context.description)
        
        # Generate planning content
        planning_content = await self._create_implementation_plan(
            task_context, task_type, current_proposals
        )
        
        # Evaluate consensus with existing proposals
        response = AgentResponse(
            agent_id=self.agent_id,
            session_id=self.session_id,
            role=self.config.role,
            message_id=f"plan_{int(datetime.utcnow().timestamp())}",
            content=planning_content,
            reasoning=self._get_planning_reasoning(task_context, current_proposals),
            confidence=self._calculate_planning_confidence(task_context),
            agrees_with_proposal=self._evaluate_agreement(current_proposals),
            consensus_score=0.0,  # Will be calculated after response
            suggested_changes=self._generate_suggestions(current_proposals),
            token_count=len(planning_content.split()),
            response_time_ms=0  # Will be set by base class
        )
        
        # Calculate consensus score
        response.consensus_score = self.evaluate_consensus(current_proposals, response)
        
        return response
    
    def get_role_prompt(self, task_context: TaskContext) -> str:
        """Get planner-specific system prompt"""
        
        return f"""You are an expert Planning Agent in the Metamorphic Reactor system.

Your role is to analyze tasks and create comprehensive, actionable implementation plans.

TASK: {task_context.description}

RESPONSIBILITIES:
1. Break down complex tasks into clear, manageable steps
2. Identify dependencies, risks, and potential blockers
3. Propose realistic timelines and resource allocation
4. Consider multiple implementation approaches
5. Ensure plans are specific, measurable, and actionable

PLANNING PRINCIPLES:
- Start with requirements analysis and goal clarification
- Use structured approaches (phases, milestones, deliverables)
- Consider both technical and non-technical constraints
- Include validation and testing strategies
- Plan for iterative improvement and feedback loops

CONTEXT FILES: {', '.join(task_context.context_files) if task_context.context_files else 'None'}

Current round: {task_context.current_round}/{task_context.max_rounds}

Generate a detailed implementation plan that other agents can critique and refine."""
    
    def _classify_task(self, description: str) -> str:
        """Classify task type for appropriate planning template"""
        
        description_lower = description.lower()
        
        if any(keyword in description_lower for keyword in [
            'code', 'program', 'software', 'develop', 'implement', 'build', 'api', 'function'
        ]):
            return "software_development"
        elif any(keyword in description_lower for keyword in [
            'analyze', 'study', 'examine', 'evaluate', 'assess', 'review'
        ]):
            return "analysis"
        elif any(keyword in description_lower for keyword in [
            'research', 'investigate', 'explore', 'find', 'search', 'discover'
        ]):
            return "research"
        else:
            return "problem_solving"
    
    async def _create_implementation_plan(
        self, 
        task_context: TaskContext, 
        task_type: str, 
        current_proposals: List[AgentResponse]
    ) -> str:
        """Create detailed implementation plan based on task type"""
        
        template = self.planning_templates.get(task_type, self.planning_templates["problem_solving"])
        
        # If this is a revision round, incorporate feedback
        if current_proposals and task_context.current_round > 1:
            return self._revise_plan_with_feedback(template, task_context, current_proposals)
        
        # First round - create initial plan
        return template.format(
            task_description=task_context.description,
            context_files=task_context.context_files,
            max_rounds=task_context.max_rounds,
            round_number=task_context.current_round
        )
    
    def _revise_plan_with_feedback(
        self, 
        base_template: str, 
        task_context: TaskContext, 
        current_proposals: List[AgentResponse]
    ) -> str:
        """Revise plan based on critic feedback and additional input"""
        
        # Extract feedback from critic agents
        critic_feedback = []
        for proposal in current_proposals:
            if proposal.role.value == "critic":
                critic_feedback.append(proposal.content)
        
        revision_notes = "\n".join([
            "## PLAN REVISION BASED ON FEEDBACK\n",
            "### Previous Round Feedback:",
            *[f"- {feedback[:200]}..." for feedback in critic_feedback[:3]],
            "\n### REVISED IMPLEMENTATION PLAN:\n"
        ])
        
        revised_plan = base_template.format(
            task_description=task_context.description,
            context_files=task_context.context_files,
            max_rounds=task_context.max_rounds,
            round_number=task_context.current_round
        )
        
        return revision_notes + revised_plan
    
    def _get_planning_reasoning(
        self, 
        task_context: TaskContext, 
        current_proposals: List[AgentResponse]
    ) -> str:
        """Generate reasoning for the planning approach"""
        
        if task_context.current_round == 1:
            return "Initial analysis and planning phase - breaking down task requirements into actionable steps"
        else:
            feedback_count = len([p for p in current_proposals if p.role.value == "critic"])
            return f"Revision round {task_context.current_round} - incorporating {feedback_count} pieces of feedback"
    
    def _calculate_planning_confidence(self, task_context: TaskContext) -> float:
        """Calculate confidence score for the plan"""
        
        base_confidence = 0.8
        
        # Adjust based on task complexity
        if len(task_context.description.split()) > 100:
            base_confidence -= 0.1  # Complex tasks have lower initial confidence
        
        # Adjust based on available context
        if task_context.context_files:
            base_confidence += 0.1
        
        # Adjust based on round number
        if task_context.current_round > 1:
            base_confidence += 0.05  # Confidence increases with refinement
        
        return min(0.95, max(0.5, base_confidence))
    
    def _evaluate_agreement(self, current_proposals: List[AgentResponse]) -> bool:
        """Evaluate if this agent agrees with current proposals"""
        
        if not current_proposals:
            return True  # First agent always agrees with empty state
        
        # Check if any critic has significant objections
        critic_objections = [
            p for p in current_proposals 
            if p.role.value == "critic" and p.consensus_score < 0.6
        ]
        
        return len(critic_objections) == 0
    
    def _generate_suggestions(self, current_proposals: List[AgentResponse]) -> List[str]:
        """Generate suggestions for improvement"""
        
        suggestions = []
        
        if not current_proposals:
            suggestions.append("Await feedback from critic agents for plan refinement")
        else:
            # Analyze feedback and suggest improvements
            for proposal in current_proposals:
                if proposal.role.value == "critic" and proposal.suggested_changes:
                    suggestions.extend(proposal.suggested_changes[:2])  # Take top 2 suggestions
        
        return suggestions[:5]  # Limit to 5 suggestions
    
    # Planning templates
    def _get_software_dev_template(self) -> str:
        return """# SOFTWARE DEVELOPMENT IMPLEMENTATION PLAN

## 1. REQUIREMENTS ANALYSIS
**Task**: {task_description}

### 1.1 Functional Requirements
- Core functionality to be implemented
- User interactions and interfaces
- Input/output specifications
- Performance requirements

### 1.2 Technical Requirements  
- Technology stack and frameworks
- Dependencies and libraries
- Platform compatibility
- Security considerations

## 2. ARCHITECTURE DESIGN

### 2.1 System Architecture
- High-level component structure
- Data flow and communication patterns
- Integration points and APIs
- Scalability considerations

### 2.2 Implementation Strategy
- Development approach (iterative, modular, etc.)
- Code organization and structure
- Testing strategy and frameworks
- Documentation requirements

## 3. IMPLEMENTATION PHASES

### Phase 1: Foundation Setup
- Project structure and configuration
- Core dependencies and tooling
- Basic scaffolding and interfaces

### Phase 2: Core Implementation
- Primary functionality development
- Unit tests for core components
- Basic integration testing

### Phase 3: Integration & Testing
- Component integration
- End-to-end testing
- Performance optimization
- Error handling and validation

### Phase 4: Finalization
- Code review and refactoring
- Documentation completion
- Deployment preparation
- Final testing and validation

## 4. RISK ASSESSMENT
- Technical complexity risks
- Dependency and compatibility issues
- Timeline and resource constraints
- Quality and testing challenges

## 5. SUCCESS CRITERIA
- Functional requirements met
- Performance targets achieved
- Code quality standards satisfied
- Documentation and testing complete

**Context Files**: {context_files}
**Implementation Round**: {round_number}/{max_rounds}"""

    def _get_analysis_template(self) -> str:
        return """# ANALYSIS IMPLEMENTATION PLAN

## 1. ANALYSIS SCOPE
**Task**: {task_description}

### 1.1 Analysis Objectives
- Primary research questions
- Success criteria and metrics
- Expected outcomes and deliverables
- Stakeholder requirements

### 1.2 Analysis Boundaries
- Scope limitations and exclusions
- Time and resource constraints
- Available data and sources
- Quality and reliability factors

## 2. METHODOLOGY

### 2.1 Analysis Approach
- Research methodology selection
- Data collection strategies
- Analysis frameworks and tools
- Validation and verification methods

### 2.2 Process Design
- Sequential analysis steps
- Parallel processing opportunities
- Review and iteration cycles
- Quality assurance checkpoints

## 3. EXECUTION PHASES

### Phase 1: Data Gathering
- Source identification and access
- Data collection and preparation
- Initial quality assessment
- Baseline establishment

### Phase 2: Analysis Execution
- Primary analysis implementation
- Pattern identification and insights
- Hypothesis testing and validation
- Interim results evaluation

### Phase 3: Synthesis & Interpretation
- Results consolidation
- Insight development and validation
- Implications assessment
- Recommendations formulation

### Phase 4: Reporting & Communication
- Findings documentation
- Presentation preparation
- Stakeholder communication
- Follow-up planning

## 4. DELIVERABLES
- Analysis reports and documentation
- Data summaries and visualizations
- Recommendations and action items
- Process documentation and lessons learned

**Context Files**: {context_files}
**Analysis Round**: {round_number}/{max_rounds}"""

    def _get_problem_solving_template(self) -> str:
        return """# PROBLEM-SOLVING IMPLEMENTATION PLAN

## 1. PROBLEM DEFINITION
**Task**: {task_description}

### 1.1 Problem Statement
- Clear problem articulation
- Root cause identification
- Impact and urgency assessment
- Success criteria definition

### 1.2 Constraint Analysis
- Resource limitations
- Time constraints
- Technical constraints
- Stakeholder requirements

## 2. SOLUTION APPROACH

### 2.1 Solution Strategy
- Problem-solving methodology
- Alternative approaches consideration
- Risk-benefit analysis
- Implementation feasibility

### 2.2 Solution Design
- Core solution components
- Implementation sequence
- Dependencies and prerequisites
- Validation and testing approach

## 3. IMPLEMENTATION ROADMAP

### Phase 1: Solution Preparation
- Resource allocation and setup
- Tool and environment preparation
- Team coordination and planning
- Initial validation and testing

### Phase 2: Core Implementation
- Primary solution development
- Iterative testing and refinement
- Progress monitoring and adjustment
- Quality assurance and validation

### Phase 3: Solution Deployment
- Implementation execution
- Performance monitoring
- Issue resolution and optimization
- Stakeholder communication

### Phase 4: Evaluation & Optimization
- Results assessment and validation
- Performance optimization
- Lessons learned documentation
- Future improvement planning

## 4. MONITORING & EVALUATION
- Success metrics and KPIs
- Progress tracking mechanisms
- Quality assurance processes
- Continuous improvement strategies

**Context Files**: {context_files}
**Solution Round**: {round_number}/{max_rounds}"""

    def _get_research_template(self) -> str:
        return """# RESEARCH IMPLEMENTATION PLAN

## 1. RESEARCH DESIGN
**Task**: {task_description}

### 1.1 Research Objectives
- Primary research questions
- Hypothesis formulation
- Expected contributions
- Success criteria

### 1.2 Research Scope
- Literature review boundaries
- Data collection scope
- Analysis limitations
- Timeline constraints

## 2. METHODOLOGY

### 2.1 Research Approach
- Methodology selection and justification
- Data collection strategies
- Analysis frameworks
- Validation methods

### 2.2 Process Framework
- Sequential research phases
- Iterative refinement cycles
- Peer review and feedback
- Quality control measures

## 3. RESEARCH PHASES

### Phase 1: Literature Review
- Existing research analysis
- Knowledge gap identification
- Theoretical framework development
- Methodology refinement

### Phase 2: Data Collection
- Primary data gathering
- Secondary source compilation
- Data validation and preparation
- Initial analysis and insights

### Phase 3: Analysis & Synthesis
- Comprehensive data analysis
- Pattern recognition and insights
- Theory validation and development
- Preliminary conclusions

### Phase 4: Documentation & Dissemination
- Research documentation
- Findings communication
- Peer review and feedback
- Publication and sharing

## 4. DELIVERABLES
- Research reports and papers
- Data sets and analysis results
- Methodology documentation
- Recommendations for future research

**Context Files**: {context_files}
**Research Round**: {round_number}/{max_rounds}"""