<script lang="ts">
  import { vscodeApi } from '../utils/vscode';
  import { createEventDispatcher } from 'svelte';
  import ModelSelector from './ModelSelector.svelte';
  import RoleSelector from './RoleSelector.svelte';
  
  const dispatch = createEventDispatcher();
  
  // Form state
  let formData = {
    name: '',
    role: 'coder',
    model: 'gpt-4',
    prompt: '',
    capabilities: [],
    team_id: '',
    temperature: 0.7,
    max_tokens: 2000,
    max_retries: 3,
    timeout_seconds: 60,
    consensus_threshold: 0.8
  };
  
  // UI state
  let isCreating = false;
  let errors: string[] = [];
  let warnings: string[] = [];
  let showAdvanced = false;
  let customCapability = '';
  
  // Role options now handled by RoleSelector component
  
  // Model options now handled by ModelSelector component
  
  const capabilityOptions = {
    coder: ['code_generation', 'refactoring', 'debugging', 'testing', 'documentation', 'api_design'],
    debugger: ['error_analysis', 'stack_trace_analysis', 'performance_profiling', 'memory_debugging', 'log_analysis'],
    planner: ['task_decomposition', 'resource_allocation', 'timeline_management', 'dependency_analysis', 'risk_assessment'],
    critic: ['code_review', 'quality_assurance', 'security_analysis', 'performance_review', 'design_critique'],
    pm: ['stakeholder_management', 'requirements_gathering', 'sprint_planning', 'team_coordination', 'progress_tracking'],
    ux: ['user_research', 'wireframing', 'prototyping', 'usability_testing', 'accessibility_analysis'],
    qa: ['test_planning', 'test_execution', 'regression_testing', 'automation_testing', 'performance_testing'],
    orchestrator: ['agent_coordination', 'task_distribution', 'consensus_building', 'conflict_resolution', 'workflow_management']
  };
  
  const roleLabels: Record<string, string> = {
    coder: 'Coder',
    debugger: 'Debugger',
    planner: 'Planner',
    critic: 'Critic',
    pm: 'Project Manager',
    ux: 'UX Designer',
    qa: 'QA Engineer',
    orchestrator: 'Orchestrator'
  };

  const defaultPrompts = {
    coder: 'You are an expert software developer with deep knowledge of programming best practices, design patterns, and modern development tools. You write clean, efficient, and well-documented code.',
    debugger: 'You are a debugging specialist with exceptional analytical skills. You systematically identify root causes of issues, analyze stack traces, and provide clear explanations with effective solutions.',
    planner: 'You are an experienced project manager and strategic planner. You excel at breaking down complex projects into manageable tasks, identifying dependencies, and organizing work efficiently.',
    critic: 'You are a senior code reviewer with expertise in software quality, security, and best practices. You provide constructive feedback and identify potential improvements.',
    pm: 'You are a skilled project manager focused on team coordination, stakeholder management, and ensuring project success through effective planning and communication.',
    ux: 'You are a user experience expert who designs intuitive, accessible interfaces and conducts user research to inform design decisions.',
    qa: 'You are a quality assurance expert with deep knowledge of testing methodologies, test planning, and ensuring software quality through systematic testing.',
    orchestrator: 'You are an orchestration specialist who coordinates multiple agents, manages workflows, and ensures effective collaboration across teams.'
  };
  
  // Reactive statements
  $: availableCapabilities = capabilityOptions[formData.role] || [];
  $: temperatureLabel = formData.temperature < 0.3 ? 'Focused' : formData.temperature < 0.7 ? 'Balanced' : 'Creative';

  // Update prompt when role changes
  $: if (formData.role && defaultPrompts[formData.role] && !formData.prompt) {
    formData.prompt = defaultPrompts[formData.role];
  }

  // Update capabilities when role changes - use role directly to avoid circular dependency
  $: if (formData.role) {
    const roleCapabilities = capabilityOptions[formData.role] || [];
    if (roleCapabilities.length > 0) {
      // Keep existing capabilities that are still valid for the new role
      const filteredCapabilities = formData.capabilities.filter(cap => roleCapabilities.includes(cap));

      // Add default capabilities for the role if none selected
      if (filteredCapabilities.length === 0) {
        formData.capabilities = roleCapabilities.slice(0, 3);
      } else {
        formData.capabilities = filteredCapabilities;
      }
    }
  }
  
  function toggleCapability(capability: string) {
    if (formData.capabilities.includes(capability)) {
      formData.capabilities = formData.capabilities.filter(c => c !== capability);
    } else {
      formData.capabilities = [...formData.capabilities, capability];
    }
  }
  
  function addCustomCapability() {
    if (customCapability.trim() && !formData.capabilities.includes(customCapability.trim())) {
      formData.capabilities = [...formData.capabilities, customCapability.trim()];
      customCapability = '';
    }
  }
  
  function removeCapability(capability: string) {
    formData.capabilities = formData.capabilities.filter(c => c !== capability);
  }
  
  function resetForm() {
    formData = {
      name: '',
      role: 'coder',
      model: 'gpt-4',
      prompt: '',
      capabilities: [],
      team_id: '',
      temperature: 0.7,
      max_tokens: 2000,
      max_retries: 3,
      timeout_seconds: 60,
      consensus_threshold: 0.8
    };
    errors = [];
    warnings = [];
    showAdvanced = false;
  }
  
  function validateForm() {
    errors = [];
    warnings = [];
    
    if (!formData.name.trim()) {
      errors.push('Agent name is required');
    }
    
    if (!formData.prompt.trim()) {
      errors.push('Agent prompt is required');
    }
    
    if (formData.prompt.length > 5000) {
      errors.push('Prompt is too long (max 5000 characters)');
    }
    
    if (formData.capabilities.length === 0) {
      warnings.push('Consider adding at least one capability');
    }
    
    if (formData.capabilities.length > 10) {
      warnings.push('Too many capabilities may reduce focus');
    }
    
    if (formData.temperature < 0.1) {
      warnings.push('Very low temperature may make responses too deterministic');
    }
    
    if (formData.temperature > 1.5) {
      warnings.push('High temperature may make responses too random');
    }
    
    if (formData.max_tokens < 500) {
      warnings.push('Low token limit may truncate responses');
    }
    
    if (formData.max_tokens > 4000) {
      warnings.push('High token limit may increase costs');
    }
    
    return errors.length === 0;
  }
  
  async function createAgent() {
    if (!validateForm()) return;
    
    isCreating = true;
    
    try {
      // Send create agent request to VS Code extension
      vscodeApi.postMessage({
        command: 'createAgent',
        data: formData
      });
      
      // Listen for response
      const handleMessage = (event: MessageEvent) => {
        const message = event.data;
        
        if (message.command === 'agentCreated') {
          window.removeEventListener('message', handleMessage);
          isCreating = false;
          
          if (message.success) {
            dispatch('agentCreated', message.agent);
            resetForm();
          } else {
            errors = message.errors || ['Failed to create agent'];
          }
        }
      };
      
      window.addEventListener('message', handleMessage);
      
      // Timeout after 10 seconds
      setTimeout(() => {
        window.removeEventListener('message', handleMessage);
        if (isCreating) {
          isCreating = false;
          errors = ['Request timed out'];
        }
      }, 10000);
      
    } catch (error) {
      isCreating = false;
      errors = ['Failed to create agent: ' + error.message];
    }
  }
  
  function previewAgent() {
    if (!validateForm()) return;
    
    vscodeApi.postMessage({
      command: 'previewAgent',
      data: formData
    });
  }
</script>

<div class="max-w-4xl mx-auto p-6 bg-gray-900 text-white">
  <div class="mb-8">
    <h1 class="text-3xl font-bold mb-2">Create New Agent</h1>
    <p class="text-gray-400">Configure and create a new AI agent for your team</p>
  </div>
  
  <!-- Error/Warning Messages -->
  {#if errors.length > 0}
    <div class="mb-6 p-4 bg-red-900 border border-red-700 rounded-lg">
      <h3 class="font-semibold text-red-300 mb-2">❌ Errors:</h3>
      <ul class="text-red-200 text-sm space-y-1">
        {#each errors as error}
          <li>• {error}</li>
        {/each}
      </ul>
    </div>
  {/if}
  
  {#if warnings.length > 0}
    <div class="mb-6 p-4 bg-yellow-900 border border-yellow-700 rounded-lg">
      <h3 class="font-semibold text-yellow-300 mb-2">⚠️ Warnings:</h3>
      <ul class="text-yellow-200 text-sm space-y-1">
        {#each warnings as warning}
          <li>• {warning}</li>
        {/each}
      </ul>
    </div>
  {/if}
  
  <form on:submit|preventDefault={createAgent} class="space-y-6">
    <!-- Basic Information -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Agent Name -->
      <div>
        <label class="block text-sm font-medium mb-2">Agent Name *</label>
        <input
          type="text"
          bind:value={formData.name}
          placeholder="e.g., Python Expert, Code Reviewer"
          class="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          required
        />
      </div>
      
      <!-- Team Assignment -->
      <div>
        <label class="block text-sm font-medium mb-2">Team ID (Optional)</label>
        <input
          type="text"
          bind:value={formData.team_id}
          placeholder="e.g., frontend-team, backend-team"
          class="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
    </div>
    
    <!-- Role Selection -->
    <div>
      <label class="block text-sm font-medium mb-3">Agent Role *</label>
      <RoleSelector 
        bind:selectedRole={formData.role}
        layout="grid"
        showCapabilities={true}
        on:roleSelected={(event) => {
          formData.role = event.detail.role;
          console.log('Selected role:', event.detail.info);
        }}
      />
    </div>
    
    <!-- Model Selection -->
    <div>
      <label class="block text-sm font-medium mb-3">AI Model *</label>
      <ModelSelector 
        bind:selectedModel={formData.model}
        layout="grid"
        on:modelSelected={(event) => {
          formData.model = event.detail.model;
          console.log('Selected model:', event.detail.info);
        }}
      />
    </div>
    
    <!-- Agent Prompt -->
    <div>
      <label class="block text-sm font-medium mb-2">Agent Prompt *</label>
      <textarea
        bind:value={formData.prompt}
        placeholder="Describe the agent's personality, expertise, and behavior..."
        class="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 h-32 resize-y"
        required
      ></textarea>
      <div class="text-xs text-gray-400 mt-1">
        {formData.prompt.length}/5000 characters
      </div>
    </div>
    
    <!-- Capabilities -->
    <div>
      <label class="block text-sm font-medium mb-3">Agent Capabilities</label>
      
      <!-- Suggested Capabilities -->
      <div class="mb-4">
        <h4 class="text-sm font-medium mb-2">Suggested for {roleLabels[formData.role]}:</h4>
        <div class="flex flex-wrap gap-2">
          {#each availableCapabilities as capability}
            <button
              type="button"
              class="px-3 py-1 text-sm rounded-full border transition-colors {formData.capabilities.includes(capability) ? 'bg-green-600 border-green-500' : 'bg-gray-800 border-gray-700 hover:bg-gray-700'}"
              on:click={() => toggleCapability(capability)}
            >
              {capability}
            </button>
          {/each}
        </div>
      </div>
      
      <!-- Custom Capability -->
      <div class="mb-4">
        <div class="flex gap-2">
          <input
            type="text"
            bind:value={customCapability}
            placeholder="Add custom capability..."
            class="flex-1 px-3 py-2 bg-gray-800 border border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            on:keypress={(e) => e.key === 'Enter' && addCustomCapability()}
          />
          <button
            type="button"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            on:click={addCustomCapability}
          >
            Add
          </button>
        </div>
      </div>
      
      <!-- Selected Capabilities -->
      {#if formData.capabilities.length > 0}
        <div>
          <h4 class="text-sm font-medium mb-2">Selected Capabilities:</h4>
          <div class="flex flex-wrap gap-2">
            {#each formData.capabilities as capability}
              <span class="px-3 py-1 bg-blue-600 text-white text-sm rounded-full flex items-center gap-1">
                {capability}
                <button
                  type="button"
                  class="text-blue-200 hover:text-white"
                  on:click={() => removeCapability(capability)}
                >
                  ×
                </button>
              </span>
            {/each}
          </div>
        </div>
      {/if}
    </div>
    
    <!-- Advanced Settings -->
    <div class="border-t border-gray-700 pt-6">
      <button
        type="button"
        class="flex items-center gap-2 text-sm font-medium mb-4"
        on:click={() => showAdvanced = !showAdvanced}
      >
        <span class="transform transition-transform {showAdvanced ? 'rotate-90' : ''}">▶</span>
        Advanced Settings
      </button>
      
      {#if showAdvanced}
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Temperature -->
          <div>
            <label class="block text-sm font-medium mb-2">
              Temperature: {formData.temperature} ({temperatureLabel})
            </label>
            <input
              type="range"
              bind:value={formData.temperature}
              min="0.1"
              max="2.0"
              step="0.1"
              class="w-full accent-blue-600"
            />
            <div class="flex justify-between text-xs text-gray-400 mt-1">
              <span>Focused</span>
              <span>Creative</span>
            </div>
          </div>
          
          <!-- Max Tokens -->
          <div>
            <label class="block text-sm font-medium mb-2">Max Tokens</label>
            <input
              type="number"
              bind:value={formData.max_tokens}
              min="100"
              max="8000"
              step="100"
              class="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <!-- Max Retries -->
          <div>
            <label class="block text-sm font-medium mb-2">Max Retries</label>
            <input
              type="number"
              bind:value={formData.max_retries}
              min="0"
              max="10"
              class="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <!-- Timeout -->
          <div>
            <label class="block text-sm font-medium mb-2">Timeout (seconds)</label>
            <input
              type="number"
              bind:value={formData.timeout_seconds}
              min="10"
              max="300"
              step="10"
              class="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <!-- Consensus Threshold -->
          <div class="md:col-span-2">
            <label class="block text-sm font-medium mb-2">
              Consensus Threshold: {formData.consensus_threshold}
            </label>
            <input
              type="range"
              bind:value={formData.consensus_threshold}
              min="0.1"
              max="1.0"
              step="0.1"
              class="w-full accent-blue-600"
            />
            <div class="flex justify-between text-xs text-gray-400 mt-1">
              <span>Flexible</span>
              <span>Strict</span>
            </div>
          </div>
        </div>
      {/if}
    </div>
    
    <!-- Action Buttons -->
    <div class="flex justify-between items-center pt-6">
      <button
        type="button"
        class="px-4 py-2 text-gray-400 hover:text-white transition-colors"
        on:click={resetForm}
      >
        Reset Form
      </button>
      
      <div class="flex gap-3">
        <button
          type="button"
          class="px-6 py-2 bg-gray-700 text-white rounded-md hover:bg-gray-600 transition-colors"
          on:click={previewAgent}
          disabled={isCreating}
        >
          Preview
        </button>
        
        <button
          type="submit"
          class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          disabled={isCreating}
        >
          {#if isCreating}
            <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            Creating...
          {:else}
            Create Agent
          {/if}
        </button>
      </div>
    </div>
  </form>
</div>

<style>
  /* Custom scrollbar for webkit browsers */
  :global(.scrollbar-thin) {
    scrollbar-width: thin;
    scrollbar-color: #4B5563 #1F2937;
  }
  
  :global(.scrollbar-thin::-webkit-scrollbar) {
    width: 6px;
  }
  
  :global(.scrollbar-thin::-webkit-scrollbar-track) {
    background: #1F2937;
  }
  
  :global(.scrollbar-thin::-webkit-scrollbar-thumb) {
    background: #4B5563;
    border-radius: 3px;
  }
  
  :global(.scrollbar-thin::-webkit-scrollbar-thumb:hover) {
    background: #6B7280;
  }
</style>