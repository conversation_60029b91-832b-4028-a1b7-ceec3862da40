"""
Agent Orchestrator for Metamorphic Reactor
Manages multi-agent conversations and consensus building
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, AsyncGenerator
from datetime import datetime, timedelta
import uuid

from .agent_types import (
    AgentRole, TaskStatus, AgentStatus, ConsensusLevel,
    AgentConfig, AgentResponse, ConversationRound, TaskContext,
    AgentCreationRequest, AgentProfile, Team, TeamCreationRequest, TeamRole, TeamMember
)
from .agent_factory import agent_factory
from .base_agent import BaseAgent
from ..utils.conversation_manager import conversation_manager
from ..services import rate_limiter, safety_guardian, LimitScope

logger = logging.getLogger(__name__)

class AgentOrchestrator:
    """
    Orchestrates multi-agent conversations for autonomous consensus building
    
    Manages the "Plan → Act → Critique → Revise" loop until agents reach
    unanimous consensus or escalate to Meta-Block for human intervention.
    """
    
    def __init__(self):
        self.active_tasks: Dict[str, TaskContext] = {}
        self.task_agents: Dict[str, Dict[str, BaseAgent]] = {}
        self.conversation_streams: Dict[str, asyncio.Queue] = {}
        self.shutdown_flag = False
        
        # Team management
        self.teams: Dict[str, Team] = {}  # team_id -> Team
        
        # Configuration
        self.default_max_rounds = 10
        self.default_timeout_minutes = 15
        self.consensus_threshold = 1.0  # Require unanimous agreement
        self.meta_block_threshold = 3   # Trigger meta-block after 3 failed rounds
    
    async def initialize(self):
        """Initialize the orchestrator"""
        logger.info("Initializing Agent Orchestrator")
        
        # Initialize conversation manager
        await conversation_manager.initialize()
        
        # Initialize services
        await rate_limiter.initialize()
        await safety_guardian.initialize()
        
        # Perform any initialization tasks
        await self._setup_monitoring()
        
        logger.info("Agent Orchestrator initialized successfully")
    
    async def create_task(
        self,
        task_id: str,
        description: str,
        context_files: List[str] = None,
        max_rounds: int = None,
        timeout_minutes: int = None,
        require_consensus: bool = True,
        agent_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Create a new multi-agent task
        
        Args:
            task_id: Unique task identifier
            description: Task description
            context_files: List of context file paths
            max_rounds: Maximum conversation rounds
            timeout_minutes: Task timeout in minutes
            require_consensus: Whether to require unanimous consensus
            agent_config: Additional agent configuration
            
        Returns:
            Dict containing task creation result
        """
        
        if task_id in self.active_tasks:
            raise ValueError(f"Task {task_id} already exists")
        
        # Create task context
        task_context = TaskContext(
            task_id=task_id,
            description=description,
            max_rounds=max_rounds or self.default_max_rounds,
            timeout_minutes=timeout_minutes or self.default_timeout_minutes,
            require_consensus=require_consensus,
            context_files=context_files or [],
            status=TaskStatus.PENDING
        )
        
        # Determine team size based on configuration
        team_size = agent_config.get("team_size", 3) if agent_config else 3
        
        # Create agent team
        try:
            agents = agent_factory.create_agent_team(
                task_description=description,
                team_size=team_size,
                task_id=task_id,
                context_files=context_files or [],
                **(agent_config or {})
            )
            
            # Store task and agents
            self.active_tasks[task_id] = task_context
            self.task_agents[task_id] = agents
            self.conversation_streams[task_id] = asyncio.Queue()
            
            # Update task context with agent info
            task_context.participating_agents = list(agents.keys())
            for role_or_id, agent in agents.items():
                task_context.agent_configs[agent.agent_id] = agent.config
            
            logger.info(f"Created task {task_id} with {len(agents)} agents")
            
            return {
                "task_id": task_id,
                "status": task_context.status.value,
                "agents": [
                    {
                        "agent_id": agent.agent_id,
                        "role": agent.role.value,
                        "session_id": agent.session_id
                    }
                    for agent in agents.values()
                ],
                "team_size": len(agents),
                "created_at": task_context.created_at
            }
            
        except Exception as e:
            logger.error(f"Failed to create task {task_id}: {e}")
            raise
    
    async def execute_task(self, task_id: str) -> Dict[str, Any]:
        """
        Execute multi-agent conversation for a task
        
        Args:
            task_id: Task identifier
            
        Returns:
            Dict containing execution results
        """
        
        if task_id not in self.active_tasks:
            raise ValueError(f"Task {task_id} not found")
        
        task_context = self.active_tasks[task_id]
        agents = self.task_agents[task_id]
        
        try:
            # Check rate limits before starting task
            rate_check = await rate_limiter.check_rate_limit(
                scope=LimitScope.TASK,
                scope_id=task_id,
                request_tokens=100,  # Estimated initial tokens
                context={"task_description": task_context.description}
            )
            
            if not rate_check.allowed:
                raise Exception(f"Rate limit exceeded: {rate_check.limit_hit.limit_type.value}")
            
            # Safety check on task description
            safety_check = await safety_guardian.check_content_safety(
                content=task_context.description,
                task_id=task_id,
                context={"operation": "task_execution"}
            )
            
            if not safety_check.allowed:
                task_context.status = TaskStatus.FAILED
                raise Exception(f"Task blocked by safety guardian: {safety_check.overall_level.value}")
            
            task_context.status = TaskStatus.RUNNING
            task_context.started_at = datetime.utcnow()
            
            logger.info(f"Starting task execution: {task_id}")
            
            # Execute conversation rounds
            result = await self._execute_conversation_rounds(task_context, agents)
            
            # Update final status
            if result["consensus_reached"]:
                task_context.status = TaskStatus.COMPLETED
                task_context.final_consensus = result["final_response"]
                task_context.consensus_score = result["consensus_score"]
            elif result["meta_block_triggered"]:
                task_context.status = TaskStatus.META_BLOCK
            else:
                task_context.status = TaskStatus.FAILED
            
            task_context.completed_at = datetime.utcnow()
            
            # Save final task state to conversation history
            await conversation_manager.save_task(task_context)
            
            logger.info(f"Task {task_id} completed with status: {task_context.status.value}")
            
            return result
            
        except Exception as e:
            task_context.status = TaskStatus.FAILED
            task_context.completed_at = datetime.utcnow()
            logger.error(f"Task execution failed for {task_id}: {e}")
            raise
    
    async def _execute_conversation_rounds(
        self, 
        task_context: TaskContext, 
        agents: Dict[str, BaseAgent]
    ) -> Dict[str, Any]:
        """Execute the main conversation loop"""
        
        conversation_history = []
        consensus_reached = False
        meta_block_triggered = False
        final_response = None
        final_consensus_score = 0.0
        
        # Get ordered agents (planner, critic, additional)
        agent_order = self._get_agent_execution_order(agents)
        
        for round_num in range(1, task_context.max_rounds + 1):
            logger.info(f"Starting round {round_num} for task {task_context.task_id}")
            
            task_context.current_round = round_num
            
            # Create new conversation round
            round_context = ConversationRound(
                round_number=round_num,
                task_id=task_context.task_id
            )
            
            # Execute agent responses in order
            current_proposals = []
            
            for agent in agent_order:
                try:
                    # Generate agent response
                    response = await agent.process_task(
                        task_context, conversation_history, current_proposals
                    )
                    
                    current_proposals.append(response)
                    round_context.responses.append(response)
                    
                    # Stream response to subscribers
                    await self._stream_response(task_context.task_id, response)
                    
                    logger.debug(f"Agent {agent.agent_id} completed response in round {round_num}")
                    
                except Exception as e:
                    logger.error(f"Agent {agent.agent_id} failed in round {round_num}: {e}")
                    # Continue with other agents
                    continue
            
            # Analyze round results
            consensus_analysis = self._analyze_consensus(current_proposals)
            round_context.consensus_level = consensus_analysis["level"]
            round_context.consensus_score = consensus_analysis["score"]
            round_context.completed_at = datetime.utcnow()
            round_context.is_complete = True
            
            # Check for consensus
            if consensus_analysis["unanimous"]:
                consensus_reached = True
                final_response = self._synthesize_final_response(current_proposals)
                final_consensus_score = consensus_analysis["score"]
                round_context.requires_meta_block = False
                break
            
            # Check for meta-block conditions
            if self._should_trigger_meta_block(task_context, round_context, round_num):
                meta_block_triggered = True
                round_context.requires_meta_block = True
                round_context.meta_block_reason = self._get_meta_block_reason(
                    task_context, round_context, round_num
                )
                break
            
            # Add round to task context
            task_context.conversation_rounds.append(round_context)
            
            # Save round to conversation history
            await conversation_manager.save_conversation_round(task_context.task_id, round_context)
            
            # Update conversation history
            conversation_history.extend([
                {
                    "round": round_num,
                    "agent_id": response.agent_id,
                    "role": response.role.value,
                    "content": response.content,
                    "consensus_score": response.consensus_score,
                    "timestamp": response.timestamp.isoformat()
                }
                for response in current_proposals
            ])
            
            # Brief pause between rounds
            await asyncio.sleep(0.5)
        
        # Final round processing
        if round_context:
            task_context.conversation_rounds.append(round_context)
            # Save final round if it wasn't already saved
            if round_context not in [r for r in task_context.conversation_rounds[:-1]]:
                await conversation_manager.save_conversation_round(task_context.task_id, round_context)
        
        return {
            "consensus_reached": consensus_reached,
            "meta_block_triggered": meta_block_triggered,
            "final_response": final_response,
            "consensus_score": final_consensus_score,
            "rounds_completed": round_num,
            "conversation_history": conversation_history
        }
    
    def _get_agent_execution_order(self, agents: Dict[str, BaseAgent]) -> List[BaseAgent]:
        """Get ordered list of agents for execution"""
        
        ordered_agents = []
        
        # Always start with planner
        for agent in agents.values():
            if agent.role == AgentRole.PLANNER:
                ordered_agents.append(agent)
                break
        
        # Then critic
        for agent in agents.values():
            if agent.role == AgentRole.CRITIC:
                ordered_agents.append(agent)
                break
        
        # Then additional agents
        for agent in agents.values():
            if agent.role == AgentRole.ADDITIONAL:
                ordered_agents.append(agent)
        
        return ordered_agents
    
    def _analyze_consensus(self, responses: List[AgentResponse]) -> Dict[str, Any]:
        """Analyze consensus level among agent responses"""
        
        if not responses:
            return {
                "level": ConsensusLevel.NONE,
                "score": 0.0,
                "unanimous": False,
                "details": "No responses to analyze"
            }
        
        # Calculate consensus metrics
        agreement_count = sum(1 for r in responses if r.agrees_with_proposal)
        total_responses = len(responses)
        consensus_scores = [r.consensus_score for r in responses]
        average_consensus = sum(consensus_scores) / len(consensus_scores)
        
        # Determine consensus level
        agreement_ratio = agreement_count / total_responses
        
        if agreement_ratio == 1.0 and average_consensus >= 0.8:
            level = ConsensusLevel.UNANIMOUS
            unanimous = True
        elif agreement_ratio >= 0.6:
            level = ConsensusLevel.MAJORITY
            unanimous = False
        elif agreement_ratio > 0.0:
            level = ConsensusLevel.PARTIAL
            unanimous = False
        else:
            level = ConsensusLevel.NONE
            unanimous = False
        
        return {
            "level": level,
            "score": average_consensus,
            "unanimous": unanimous,
            "agreement_ratio": agreement_ratio,
            "details": f"{agreement_count}/{total_responses} agents agree"
        }
    
    def _should_trigger_meta_block(
        self, 
        task_context: TaskContext, 
        round_context: ConversationRound,
        round_num: int
    ) -> bool:
        """Determine if Meta-Block should be triggered"""
        
        # Check maximum rounds
        if round_num >= task_context.max_rounds:
            return True
        
        # Check for persistent low consensus
        if len(task_context.conversation_rounds) >= self.meta_block_threshold:
            recent_rounds = task_context.conversation_rounds[-self.meta_block_threshold:]
            recent_scores = [r.consensus_score for r in recent_rounds]
            
            if all(score < 0.5 for score in recent_scores):
                return True
        
        # Check for timeout
        if task_context.started_at:
            elapsed = datetime.utcnow() - task_context.started_at
            timeout = timedelta(minutes=task_context.timeout_minutes)
            
            if elapsed > timeout:
                return True
        
        return False
    
    def _get_meta_block_reason(
        self, 
        task_context: TaskContext, 
        round_context: ConversationRound,
        round_num: int
    ) -> str:
        """Generate Meta-Block reason description"""
        
        reasons = []
        
        if round_num >= task_context.max_rounds:
            reasons.append(f"Maximum rounds ({task_context.max_rounds}) reached")
        
        if len(task_context.conversation_rounds) >= self.meta_block_threshold:
            recent_rounds = task_context.conversation_rounds[-self.meta_block_threshold:]
            recent_scores = [r.consensus_score for r in recent_rounds]
            
            if all(score < 0.5 for score in recent_scores):
                avg_score = sum(recent_scores) / len(recent_scores)
                reasons.append(f"Persistent low consensus (avg {avg_score:.2f}) over {len(recent_scores)} rounds")
        
        if task_context.started_at:
            elapsed = datetime.utcnow() - task_context.started_at
            timeout = timedelta(minutes=task_context.timeout_minutes)
            
            if elapsed > timeout:
                reasons.append(f"Task timeout ({task_context.timeout_minutes} minutes)")
        
        return "; ".join(reasons) if reasons else "Meta-Block triggered"
    
    def _synthesize_final_response(self, responses: List[AgentResponse]) -> str:
        """Synthesize final consensus response from agent responses"""
        
        if not responses:
            return "No consensus achieved - no responses available"
        
        # Find the highest confidence response or latest planner response
        planner_responses = [r for r in responses if r.role == AgentRole.PLANNER]
        
        if planner_responses:
            return planner_responses[-1].content
        else:
            # Fall back to highest confidence response
            best_response = max(responses, key=lambda r: r.confidence)
            return best_response.content
    
    async def _stream_response(self, task_id: str, response: AgentResponse):
        """Stream response to task subscribers"""
        
        if task_id in self.conversation_streams:
            try:
                await self.conversation_streams[task_id].put({
                    "type": "agent_response",
                    "task_id": task_id,
                    "agent_id": response.agent_id,
                    "role": response.role.value,
                    "content": response.content,
                    "consensus_score": response.consensus_score,
                    "timestamp": response.timestamp.isoformat()
                })
            except Exception as e:
                logger.error(f"Error streaming response for task {task_id}: {e}")
    
    async def stream_task_tokens(self, task_id: str) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream real-time tokens from task execution"""
        
        if task_id not in self.conversation_streams:
            raise ValueError(f"Task {task_id} not found or not active")
        
        stream = self.conversation_streams[task_id]
        
        try:
            while True:
                try:
                    # Wait for next token with timeout
                    token_data = await asyncio.wait_for(stream.get(), timeout=1.0)
                    yield token_data
                    
                except asyncio.TimeoutError:
                    # Check if task is still active
                    if task_id not in self.active_tasks:
                        break
                    
                    task_context = self.active_tasks[task_id]
                    if task_context.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.META_BLOCK]:
                        break
                    
                    # Send keepalive
                    yield {"type": "keepalive", "timestamp": datetime.utcnow().isoformat()}
                    
        except Exception as e:
            logger.error(f"Error streaming tokens for task {task_id}: {e}")
            yield {"type": "error", "message": str(e)}
    
    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get current status of a task"""
        
        if task_id not in self.active_tasks:
            return None
        
        task_context = self.active_tasks[task_id]
        agents = self.task_agents.get(task_id, {})
        
        return {
            "task_id": task_id,
            "status": task_context.status.value,
            "description": task_context.description,
            "current_round": task_context.current_round,
            "max_rounds": task_context.max_rounds,
            "consensus_score": task_context.consensus_score,
            "created_at": task_context.created_at.isoformat(),
            "started_at": task_context.started_at.isoformat() if task_context.started_at else None,
            "completed_at": task_context.completed_at.isoformat() if task_context.completed_at else None,
            "agents": [
                {
                    "agent_id": agent.agent_id,
                    "role": agent.role.value,
                    "status": agent.status.value,
                    "metrics": agent.get_performance_metrics()
                }
                for agent in agents.values()
            ]
        }
    
    async def stop_task(self, task_id: str) -> bool:
        """Stop a running task"""
        
        if task_id not in self.active_tasks:
            return False
        
        task_context = self.active_tasks[task_id]
        
        if task_context.status == TaskStatus.RUNNING:
            task_context.status = TaskStatus.STOPPED
            task_context.completed_at = datetime.utcnow()
            
            # Stop all agents for this task
            if task_id in self.task_agents:
                for agent in self.task_agents[task_id].values():
                    try:
                        await agent.reset_session()
                    except Exception as e:
                        logger.error(f"Error stopping agent {agent.agent_id}: {e}")
            
            logger.info(f"Stopped task: {task_id}")
            return True
        
        return False
    
    async def list_active_tasks(self) -> List[Dict[str, Any]]:
        """List all active tasks"""
        
        active_tasks = []
        
        for task_id, task_context in self.active_tasks.items():
            if task_context.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
                active_tasks.append({
                    "task_id": task_id,
                    "status": task_context.status.value,
                    "description": task_context.description[:100] + "..." if len(task_context.description) > 100 else task_context.description,
                    "current_round": task_context.current_round,
                    "max_rounds": task_context.max_rounds,
                    "agent_count": len(task_context.participating_agents),
                    "created_at": task_context.created_at.isoformat()
                })
        
        return active_tasks
    
    async def get_health_status(self) -> Dict[str, Any]:
        """Get orchestrator health status"""
        
        total_tasks = len(self.active_tasks)
        active_task_count = len([
            t for t in self.active_tasks.values() 
            if t.status in [TaskStatus.PENDING, TaskStatus.RUNNING]
        ])
        
        total_agents = sum(len(agents) for agents in self.task_agents.values())
        
        return {
            "status": "healthy",
            "total_tasks": total_tasks,
            "active_tasks": active_task_count,
            "total_agents": total_agents,
            "agent_factory_status": agent_factory.get_factory_status(),
            "uptime_seconds": (datetime.utcnow() - datetime.utcnow()).total_seconds(),  # Placeholder
            "memory_usage": "N/A"  # Could add actual memory monitoring
        }
    
    async def _setup_monitoring(self):
        """Setup monitoring and health checks"""
        # Could implement periodic health checks, cleanup, etc.
        pass
    
    async def cleanup(self):
        """Cleanup orchestrator resources"""
        
        logger.info("Cleaning up Agent Orchestrator")
        
        self.shutdown_flag = True
        
        # Stop all active tasks
        for task_id in list(self.active_tasks.keys()):
            await self.stop_task(task_id)
        
        # Shutdown all agents
        await agent_factory.shutdown_all_agents()
        
        # Clear data structures
        self.active_tasks.clear()
        self.task_agents.clear()
        self.conversation_streams.clear()
        
        logger.info("Agent Orchestrator cleanup completed")
    
    # Agent Creation System methods
    async def create_agent_profile(
        self,
        agent_id: str,
        name: str,
        role: AgentRole,
        model: str,
        prompt: str,
        capabilities: List[str] = None,
        team_id: str = None,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        max_retries: int = 3,
        timeout_seconds: int = 60,
        consensus_threshold: float = 0.8
    ) -> AgentProfile:
        """Create a new agent profile through the orchestrator"""
        
        # Create the request object
        request = AgentCreationRequest(
            name=name,
            role=role,
            model=model,
            prompt=prompt,
            capabilities=capabilities or [],
            team_id=team_id,
            temperature=temperature,
            max_tokens=max_tokens,
            max_retries=max_retries,
            timeout_seconds=timeout_seconds,
            consensus_threshold=consensus_threshold
        )
        
        # Create the agent profile through the factory
        profile = agent_factory.create_agent_profile(request)
        
        logger.info(f"Orchestrator created agent profile: {profile.name} ({profile.id})")
        
        return profile
    
    async def get_agent_profile(self, agent_id: str) -> Optional[AgentProfile]:
        """Get agent profile by ID"""
        return agent_factory.get_agent_profile(agent_id)
    
    async def update_agent_profile(self, agent_id: str, updates: Dict[str, Any]) -> Optional[AgentProfile]:
        """Update agent profile"""
        return agent_factory.update_agent_profile(agent_id, updates)
    
    async def delete_agent_profile(self, agent_id: str) -> bool:
        """Delete agent profile"""
        return agent_factory.delete_agent_profile(agent_id)
    
    async def list_agent_profiles(self) -> List[AgentProfile]:
        """List all agent profiles"""
        profiles = agent_factory.list_agent_profiles()
        return list(profiles.values())
    
    async def get_profiles_by_role(self, role: AgentRole) -> List[AgentProfile]:
        """Get agent profiles by role"""
        profiles = agent_factory.get_profiles_by_role(role)
        return list(profiles.values())
    
    async def get_profiles_by_team(self, team_id: str) -> List[AgentProfile]:
        """Get agent profiles by team"""
        profiles = agent_factory.get_profiles_by_team(team_id)
        return list(profiles.values())
    
    async def validate_agent_creation(self, request: AgentCreationRequest) -> Dict[str, Any]:
        """Validate agent creation request"""
        return agent_factory.validate_agent_creation_request(request)
    
    async def get_supported_models(self) -> Dict[str, List[str]]:
        """Get supported models by provider"""
        return agent_factory.SUPPORTED_MODELS
    
    async def get_supported_roles(self) -> List[str]:
        """Get supported agent roles"""
        return [role.value for role in agent_factory.AGENT_CLASSES.keys()]
    
    async def clone_agent_profile(self, source_agent_id: str, new_name: str = None) -> Optional[AgentProfile]:
        """Clone an existing agent profile"""
        return agent_factory.clone_agent_profile(source_agent_id, new_name)
    
    async def batch_clone_agents(self, agent_ids: List[str], name_prefix: str = None) -> List[AgentProfile]:
        """Clone multiple agent profiles"""
        return agent_factory.batch_clone_agents(agent_ids, name_prefix)
    
    async def create_agent_template(self, agent_id: str, template_name: str) -> Optional[Dict[str, Any]]:
        """Create a template from an existing agent profile"""
        return agent_factory.create_agent_template(agent_id, template_name)
    
    async def create_agent_from_template(self, template: Dict[str, Any], name: str) -> AgentProfile:
        """Create a new agent profile from a template"""
        return agent_factory.create_agent_from_template(template, name)
    
    # Team management methods
    async def create_team(
        self,
        team_id: str,
        name: str,
        description: str,
        leader_id: Optional[str] = None,
        initial_members: List[str] = None,
        objectives: List[str] = None,
        max_members: int = 10,
        communication_channel: str = "default"
    ) -> Team:
        """Create a new team"""
        
        # Create the team request
        request = TeamCreationRequest(
            name=name,
            description=description,
            leader_id=leader_id,
            initial_members=initial_members or [],
            objectives=objectives or [],
            max_members=max_members,
            communication_channel=communication_channel
        )
        
        # Validate the request
        validation = agent_factory.validate_team_creation_request(request)
        if not validation["valid"]:
            raise ValueError(f"Team creation validation failed: {validation['errors']}")
        
        # Create the team
        team = Team(
            id=team_id,
            name=name,
            description=description,
            leader_id=leader_id,
            max_members=max_members,
            objectives=objectives or [],
            communication_channel=communication_channel
        )
        
        # Add initial members
        for member_id in initial_members or []:
            role = TeamRole.LEADER if member_id == leader_id else TeamRole.MEMBER
            team.add_member(member_id, role)
        
        # Store the team
        self.teams[team_id] = team
        
        logger.info(f"Created team: {name} ({team_id}) with {len(team.members)} members")
        
        return team
    
    async def get_team(self, team_id: str) -> Optional[Team]:
        """Get team by ID"""
        return self.teams.get(team_id)
    
    async def list_teams(self) -> List[Team]:
        """List all teams"""
        return list(self.teams.values())
    
    async def update_team(self, team_id: str, updates: Dict[str, Any]) -> Optional[Team]:
        """Update team"""
        team = self.teams.get(team_id)
        if not team:
            return None
        
        # Update team fields
        for key, value in updates.items():
            if hasattr(team, key):
                setattr(team, key, value)
        
        team.updated_at = datetime.utcnow()
        
        # Validate the updated team
        validation = agent_factory.validate_team_configuration(team)
        if not validation["valid"]:
            logger.warning(f"Team {team_id} validation failed after update: {validation['errors']}")
        
        return team
    
    async def delete_team(self, team_id: str) -> bool:
        """Delete team"""
        if team_id not in self.teams:
            return False
        
        team = self.teams[team_id]
        
        # Remove team assignment from all agents
        for member in team.members:
            if member.agent_id in agent_factory.agent_profiles:
                agent_factory.agent_profiles[member.agent_id].team_id = None
        
        # Remove the team
        del self.teams[team_id]
        
        logger.info(f"Deleted team: {team_id}")
        
        return True
    
    async def add_team_member(self, team_id: str, agent_id: str, role: str = "member") -> bool:
        """Add member to team"""
        team = self.teams.get(team_id)
        if not team:
            return False
        
        # Convert string role to TeamRole enum
        try:
            team_role = TeamRole(role)
        except ValueError:
            logger.error(f"Invalid team role: {role}")
            return False
        
        # Validate the assignment
        validation = agent_factory.validate_team_member_assignment(team_id, agent_id, team_role)
        if not validation["valid"]:
            logger.error(f"Team member assignment validation failed: {validation['errors']}")
            return False
        
        # Add the member
        success = team.add_member(agent_id, team_role)
        
        if success:
            # Update agent profile with team assignment
            if agent_id in agent_factory.agent_profiles:
                agent_factory.agent_profiles[agent_id].team_id = team_id
            
            logger.info(f"Added agent {agent_id} to team {team_id} as {role}")
        
        return success
    
    async def remove_team_member(self, team_id: str, agent_id: str) -> bool:
        """Remove member from team"""
        team = self.teams.get(team_id)
        if not team:
            return False
        
        # Remove the member
        success = team.remove_member(agent_id)
        
        if success:
            # Update agent profile
            if agent_id in agent_factory.agent_profiles:
                agent_factory.agent_profiles[agent_id].team_id = None
            
            logger.info(f"Removed agent {agent_id} from team {team_id}")
        
        return success
    
    async def set_team_leader(self, team_id: str, agent_id: str) -> bool:
        """Set team leader"""
        team = self.teams.get(team_id)
        if not team:
            return False
        
        # Validate the assignment
        validation = agent_factory.validate_team_member_assignment(team_id, agent_id, TeamRole.LEADER)
        if not validation["valid"]:
            logger.error(f"Team leader assignment validation failed: {validation['errors']}")
            return False
        
        # Set the leader
        success = team.set_leader(agent_id)
        
        if success:
            logger.info(f"Set agent {agent_id} as leader of team {team_id}")
        
        return success
    
    async def select_team_leader(self, team_id: str, criteria: Dict[str, Any] = None) -> Optional[str]:
        """
        Select team leader based on criteria
        
        Args:
            team_id: Team ID
            criteria: Selection criteria (experience, role, performance, etc.)
            
        Returns:
            Agent ID of selected leader or None if no suitable candidate
        """
        team = self.teams.get(team_id)
        if not team:
            return None
        
        active_members = team.get_active_members()
        if not active_members:
            return None
        
        # Default criteria
        default_criteria = {
            "prefer_experience": True,
            "prefer_high_performance": True,
            "prefer_leadership_roles": True,
            "prefer_stable_agents": True
        }
        
        criteria = criteria or default_criteria
        
        # Score each member for leadership
        candidate_scores = []
        
        for member in active_members:
            agent_profile = agent_factory.agent_profiles.get(member.agent_id)
            if not agent_profile:
                continue
            
            score = 0
            
            # Experience score (based on tasks completed)
            if criteria.get("prefer_experience"):
                score += min(agent_profile.tasks_completed / 10, 5)  # Max 5 points
            
            # Performance score
            if criteria.get("prefer_high_performance"):
                score += agent_profile.success_rate * 3  # Max 3 points
            
            # Role suitability score
            if criteria.get("prefer_leadership_roles"):
                leadership_roles = [AgentRole.ORCHESTRATOR, AgentRole.PM, AgentRole.PLANNER]
                if agent_profile.role in leadership_roles:
                    score += 2
                elif agent_profile.role == AgentRole.CRITIC:
                    score += 1
            
            # Stability score (based on response time consistency)
            if criteria.get("prefer_stable_agents"):
                if agent_profile.avg_response_time < 5000:  # Under 5 seconds
                    score += 1
            
            candidate_scores.append((member.agent_id, score))
        
        # Sort by score (highest first)
        candidate_scores.sort(key=lambda x: x[1], reverse=True)
        
        # Select the highest scoring candidate
        if candidate_scores:
            selected_agent_id = candidate_scores[0][0]
            logger.info(f"Selected {selected_agent_id} as leader for team {team_id} (score: {candidate_scores[0][1]})")
            return selected_agent_id
        
        return None
    
    async def auto_assign_team_leader(self, team_id: str) -> bool:
        """
        Automatically assign team leader based on selection criteria
        
        Args:
            team_id: Team ID
            
        Returns:
            True if leader was assigned, False otherwise
        """
        selected_leader = await self.select_team_leader(team_id)
        if selected_leader:
            return await self.set_team_leader(team_id, selected_leader)
        
        return False
    
    async def rotate_team_leader(self, team_id: str) -> bool:
        """
        Rotate team leader to next best candidate
        
        Args:
            team_id: Team ID
            
        Returns:
            True if leader was rotated, False otherwise
        """
        team = self.teams.get(team_id)
        if not team:
            return False
        
        current_leader = team.get_leader()
        if not current_leader:
            return await self.auto_assign_team_leader(team_id)
        
        # Get all candidates except current leader
        active_members = team.get_active_members()
        candidates = [m for m in active_members if m.agent_id != current_leader.agent_id]
        
        if not candidates:
            return False
        
        # Score candidates (excluding current leader)
        candidate_scores = []
        for member in candidates:
            agent_profile = agent_factory.agent_profiles.get(member.agent_id)
            if not agent_profile:
                continue
            
            score = 0
            score += min(agent_profile.tasks_completed / 10, 5)
            score += agent_profile.success_rate * 3
            
            leadership_roles = [AgentRole.ORCHESTRATOR, AgentRole.PM, AgentRole.PLANNER]
            if agent_profile.role in leadership_roles:
                score += 2
            
            candidate_scores.append((member.agent_id, score))
        
        # Sort by score and select highest
        candidate_scores.sort(key=lambda x: x[1], reverse=True)
        
        if candidate_scores:
            new_leader = candidate_scores[0][0]
            success = await self.set_team_leader(team_id, new_leader)
            if success:
                logger.info(f"Rotated team {team_id} leadership from {current_leader.agent_id} to {new_leader}")
            return success
        
        return False
    
    async def set_team_objectives(self, team_id: str, objectives: List[str]) -> bool:
        """
        Set team objectives
        
        Args:
            team_id: Team ID
            objectives: List of objective strings
            
        Returns:
            True if objectives were set, False otherwise
        """
        team = self.teams.get(team_id)
        if not team:
            return False
        
        # Validate objectives
        if len(objectives) > 10:
            logger.warning(f"Team {team_id} has too many objectives ({len(objectives)}), limiting to 10")
            objectives = objectives[:10]
        
        # Validate each objective
        validated_objectives = []
        for obj in objectives:
            if len(obj.strip()) > 0 and len(obj) <= 500:
                validated_objectives.append(obj.strip())
            else:
                logger.warning(f"Invalid objective skipped: {obj}")
        
        team.objectives = validated_objectives
        team.updated_at = datetime.utcnow()
        
        logger.info(f"Set {len(validated_objectives)} objectives for team {team_id}")
        
        return True
    
    async def add_team_objective(self, team_id: str, objective: str) -> bool:
        """
        Add single objective to team
        
        Args:
            team_id: Team ID
            objective: Objective string
            
        Returns:
            True if objective was added, False otherwise
        """
        team = self.teams.get(team_id)
        if not team:
            return False
        
        if len(objective.strip()) == 0 or len(objective) > 500:
            return False
        
        if len(team.objectives) >= 10:
            logger.warning(f"Team {team_id} already has maximum objectives (10)")
            return False
        
        team.objectives.append(objective.strip())
        team.updated_at = datetime.utcnow()
        
        logger.info(f"Added objective to team {team_id}: {objective}")
        
        return True
    
    async def remove_team_objective(self, team_id: str, objective_index: int) -> bool:
        """
        Remove objective from team by index
        
        Args:
            team_id: Team ID
            objective_index: Index of objective to remove
            
        Returns:
            True if objective was removed, False otherwise
        """
        team = self.teams.get(team_id)
        if not team:
            return False
        
        if objective_index < 0 or objective_index >= len(team.objectives):
            return False
        
        removed_objective = team.objectives.pop(objective_index)
        team.updated_at = datetime.utcnow()
        
        logger.info(f"Removed objective from team {team_id}: {removed_objective}")
        
        return True
    
    async def update_team_objective(self, team_id: str, objective_index: int, new_objective: str) -> bool:
        """
        Update specific team objective
        
        Args:
            team_id: Team ID
            objective_index: Index of objective to update
            new_objective: New objective string
            
        Returns:
            True if objective was updated, False otherwise
        """
        team = self.teams.get(team_id)
        if not team:
            return False
        
        if objective_index < 0 or objective_index >= len(team.objectives):
            return False
        
        if len(new_objective.strip()) == 0 or len(new_objective) > 500:
            return False
        
        old_objective = team.objectives[objective_index]
        team.objectives[objective_index] = new_objective.strip()
        team.updated_at = datetime.utcnow()
        
        logger.info(f"Updated team {team_id} objective {objective_index}: '{old_objective}' -> '{new_objective}'")
        
        return True
    
    async def get_team_objectives(self, team_id: str) -> Optional[List[str]]:
        """
        Get team objectives
        
        Args:
            team_id: Team ID
            
        Returns:
            List of objectives or None if team not found
        """
        team = self.teams.get(team_id)
        if not team:
            return None
        
        return team.objectives.copy()
    
    async def set_team_current_task(self, team_id: str, task_description: str) -> bool:
        """
        Set current task for team
        
        Args:
            team_id: Team ID
            task_description: Current task description
            
        Returns:
            True if task was set, False otherwise
        """
        team = self.teams.get(team_id)
        if not team:
            return False
        
        if len(task_description.strip()) == 0 or len(task_description) > 1000:
            return False
        
        team.current_task = task_description.strip()
        team.updated_at = datetime.utcnow()
        
        logger.info(f"Set current task for team {team_id}: {task_description}")
        
        return True
    
    async def clear_team_current_task(self, team_id: str) -> bool:
        """
        Clear current task for team
        
        Args:
            team_id: Team ID
            
        Returns:
            True if task was cleared, False otherwise
        """
        team = self.teams.get(team_id)
        if not team:
            return False
        
        team.current_task = None
        team.updated_at = datetime.utcnow()
        
        logger.info(f"Cleared current task for team {team_id}")
        
        return True
    
    async def setup_team_communication_channel(self, team_id: str, channel_name: str) -> bool:
        """
        Setup communication channel for team
        
        Args:
            team_id: Team ID
            channel_name: Channel name
            
        Returns:
            True if channel was setup, False otherwise
        """
        team = self.teams.get(team_id)
        if not team:
            return False
        
        if len(channel_name.strip()) == 0 or len(channel_name) > 100:
            return False
        
        team.communication_channel = channel_name.strip()
        team.updated_at = datetime.utcnow()
        
        logger.info(f"Setup communication channel for team {team_id}: {channel_name}")
        
        return True
    
    async def send_team_message(self, team_id: str, sender_id: str, message: str) -> bool:
        """
        Send message to team communication channel
        
        Args:
            team_id: Team ID
            sender_id: Agent ID sending message
            message: Message content
            
        Returns:
            True if message was sent, False otherwise
        """
        team = self.teams.get(team_id)
        if not team:
            return False
        
        if len(message.strip()) == 0 or len(message) > 5000:
            return False
        
        # Create message entry
        message_entry = {
            "id": str(uuid.uuid4()),
            "sender_id": sender_id,
            "message": message.strip(),
            "timestamp": datetime.utcnow().isoformat(),
            "type": "message"
        }
        
        team.message_history.append(message_entry)
        team.updated_at = datetime.utcnow()
        
        logger.info(f"Team {team_id} message from {sender_id}: {message[:50]}...")
        
        return True
    
    async def send_team_notification(self, team_id: str, notification_type: str, message: str) -> bool:
        """
        Send notification to team communication channel
        
        Args:
            team_id: Team ID
            notification_type: Type of notification (system, alert, info, etc.)
            message: Notification message
            
        Returns:
            True if notification was sent, False otherwise
        """
        team = self.teams.get(team_id)
        if not team:
            return False
        
        if len(message.strip()) == 0 or len(message) > 1000:
            return False
        
        # Create notification entry
        notification_entry = {
            "id": str(uuid.uuid4()),
            "sender_id": "system",
            "message": message.strip(),
            "timestamp": datetime.utcnow().isoformat(),
            "type": notification_type,
            "is_notification": True
        }
        
        team.message_history.append(notification_entry)
        team.updated_at = datetime.utcnow()
        
        logger.info(f"Team {team_id} notification ({notification_type}): {message}")
        
        return True
    
    async def get_team_messages(self, team_id: str, limit: int = 50) -> Optional[List[Dict[str, Any]]]:
        """
        Get team communication history
        
        Args:
            team_id: Team ID
            limit: Maximum number of messages to return
            
        Returns:
            List of messages or None if team not found
        """
        team = self.teams.get(team_id)
        if not team:
            return None
        
        # Return most recent messages first
        return team.message_history[-limit:]
    
    async def clear_team_messages(self, team_id: str) -> bool:
        """
        Clear team communication history
        
        Args:
            team_id: Team ID
            
        Returns:
            True if messages were cleared, False otherwise
        """
        team = self.teams.get(team_id)
        if not team:
            return False
        
        team.message_history.clear()
        team.updated_at = datetime.utcnow()
        
        logger.info(f"Cleared message history for team {team_id}")
        
        return True
    
    async def broadcast_to_team(self, team_id: str, message: str, sender_id: str = "system") -> bool:
        """
        Broadcast message to all team members
        
        Args:
            team_id: Team ID
            message: Message to broadcast
            sender_id: ID of sender (defaults to system)
            
        Returns:
            True if broadcast was sent, False otherwise
        """
        team = self.teams.get(team_id)
        if not team:
            return False
        
        # Send message to team channel
        message_sent = await self.send_team_message(team_id, sender_id, message)
        
        if message_sent:
            # Also send notification to all team members
            active_members = team.get_active_members()
            for member in active_members:
                # Here you would integrate with individual agent notification systems
                logger.info(f"Notifying team member {member.agent_id} of broadcast: {message[:50]}...")
        
        return message_sent
    
    async def update_team_performance(self, team_id: str, task_completed: bool, completion_time: float = None) -> bool:
        """
        Update team performance metrics
        
        Args:
            team_id: Team ID
            task_completed: Whether task was completed successfully
            completion_time: Time taken to complete task (in seconds)
            
        Returns:
            True if performance was updated, False otherwise
        """
        team = self.teams.get(team_id)
        if not team:
            return False
        
        if task_completed:
            team.tasks_completed += 1
            
            # Update success rate
            total_tasks = team.tasks_completed + (1 if not task_completed else 0)
            team.success_rate = team.tasks_completed / total_tasks
            
            # Update average completion time
            if completion_time is not None:
                if team.avg_completion_time == 0:
                    team.avg_completion_time = completion_time
                else:
                    # Rolling average
                    team.avg_completion_time = (team.avg_completion_time * (team.tasks_completed - 1) + completion_time) / team.tasks_completed
        
        team.updated_at = datetime.utcnow()
        
        logger.info(f"Updated team {team_id} performance: {team.tasks_completed} tasks, {team.success_rate:.2f} success rate")
        
        return True
    
    async def get_team_performance_metrics(self, team_id: str) -> Optional[Dict[str, Any]]:
        """
        Get team performance metrics
        
        Args:
            team_id: Team ID
            
        Returns:
            Performance metrics dict or None if team not found
        """
        team = self.teams.get(team_id)
        if not team:
            return None
        
        active_members = team.get_active_members()
        
        # Calculate individual member performance
        member_performance = []
        for member in active_members:
            if member.agent_id in agent_factory.agent_profiles:
                profile = agent_factory.agent_profiles[member.agent_id]
                member_performance.append({
                    "agent_id": member.agent_id,
                    "role": profile.role.value,
                    "tasks_completed": profile.tasks_completed,
                    "success_rate": profile.success_rate,
                    "avg_response_time": profile.avg_response_time
                })
        
        # Calculate team-level metrics
        team_metrics = {
            "team_id": team_id,
            "team_name": team.name,
            "tasks_completed": team.tasks_completed,
            "success_rate": team.success_rate,
            "avg_completion_time": team.avg_completion_time,
            "member_count": len(active_members),
            "created_at": team.created_at.isoformat(),
            "updated_at": team.updated_at.isoformat(),
            "members": member_performance
        }
        
        return team_metrics
    
    async def get_team_performance_analytics(self, team_id: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed team performance analytics
        
        Args:
            team_id: Team ID
            
        Returns:
            Analytics dict or None if team not found
        """
        team = self.teams.get(team_id)
        if not team:
            return None
        
        metrics = await self.get_team_performance_metrics(team_id)
        if not metrics:
            return None
        
        # Calculate additional analytics
        active_members = team.get_active_members()
        
        # Role distribution
        role_distribution = {}
        for member in active_members:
            if member.agent_id in agent_factory.agent_profiles:
                profile = agent_factory.agent_profiles[member.agent_id]
                role = profile.role.value
                role_distribution[role] = role_distribution.get(role, 0) + 1
        
        # Performance trends (mock data for now)
        performance_trends = {
            "tasks_per_day": team.tasks_completed / max(1, (datetime.utcnow() - team.created_at).days),
            "efficiency_score": team.success_rate * (1 / max(1, team.avg_completion_time / 3600)),  # Tasks per hour
            "collaboration_score": min(len(active_members) / 5, 1.0)  # Optimal team size is 5
        }
        
        # Team health indicators
        health_indicators = {
            "member_stability": len([m for m in team.members if m.is_active]) / len(team.members) if team.members else 0,
            "communication_activity": len(team.message_history) / max(1, (datetime.utcnow() - team.created_at).days),
            "leadership_stability": 1.0 if team.get_leader() else 0.0
        }
        
        analytics = {
            **metrics,
            "role_distribution": role_distribution,
            "performance_trends": performance_trends,
            "health_indicators": health_indicators,
            "recommendations": await self._generate_team_recommendations(team)
        }
        
        return analytics
    
    async def _generate_team_recommendations(self, team: Team) -> List[str]:
        """
        Generate recommendations for team improvement
        
        Args:
            team: Team object
            
        Returns:
            List of recommendation strings
        """
        recommendations = []
        
        active_members = team.get_active_members()
        
        # Check team size
        if len(active_members) < 2:
            recommendations.append("Consider adding more team members for better collaboration")
        elif len(active_members) > 10:
            recommendations.append("Large team size may impact coordination - consider splitting into smaller teams")
        
        # Check leadership
        if not team.get_leader():
            recommendations.append("Assign a team leader to improve coordination")
        
        # Check role diversity
        roles = set()
        for member in active_members:
            if member.agent_id in agent_factory.agent_profiles:
                roles.add(agent_factory.agent_profiles[member.agent_id].role)
        
        if len(roles) < 2 and len(active_members) >= 3:
            recommendations.append("Increase role diversity for better task coverage")
        
        # Check performance
        if team.success_rate < 0.7:
            recommendations.append("Review team processes to improve success rate")
        
        if team.avg_completion_time > 7200:  # 2 hours
            recommendations.append("Consider optimizing workflows to reduce completion time")
        
        # Check communication
        if len(team.message_history) < team.tasks_completed:
            recommendations.append("Encourage more team communication for better coordination")
        
        # Check objectives
        if not team.objectives:
            recommendations.append("Set clear team objectives to improve focus")
        
        return recommendations
    
    async def compare_team_performance(self, team_ids: List[str]) -> Dict[str, Any]:
        """
        Compare performance across multiple teams
        
        Args:
            team_ids: List of team IDs to compare
            
        Returns:
            Comparison results
        """
        team_metrics = []
        
        for team_id in team_ids:
            metrics = await self.get_team_performance_metrics(team_id)
            if metrics:
                team_metrics.append(metrics)
        
        if not team_metrics:
            return {}
        
        # Calculate comparative metrics
        comparison = {
            "teams": team_metrics,
            "benchmarks": {
                "highest_success_rate": max(t["success_rate"] for t in team_metrics),
                "fastest_completion": min(t["avg_completion_time"] for t in team_metrics if t["avg_completion_time"] > 0),
                "most_tasks_completed": max(t["tasks_completed"] for t in team_metrics),
                "largest_team": max(t["member_count"] for t in team_metrics)
            },
            "rankings": {
                "by_success_rate": sorted(team_metrics, key=lambda x: x["success_rate"], reverse=True),
                "by_task_count": sorted(team_metrics, key=lambda x: x["tasks_completed"], reverse=True),
                "by_completion_time": sorted(team_metrics, key=lambda x: x["avg_completion_time"] if x["avg_completion_time"] > 0 else float('inf'))
            }
        }
        
        return comparison


# Additional helper methods for the routes
    async def create_agent(
        self,
        agent_id: str,
        session_id: str,
        role: AgentRole,
        task_description: str,
        context_files: List[str] = None
    ) -> Dict[str, Any]:
        """Create individual agent (for direct API usage)"""
        
        agent = agent_factory.create_agent(
            role=role,
            task_description=task_description,
            agent_id=agent_id,
            session_id=session_id,
            context_files=context_files or []
        )
        
        return {
            "agent_id": agent_id,
            "session_id": session_id,
            "role": role.value,
            "status": agent.status.value,
            "created_at": datetime.utcnow().isoformat()
        }
    
    async def start_conversation(
        self,
        session_id: str,
        message_id: str,
        message: str,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Start conversation with individual agent"""
        
        agent = agent_factory.get_agent_by_session(session_id)
        if not agent:
            raise ValueError(f"Agent session {session_id} not found")
        
        # Create minimal task context for individual conversation
        task_context = TaskContext(
            task_id=f"conv_{message_id}",
            description=message
        )
        
        response = await agent.process_task(task_context, [], [])
        
        return {
            "session_id": session_id,
            "message_id": message_id,
            "status": "completed",
            "response": response.content,
            "token_count": response.token_count
        }
    
    async def get_agent_status(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get agent status by session ID"""
        
        agent = agent_factory.get_agent_by_session(session_id)
        if not agent:
            return None
        
        return {
            "agent_id": agent.agent_id,
            "session_id": session_id,
            "role": agent.role.value,
            "status": agent.status.value,
            "metrics": agent.get_performance_metrics()
        }
    
    async def delete_agent(self, session_id: str) -> bool:
        """Delete agent by session ID"""
        
        agent = agent_factory.get_agent_by_session(session_id)
        if not agent:
            return False
        
        return await agent_factory.shutdown_agent(agent.agent_id)
    
    async def get_consensus_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get consensus status for a task"""
        
        if task_id not in self.active_tasks:
            return None
        
        task_context = self.active_tasks[task_id]
        
        if not task_context.conversation_rounds:
            return {
                "task_id": task_id,
                "consensus_level": "none",
                "consensus_score": 0.0,
                "rounds_completed": 0,
                "details": "No conversation rounds completed yet"
            }
        
        latest_round = task_context.conversation_rounds[-1]
        
        return {
            "task_id": task_id,
            "consensus_level": latest_round.consensus_level.value,
            "consensus_score": latest_round.consensus_score,
            "rounds_completed": len(task_context.conversation_rounds),
            "current_round": task_context.current_round,
            "max_rounds": task_context.max_rounds,
            "is_consensus_reached": task_context.is_consensus_reached,
            "should_meta_block": task_context.should_meta_block,
            "details": f"Round {latest_round.round_number}: {latest_round.consensus_level.value}"
        }
    
    async def list_active_agents(self) -> List[Dict[str, Any]]:
        """List all active agents across all tasks"""
        
        active_agents = []
        
        for task_id, agents in self.task_agents.items():
            task_context = self.active_tasks.get(task_id)
            
            for agent in agents.values():
                active_agents.append({
                    "agent_id": agent.agent_id,
                    "session_id": agent.session_id,
                    "role": agent.role.value,
                    "status": agent.status.value,
                    "task_id": task_id,
                    "task_status": task_context.status.value if task_context else "unknown",
                    "metrics": agent.get_performance_metrics()
                })
        
        return active_agents