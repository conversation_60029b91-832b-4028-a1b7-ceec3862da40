"""
LLM Client for Metamorphic Reactor
Unified interface for multiple LLM providers with API key rotation and safety integration
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional, AsyncGenerator, <PERSON><PERSON>
from datetime import datetime
import openai
import anthropic
import google.generativeai as genai
from dataclasses import dataclass

from .api_key_manager import api_key_manager, LLMProvider
from .rate_limiter import rate_limiter, LimitScope
from .safety_guardian import safety_guardian

logger = logging.getLogger(__name__)

@dataclass
class LLMRequest:
    """Standardized LLM request"""
    prompt: str
    model: Optional[str] = None
    max_tokens: int = 2000
    temperature: float = 0.7
    stream: bool = False
    context: Dict[str, Any] = None

@dataclass  
class LLMResponse:
    """Standardized LLM response"""
    content: str
    provider: LLMProvider
    model: str
    token_count: int
    cost_usd: float
    response_time_ms: int
    success: bool
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = None

class LLMClient:
    """
    Unified LLM client with automatic provider selection and safety integration
    
    Features:
    - Automatic API key rotation and load balancing
    - Built-in rate limiting and quota management
    - Safety checks on prompts and responses
    - Cost tracking and monitoring
    - Support for streaming responses
    - Fallback to alternative providers
    """
    
    def __init__(self):
        self.provider_clients: Dict[LLMProvider, Any] = {}
        self._initialized = False
    
    async def initialize(self):
        """Initialize LLM clients"""
        if self._initialized:
            return
        
        await api_key_manager.initialize()
        self._initialized = True
        logger.info("LLM Client initialized successfully")
    
    async def generate_response(
        self,
        request: LLMRequest,
        user_id: Optional[str] = None,
        task_id: Optional[str] = None,
        agent_id: Optional[str] = None,
        preferred_provider: Optional[LLMProvider] = None
    ) -> LLMResponse:
        """
        Generate LLM response with automatic provider selection
        
        Args:
            request: LLM request parameters
            user_id: User identifier for rate limiting
            task_id: Task identifier  
            agent_id: Agent identifier
            preferred_provider: Preferred LLM provider
            
        Returns:
            LLMResponse: Standardized response
        """
        await self.initialize()
        
        start_time = time.time()
        
        # Safety check on prompt
        safety_result = await safety_guardian.check_content_safety(
            content=request.prompt,
            user_id=user_id,
            task_id=task_id,
            agent_id=agent_id,
            context={"operation": "llm_request"}
        )
        
        if not safety_result.allowed:
            return LLMResponse(
                content="",
                provider=preferred_provider or LLMProvider.OPENAI,
                model=request.model or "unknown",
                token_count=0,
                cost_usd=0.0,
                response_time_ms=int((time.time() - start_time) * 1000),
                success=False,
                error_message=f"Request blocked by safety guardian: {safety_result.overall_level.value}"
            )
        
        # Use filtered content if PII was masked
        prompt_to_use = safety_result.filtered_content or request.prompt
        
        # Rate limiting check
        estimated_tokens = len(prompt_to_use.split()) * 1.3  # Rough estimate
        
        rate_check = await rate_limiter.check_rate_limit(
            scope=LimitScope.USER if user_id else LimitScope.GLOBAL,
            scope_id=user_id or "system",
            request_tokens=int(estimated_tokens),
            context={"agent_id": agent_id, "task_id": task_id}
        )
        
        if not rate_check.allowed:
            return LLMResponse(
                content="",
                provider=preferred_provider or LLMProvider.OPENAI,
                model=request.model or "unknown",
                token_count=0,
                cost_usd=0.0,
                response_time_ms=int((time.time() - start_time) * 1000),
                success=False,
                error_message=f"Rate limit exceeded: {rate_check.limit_hit.limit_type.value}"
            )
        
        # Try providers in order of preference
        providers_to_try = self._get_provider_order(preferred_provider)
        
        for provider in providers_to_try:
            try:
                response = await self._call_provider(
                    provider, request, prompt_to_use, user_id, task_id, agent_id
                )
                
                if response.success:
                    # Record usage
                    await api_key_manager.record_usage(
                        key_id=response.metadata.get("key_id", "unknown"),
                        provider=provider,
                        model_name=response.model,
                        tokens_used=response.token_count,
                        response_time_ms=response.response_time_ms,
                        success=True
                    )
                    
                    # Safety check on response
                    response_safety = await safety_guardian.check_content_safety(
                        content=response.content,
                        user_id=user_id,
                        task_id=task_id,
                        agent_id=agent_id,
                        context={"operation": "llm_response"}
                    )
                    
                    if not response_safety.allowed:
                        response.content = "[Response filtered by safety guardian]"
                        response.metadata["safety_filtered"] = True
                    
                    return response
                
            except Exception as e:
                logger.warning(f"Provider {provider.value} failed: {e}")
                continue
        
        # All providers failed
        return LLMResponse(
            content="",
            provider=preferred_provider or LLMProvider.OPENAI,
            model=request.model or "unknown",
            token_count=0,
            cost_usd=0.0,
            response_time_ms=int((time.time() - start_time) * 1000),
            success=False,
            error_message="All LLM providers failed"
        )
    
    def _get_provider_order(self, preferred: Optional[LLMProvider]) -> List[LLMProvider]:
        """Get provider order with preference"""
        
        all_providers = list(LLMProvider)
        
        if preferred and preferred in all_providers:
            # Put preferred first, others in default order
            order = [preferred] + [p for p in all_providers if p != preferred]
        else:
            # Default order: OpenAI, Claude, Gemini
            order = [LLMProvider.OPENAI, LLMProvider.ANTHROPIC, LLMProvider.GOOGLE]
        
        return order
    
    async def _call_provider(
        self,
        provider: LLMProvider,
        request: LLMRequest,
        prompt: str,
        user_id: Optional[str],
        task_id: Optional[str],
        agent_id: Optional[str]
    ) -> LLMResponse:
        """Call specific LLM provider"""
        
        start_time = time.time()
        
        # Get API key
        api_key, key_id = await api_key_manager.get_api_key(provider, request.model)
        
        if not api_key:
            raise Exception(f"No available API key for {provider.value}")
        
        try:
            if provider == LLMProvider.OPENAI:
                response = await self._call_openai(api_key, request, prompt)
            elif provider == LLMProvider.ANTHROPIC:
                response = await self._call_anthropic(api_key, request, prompt)
            elif provider == LLMProvider.GOOGLE:
                response = await self._call_google(api_key, request, prompt)
            else:
                raise Exception(f"Unsupported provider: {provider.value}")
            
            response.metadata["key_id"] = key_id
            response.response_time_ms = int((time.time() - start_time) * 1000)
            
            return response
            
        except Exception as e:
            response_time = int((time.time() - start_time) * 1000)
            
            # Record failed usage
            await api_key_manager.record_usage(
                key_id=key_id,
                provider=provider,
                model_name=request.model or "unknown",
                tokens_used=0,
                response_time_ms=response_time,
                success=False,
                error_message=str(e)
            )
            
            raise e
    
    async def _call_openai(self, api_key: str, request: LLMRequest, prompt: str) -> LLMResponse:
        """Call OpenAI API"""
        
        client = openai.AsyncOpenAI(api_key=api_key)
        
        model = request.model or "gpt-3.5-turbo"
        
        response = await client.chat.completions.create(
            model=model,
            messages=[{"role": "user", "content": prompt}],
            max_tokens=request.max_tokens,
            temperature=request.temperature,
            stream=request.stream
        )
        
        if request.stream:
            # Handle streaming response
            content = ""
            async for chunk in response:
                if chunk.choices[0].delta.content:
                    content += chunk.choices[0].delta.content
        else:
            content = response.choices[0].message.content
        
        # Estimate token count (rough approximation)
        token_count = len(content.split()) + len(prompt.split())
        
        # Calculate cost
        cost_usd = api_key_manager._calculate_cost(LLMProvider.OPENAI, model, token_count)
        
        return LLMResponse(
            content=content,
            provider=LLMProvider.OPENAI,
            model=model,
            token_count=token_count,
            cost_usd=cost_usd,
            response_time_ms=0,  # Will be set by caller
            success=True,
            metadata={"usage": response.usage.dict() if hasattr(response, 'usage') else {}}
        )
    
    async def _call_anthropic(self, api_key: str, request: LLMRequest, prompt: str) -> LLMResponse:
        """Call Anthropic Claude API"""
        
        client = anthropic.AsyncAnthropic(api_key=api_key)
        
        model = request.model or "claude-3-haiku-20240307"
        
        response = await client.messages.create(
            model=model,
            max_tokens=request.max_tokens,
            temperature=request.temperature,
            messages=[{"role": "user", "content": prompt}]
        )
        
        content = response.content[0].text if response.content else ""
        
        # Estimate token count
        token_count = len(content.split()) + len(prompt.split())
        
        # Calculate cost
        cost_usd = api_key_manager._calculate_cost(LLMProvider.ANTHROPIC, model, token_count)
        
        return LLMResponse(
            content=content,
            provider=LLMProvider.ANTHROPIC,
            model=model,
            token_count=token_count,
            cost_usd=cost_usd,
            response_time_ms=0,
            success=True,
            metadata={"usage": {"input_tokens": response.usage.input_tokens, "output_tokens": response.usage.output_tokens}}
        )
    
    async def _call_google(self, api_key: str, request: LLMRequest, prompt: str) -> LLMResponse:
        """Call Google Gemini API"""
        
        genai.configure(api_key=api_key)
        
        model = request.model or "gemini-pro"
        
        # Configure generation parameters
        generation_config = genai.GenerationConfig(
            max_output_tokens=request.max_tokens,
            temperature=request.temperature,
        )
        
        model_instance = genai.GenerativeModel(model)
        response = await model_instance.generate_content_async(
            prompt,
            generation_config=generation_config
        )
        
        content = response.text if response.text else ""
        
        # Estimate token count  
        token_count = len(content.split()) + len(prompt.split())
        
        # Calculate cost
        cost_usd = api_key_manager._calculate_cost(LLMProvider.GOOGLE, model, token_count)
        
        return LLMResponse(
            content=content,
            provider=LLMProvider.GOOGLE,
            model=model,
            token_count=token_count,
            cost_usd=cost_usd,
            response_time_ms=0,
            success=True,
            metadata={"usage": {"prompt_token_count": response.usage_metadata.prompt_token_count if hasattr(response, 'usage_metadata') else 0}}
        )
    
    async def stream_response(
        self,
        request: LLMRequest,
        user_id: Optional[str] = None,
        task_id: Optional[str] = None,
        agent_id: Optional[str] = None,
        preferred_provider: Optional[LLMProvider] = None
    ) -> AsyncGenerator[str, None]:
        """Stream LLM response in real-time"""
        
        # For now, generate full response and stream it token by token
        # In production, implement proper streaming for each provider
        
        response = await self.generate_response(
            request, user_id, task_id, agent_id, preferred_provider
        )
        
        if response.success:
            tokens = response.content.split()
            for token in tokens:
                yield token + " "
                await asyncio.sleep(0.05)  # Simulate streaming delay
        else:
            yield f"[Error: {response.error_message}]"
    
    async def test_providers(self) -> Dict[str, Any]:
        """Test all available providers"""
        
        test_results = {}
        test_prompt = "Hello, please respond with 'Test successful' if you can understand this message."
        
        for provider in LLMProvider:
            try:
                request = LLMRequest(
                    prompt=test_prompt,
                    max_tokens=50,
                    temperature=0.1
                )
                
                response = await self.generate_response(
                    request, 
                    preferred_provider=provider
                )
                
                test_results[provider.value] = {
                    "success": response.success,
                    "response_time_ms": response.response_time_ms,
                    "cost_usd": response.cost_usd,
                    "error": response.error_message
                }
                
            except Exception as e:
                test_results[provider.value] = {
                    "success": False,
                    "error": str(e)
                }
        
        return test_results


# Global LLM client instance
llm_client = LLMClient()