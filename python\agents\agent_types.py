"""
Agent type definitions and enums for Metamorphic Reactor
Defines roles, statuses, and common interfaces for all agent types
"""

from enum import Enum
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field
from datetime import datetime

class AgentRole(str, Enum):
    """Agent role definitions"""
    PLANNER = "planner"
    CRITIC = "critic" 
    ADDITIONAL = "additional"
    CODER = "coder"
    DEBUGGER = "debugger"
    PM = "pm"
    UX = "ux"
    QA = "qa"
    ORCHESTRATOR = "orchestrator"

class TaskStatus(str, Enum):
    """Task execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    STOPPED = "stopped"
    META_BLOCK = "meta_block"

class AgentStatus(str, Enum):
    """Individual agent status"""
    IDLE = "idle"
    THINKING = "thinking"
    RESPONDING = "responding"
    WAITING = "waiting"
    ERROR = "error"
    STOPPED = "stopped"

class ConsensusLevel(str, Enum):
    """Consensus agreement levels"""
    NONE = "none"           # 0% agreement
    PARTIAL = "partial"     # 1-50% agreement
    MAJORITY = "majority"   # 51-99% agreement
    UNANIMOUS = "unanimous" # 100% agreement

class TeamStatus(str, Enum):
    """Team status definitions"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    DISBANDED = "disbanded"
    PAUSED = "paused"

class TeamRole(str, Enum):
    """Team member roles"""
    LEADER = "leader"
    MEMBER = "member"
    OBSERVER = "observer"

class AgentConfig(BaseModel):
    """Configuration for agent instances"""
    role: AgentRole
    agent_id: str = Field(..., min_length=1)
    session_id: str = Field(..., min_length=1)
    
    # LLM Configuration
    model_name: str = Field(default="gpt-3.5-turbo")
    api_key: Optional[str] = None
    temperature: float = Field(default=0.7, ge=0.0, le=2.0)
    max_tokens: int = Field(default=2000, ge=1, le=8000)
    
    # Behavior Configuration
    max_retries: int = Field(default=3, ge=0, le=10)
    timeout_seconds: int = Field(default=60, ge=1, le=300)
    consensus_threshold: float = Field(default=0.8, ge=0.0, le=1.0)
    
    # Context Configuration
    task_description: str = Field(..., min_length=1)
    context_files: List[str] = Field(default_factory=list)
    conversation_history: List[Dict[str, Any]] = Field(default_factory=list)

class AgentResponse(BaseModel):
    """Standard agent response format"""
    agent_id: str
    session_id: str
    role: AgentRole
    message_id: str
    
    # Response content
    content: str
    reasoning: Optional[str] = None
    confidence: float = Field(ge=0.0, le=1.0)
    
    # Consensus data
    agrees_with_proposal: bool
    consensus_score: float = Field(ge=0.0, le=1.0)
    suggested_changes: List[str] = Field(default_factory=list)
    
    # Metadata
    token_count: int = Field(ge=0)
    response_time_ms: int = Field(ge=0)
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    # Quality metrics
    quality_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    complexity_level: Optional[str] = None

class ConversationRound(BaseModel):
    """A single round of multi-agent conversation"""
    round_number: int = Field(ge=1)
    task_id: str
    
    # Round data
    responses: List[AgentResponse] = Field(default_factory=list)
    consensus_level: ConsensusLevel = ConsensusLevel.NONE
    consensus_score: float = Field(default=0.0, ge=0.0, le=1.0)
    
    # Round metadata
    started_at: datetime = Field(default_factory=datetime.utcnow)
    completed_at: Optional[datetime] = None
    duration_ms: Optional[int] = None
    
    # Round status
    is_complete: bool = False
    requires_meta_block: bool = False
    meta_block_reason: Optional[str] = None

class TaskContext(BaseModel):
    """Complete task context and state"""
    task_id: str = Field(..., min_length=1)
    description: str = Field(..., min_length=1)
    
    # Task configuration
    max_rounds: int = Field(default=10, ge=1, le=50)
    timeout_minutes: int = Field(default=15, ge=1, le=60)
    require_consensus: bool = Field(default=True)
    target_consensus: float = Field(default=1.0, ge=0.5, le=1.0)
    
    # Task state
    status: TaskStatus = TaskStatus.PENDING
    current_round: int = Field(default=0, ge=0)
    conversation_rounds: List[ConversationRound] = Field(default_factory=list)
    
    # Context data
    context_files: List[str] = Field(default_factory=list)
    file_contents: Dict[str, str] = Field(default_factory=dict)
    
    # Participants
    participating_agents: List[str] = Field(default_factory=list)
    agent_configs: Dict[str, AgentConfig] = Field(default_factory=dict)
    
    # Results
    final_consensus: Optional[str] = None
    consensus_score: float = Field(default=0.0, ge=0.0, le=1.0)
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    @property
    def is_consensus_reached(self) -> bool:
        """Check if unanimous consensus has been reached"""
        return self.consensus_score >= self.target_consensus
    
    @property
    def should_meta_block(self) -> bool:
        """Check if Meta-Block escalation is needed"""
        if self.current_round >= self.max_rounds:
            return True
        
        # Check for stagnation (no progress in last 3 rounds)
        if len(self.conversation_rounds) >= 3:
            recent_scores = [r.consensus_score for r in self.conversation_rounds[-3:]]
            if all(score < 0.6 for score in recent_scores):
                return True
        
        return False

class AgentCreationRequest(BaseModel):
    """Request model for creating new agents"""
    name: str = Field(..., min_length=1, max_length=100)
    role: AgentRole
    model: str = Field(..., min_length=1)
    prompt: str = Field(..., min_length=1, max_length=10000)
    capabilities: List[str] = Field(default_factory=list)
    team_id: Optional[str] = None
    
    # LLM Configuration
    temperature: float = Field(default=0.7, ge=0.0, le=2.0)
    max_tokens: int = Field(default=2000, ge=1, le=8000)
    
    # Behavior Configuration
    max_retries: int = Field(default=3, ge=0, le=10)
    timeout_seconds: int = Field(default=60, ge=1, le=300)
    consensus_threshold: float = Field(default=0.8, ge=0.0, le=1.0)

class AgentProfile(BaseModel):
    """Complete agent profile with metadata"""
    id: str = Field(..., min_length=1)
    name: str = Field(..., min_length=1, max_length=100)
    role: AgentRole
    model: str = Field(..., min_length=1)
    prompt: str = Field(..., min_length=1, max_length=10000)
    capabilities: List[str] = Field(default_factory=list)
    team_id: Optional[str] = None
    status: AgentStatus = AgentStatus.IDLE
    
    # LLM Configuration
    temperature: float = Field(default=0.7, ge=0.0, le=2.0)
    max_tokens: int = Field(default=2000, ge=1, le=8000)
    
    # Behavior Configuration
    max_retries: int = Field(default=3, ge=0, le=10)
    timeout_seconds: int = Field(default=60, ge=1, le=300)
    consensus_threshold: float = Field(default=0.8, ge=0.0, le=1.0)
    
    # Performance Metrics
    success_rate: float = Field(default=0.0, ge=0.0, le=1.0)
    avg_response_time: float = Field(default=0.0, ge=0.0)
    tasks_completed: int = Field(default=0, ge=0)
    
    # Memory
    memory: List[Dict[str, Any]] = Field(default_factory=list)
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

class TeamMember(BaseModel):
    """Team member definition"""
    agent_id: str = Field(..., min_length=1)
    role: TeamRole = TeamRole.MEMBER
    joined_at: datetime = Field(default_factory=datetime.utcnow)
    is_active: bool = True

class Team(BaseModel):
    """Team interface for multi-agent collaboration"""
    id: str = Field(..., min_length=1)
    name: str = Field(..., min_length=1, max_length=100)
    description: str = Field(..., max_length=1000)
    
    # Team configuration
    status: TeamStatus = TeamStatus.ACTIVE
    leader_id: Optional[str] = None
    max_members: int = Field(default=10, ge=1, le=50)
    
    # Team members
    members: List[TeamMember] = Field(default_factory=list)
    
    # Team objectives
    objectives: List[str] = Field(default_factory=list)
    current_task: Optional[str] = None
    
    # Communication
    communication_channel: str = Field(..., min_length=1)
    message_history: List[Dict[str, Any]] = Field(default_factory=list)
    
    # Performance tracking
    tasks_completed: int = Field(default=0, ge=0)
    success_rate: float = Field(default=0.0, ge=0.0, le=1.0)
    avg_completion_time: float = Field(default=0.0, ge=0.0)
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    def get_active_members(self) -> List[TeamMember]:
        """Get list of active team members"""
        return [member for member in self.members if member.is_active]
    
    def get_leader(self) -> Optional[TeamMember]:
        """Get team leader member"""
        if not self.leader_id:
            return None
        return next((member for member in self.members if member.agent_id == self.leader_id), None)
    
    def add_member(self, agent_id: str, role: TeamRole = TeamRole.MEMBER) -> bool:
        """Add a new member to the team"""
        if len(self.get_active_members()) >= self.max_members:
            return False
        
        # Check if agent is already a member
        existing_member = next((member for member in self.members if member.agent_id == agent_id), None)
        if existing_member:
            existing_member.is_active = True
            return True
        
        new_member = TeamMember(agent_id=agent_id, role=role)
        self.members.append(new_member)
        self.updated_at = datetime.utcnow()
        return True
    
    def remove_member(self, agent_id: str) -> bool:
        """Remove a member from the team"""
        member = next((member for member in self.members if member.agent_id == agent_id), None)
        if member:
            member.is_active = False
            self.updated_at = datetime.utcnow()
            # If removing leader, clear leader_id
            if self.leader_id == agent_id:
                self.leader_id = None
            return True
        return False
    
    def set_leader(self, agent_id: str) -> bool:
        """Set team leader"""
        member = next((member for member in self.members if member.agent_id == agent_id and member.is_active), None)
        if member:
            # Update previous leader to member role
            if self.leader_id:
                prev_leader = next((m for m in self.members if m.agent_id == self.leader_id), None)
                if prev_leader:
                    prev_leader.role = TeamRole.MEMBER
            
            # Set new leader
            member.role = TeamRole.LEADER
            self.leader_id = agent_id
            self.updated_at = datetime.utcnow()
            return True
        return False

class TeamCreationRequest(BaseModel):
    """Request model for creating new teams"""
    name: str = Field(..., min_length=1, max_length=100)
    description: str = Field(..., max_length=1000)
    leader_id: Optional[str] = None
    initial_members: List[str] = Field(default_factory=list)
    objectives: List[str] = Field(default_factory=list)
    max_members: int = Field(default=10, ge=1, le=50)
    communication_channel: str = Field(..., min_length=1)

# Type aliases for clarity
AgentDict = Dict[str, Any]
ConversationHistory = List[Dict[str, Any]]
TokenStream = List[str]