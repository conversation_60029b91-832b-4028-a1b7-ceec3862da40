<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  
  const dispatch = createEventDispatcher();
  
  export let agentData: AgentConfig;
  export let showToasts: boolean = true;
  export let showModal: boolean = true;
  export let autoRetry: boolean = true;
  export let maxRetries: number = 3;
  export let validationRequired: boolean = true;
  
  interface AgentConfig {
    name: string;
    role: string;
    model: string;
    prompt: string;
    capabilities: string[];
    team?: string;
    metadata?: {
      description?: string;
      tags?: string[];
    };
  }
  
  interface CreationState {
    status: 'idle' | 'validating' | 'creating' | 'success' | 'error';
    progress: number;
    currentStep: string;
    message: string;
    error?: Error | null;
    result?: any;
    retryCount: number;
  }
  
  interface CreationStep {
    id: string;
    name: string;
    description: string;
    weight: number;
    handler: () => Promise<any>;
  }
  
  // Creation state
  let creationState: CreationState = {
    status: 'idle',
    progress: 0,
    currentStep: '',
    message: '',
    error: null,
    result: null,
    retryCount: 0
  };
  
  let showSuccessModal = false;
  let showErrorModal = false;
  let creationSteps: CreationStep[] = [];
  
  // Creation steps definition
  const getCreationSteps = (): CreationStep[] => [
    {
      id: 'validate',
      name: 'Validation',
      description: 'Validating agent configuration',
      weight: 10,
      handler: validateAgent
    },
    {
      id: 'prepare',
      name: 'Preparation',
      description: 'Preparing agent configuration',
      weight: 15,
      handler: prepareAgentConfig
    },
    {
      id: 'create',
      name: 'Creation',
      description: 'Creating agent instance',
      weight: 30,
      handler: createAgentInstance
    },
    {
      id: 'configure',
      name: 'Configuration',
      description: 'Configuring agent capabilities',
      weight: 25,
      handler: configureAgentCapabilities
    },
    {
      id: 'test',
      name: 'Testing',
      description: 'Running initial agent tests',
      weight: 15,
      handler: testAgentFunctionality
    },
    {
      id: 'deploy',
      name: 'Deployment',
      description: 'Deploying agent to system',
      weight: 5,
      handler: deployAgent
    }
  ];
  
  // Validation logic
  async function validateAgent(): Promise<any> {
    await delay(800);
    
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // Required field validation
    if (!agentData.name?.trim()) {
      errors.push('Agent name is required');
    }
    if (!agentData.role) {
      errors.push('Role selection is required');
    }
    if (!agentData.model) {
      errors.push('Model selection is required');
    }
    if (!agentData.prompt?.trim()) {
      errors.push('Prompt is required');
    }
    
    // Warning validations
    if (agentData.prompt && agentData.prompt.length < 50) {
      warnings.push('Prompt is quite short - consider adding more detail');
    }
    if (!agentData.capabilities || agentData.capabilities.length === 0) {
      warnings.push('No capabilities selected - agent may be less effective');
    }
    if (agentData.capabilities && agentData.capabilities.length > 15) {
      warnings.push('Many capabilities selected - consider focusing on fewer core skills');
    }
    
    // Advanced validation
    if (agentData.prompt) {
      if (!agentData.prompt.toLowerCase().includes('you are')) {
        warnings.push('Consider starting prompt with "You are..." for clarity');
      }
      if (agentData.prompt.length > 5000) {
        warnings.push('Very long prompt - consider summarizing key points');
      }
    }
    
    if (errors.length > 0) {
      throw new Error(`Validation failed: ${errors.join(', ')}`);
    }
    
    return {
      valid: true,
      warnings,
      score: calculateValidationScore()
    };
  }
  
  // Preparation logic
  async function prepareAgentConfig(): Promise<any> {
    await delay(600);
    
    const config = {
      id: `agent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: agentData.name.trim(),
      role: agentData.role,
      model: agentData.model,
      prompt: agentData.prompt.trim(),
      capabilities: [...agentData.capabilities],
      team: agentData.team,
      metadata: {
        created: new Date().toISOString(),
        version: '1.0.0',
        status: 'active',
        ...agentData.metadata
      },
      configuration: {
        maxTokens: getModelMaxTokens(agentData.model),
        temperature: getDefaultTemperature(agentData.role),
        topP: 0.9,
        frequencyPenalty: 0.1,
        presencePenalty: 0.1
      }
    };
    
    return config;
  }
  
  // Agent creation logic
  async function createAgentInstance(): Promise<any> {
    await delay(1200);
    
    // Simulate different creation scenarios
    const random = Math.random();
    if (random < 0.1 && creationState.retryCount === 0) {
      throw new Error('Network timeout - please try again');
    }
    if (random < 0.05) {
      throw new Error('Model temporarily unavailable');
    }
    
    const instance = {
      id: `instance_${Date.now()}`,
      status: 'initializing',
      pid: Math.floor(Math.random() * 10000),
      memory: '128MB',
      cpu: '0.1 cores',
      startTime: new Date().toISOString()
    };
    
    return instance;
  }
  
  // Capability configuration
  async function configureAgentCapabilities(): Promise<any> {
    await delay(900);
    
    const capabilityConfig = {
      skills: agentData.capabilities.map(cap => ({
        name: cap,
        level: getCapabilityLevel(cap, agentData.role),
        weight: getCapabilityWeight(cap, agentData.role)
      })),
      specializations: getSpecializations(agentData.role),
      restrictions: getRoleRestrictions(agentData.role)
    };
    
    return capabilityConfig;
  }
  
  // Testing logic
  async function testAgentFunctionality(): Promise<any> {
    await delay(1500);
    
    const testResults = {
      connectivity: { status: 'pass', responseTime: Math.round(Math.random() * 200 + 50) },
      modelAccess: { status: 'pass', modelVersion: agentData.model },
      promptProcessing: { 
        status: 'pass', 
        testPrompt: 'Hello, how are you?',
        response: `Hello! I'm ${agentData.name}, ready to assist you.`
      },
      capabilities: agentData.capabilities.map(cap => ({
        name: cap,
        status: Math.random() > 0.1 ? 'pass' : 'warning',
        details: Math.random() > 0.1 ? 'Working correctly' : 'Limited functionality'
      }))
    };
    
    const failedTests = testResults.capabilities.filter(c => c.status === 'fail');
    if (failedTests.length > 0) {
      throw new Error(`Capability tests failed: ${failedTests.map(t => t.name).join(', ')}`);
    }
    
    return testResults;
  }
  
  // Deployment logic
  async function deployAgent(): Promise<any> {
    await delay(400);
    
    const deployment = {
      id: `deployment_${Date.now()}`,
      status: 'active',
      endpoint: `/api/agents/${agentData.name.toLowerCase().replace(/\s+/g, '-')}`,
      health: 'healthy',
      deployTime: new Date().toISOString()
    };
    
    return deployment;
  }
  
  // Main creation function
  export async function createAgent(): Promise<any> {
    if (creationState.status === 'creating') {
      return; // Already in progress
    }
    
    creationSteps = getCreationSteps();
    creationState = {
      status: 'creating',
      progress: 0,
      currentStep: '',
      message: 'Starting agent creation...',
      error: null,
      result: null,
      retryCount: 0
    };
    
    showErrorModal = false;
    showSuccessModal = false;
    
    try {
      let cumulativeWeight = 0;
      const totalWeight = creationSteps.reduce((sum, step) => sum + step.weight, 0);
      const result: any = {};
      
      for (const step of creationSteps) {
        creationState.currentStep = step.name;
        creationState.message = step.description;
        
        try {
          const stepResult = await step.handler();
          result[step.id] = stepResult;
          
          cumulativeWeight += step.weight;
          creationState.progress = Math.round((cumulativeWeight / totalWeight) * 100);
          
          // Small delay between steps for better UX
          await delay(200);
          
        } catch (stepError) {
          if (autoRetry && creationState.retryCount < maxRetries && isRetryableError(stepError)) {
            creationState.retryCount++;
            creationState.message = `Retrying ${step.description} (${creationState.retryCount}/${maxRetries})...`;
            await delay(1000);
            
            // Retry the step
            const stepResult = await step.handler();
            result[step.id] = stepResult;
            
            cumulativeWeight += step.weight;
            creationState.progress = Math.round((cumulativeWeight / totalWeight) * 100);
          } else {
            throw stepError;
          }
        }
      }
      
      // Success
      creationState.status = 'success';
      creationState.progress = 100;
      creationState.message = 'Agent created successfully!';
      creationState.result = result;
      
      if (showModal) {
        showSuccessModal = true;
      }
      
      if (showToasts) {
        showToast('success', 'Agent created successfully!');
      }
      
      dispatch('success', {
        agent: result,
        config: agentData
      });
      
      return result;
      
    } catch (error) {
      creationState.status = 'error';
      creationState.error = error as Error;
      creationState.message = `Failed: ${(error as Error).message}`;
      
      if (showModal) {
        showErrorModal = true;
      }
      
      if (showToasts) {
        showToast('error', `Creation failed: ${(error as Error).message}`);
      }
      
      dispatch('error', {
        error: error as Error,
        config: agentData,
        state: creationState
      });
      
      throw error;
    }
  }
  
  // Retry creation
  function retryCreation() {
    creationState.retryCount = 0;
    createAgent();
  }
  
  // Helper functions
  function delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  function calculateValidationScore(): number {
    let score = 0;
    if (agentData.name?.trim()) score += 20;
    if (agentData.role) score += 20;
    if (agentData.model) score += 20;
    if (agentData.prompt?.trim()) score += 30;
    if (agentData.capabilities?.length > 0) score += 10;
    return score;
  }
  
  function getModelMaxTokens(model: string): number {
    const tokenLimits: Record<string, number> = {
      'gpt-4': 8192,
      'gpt-3.5-turbo': 4096,
      'claude-3-opus': 200000,
      'claude-3-sonnet': 200000,
      'claude-3-haiku': 200000,
      'gemini-pro': 32768
    };
    return tokenLimits[model] || 4096;
  }
  
  function getDefaultTemperature(role: string): number {
    const roleTemperatures: Record<string, number> = {
      'coder': 0.3,
      'debugger': 0.2,
      'analyst': 0.4,
      'writer': 0.7,
      'pm': 0.5,
      'qa': 0.3,
      'ux': 0.6
    };
    return roleTemperatures[role] || 0.5;
  }
  
  function getCapabilityLevel(capability: string, role: string): number {
    // Simple heuristic for capability levels
    const roleCapabilities: Record<string, string[]> = {
      'coder': ['programming', 'debugging', 'testing'],
      'analyst': ['data_analysis', 'statistics', 'visualization'],
      'writer': ['writing', 'documentation', 'communication']
    };
    
    const primaryCaps = roleCapabilities[role] || [];
    if (primaryCaps.some(cap => capability.includes(cap))) {
      return 0.9;
    }
    return 0.6;
  }
  
  function getCapabilityWeight(capability: string, role: string): number {
    return Math.random() * 0.5 + 0.5; // Random weight between 0.5 and 1.0
  }
  
  function getSpecializations(role: string): string[] {
    const specializations: Record<string, string[]> = {
      'coder': ['Code Generation', 'Bug Fixing', 'Refactoring'],
      'analyst': ['Data Insights', 'Statistical Analysis', 'Reporting'],
      'writer': ['Technical Writing', 'Content Creation', 'Documentation']
    };
    return specializations[role] || [];
  }
  
  function getRoleRestrictions(role: string): string[] {
    const restrictions: Record<string, string[]> = {
      'coder': ['No direct system access', 'Code review required'],
      'analyst': ['Data privacy compliance', 'No data modification'],
      'writer': ['Fact-checking required', 'Brand guidelines adherence']
    };
    return restrictions[role] || [];
  }
  
  function isRetryableError(error: any): boolean {
    const retryableErrors = [
      'Network timeout',
      'temporarily unavailable',
      'rate limited',
      'connection failed'
    ];
    
    const message = error.message?.toLowerCase() || '';
    return retryableErrors.some(retryable => message.includes(retryable));
  }
  
  function showToast(type: 'success' | 'error' | 'warning' | 'info', message: string) {
    // Dispatch toast event for parent component to handle
    dispatch('toast', { type, message });
  }
  
  function closeModals() {
    showSuccessModal = false;
    showErrorModal = false;
  }
  
  function handleSuccessAction(action: string) {
    dispatch('successAction', { action, result: creationState.result });
    closeModals();
  }
  
  function formatDuration(startTime: string): string {
    const start = new Date(startTime);
    const duration = Date.now() - start.getTime();
    return `${Math.round(duration / 1000)}s`;
  }
  
  // Export the current state for external access
  export function getCreationState() {
    return { ...creationState };
  }
  
  export function resetState() {
    creationState = {
      status: 'idle',
      progress: 0,
      currentStep: '',
      message: '',
      error: null,
      result: null,
      retryCount: 0
    };
    showSuccessModal = false;
    showErrorModal = false;
  }
</script>

<!-- Creation Progress Display -->
{#if creationState.status === 'creating'}
  <div class="creation-progress">
    <div class="progress-header">
      <h3>Creating Agent: {agentData.name}</h3>
      <div class="progress-stats">
        <span class="progress-percentage">{creationState.progress}%</span>
        {#if creationState.retryCount > 0}
          <span class="retry-indicator">Retry {creationState.retryCount}/{maxRetries}</span>
        {/if}
      </div>
    </div>
    
    <div class="progress-bar">
      <div 
        class="progress-fill" 
        style="width: {creationState.progress}%"
      ></div>
    </div>
    
    <div class="current-step">
      <div class="step-info">
        <span class="step-name">{creationState.currentStep}</span>
        <span class="step-description">{creationState.message}</span>
      </div>
      <div class="step-spinner">⟳</div>
    </div>
    
    <div class="creation-steps">
      {#each creationSteps as step, index}
        <div 
          class="step-item"
          class:completed={creationState.progress > (creationSteps.slice(0, index).reduce((sum, s) => sum + s.weight, 0) / creationSteps.reduce((sum, s) => sum + s.weight, 0)) * 100}
          class:current={step.name === creationState.currentStep}
        >
          <div class="step-indicator">
            {#if creationState.progress > (creationSteps.slice(0, index).reduce((sum, s) => sum + s.weight, 0) / creationSteps.reduce((sum, s) => sum + s.weight, 0)) * 100}
              ✓
            {:else if step.name === creationState.currentStep}
              ⟳
            {:else}
              {index + 1}
            {/if}
          </div>
          <span class="step-label">{step.name}</span>
        </div>
      {/each}
    </div>
  </div>
{/if}

<!-- Success Modal -->
{#if showSuccessModal && showModal}
  <div class="modal-overlay" on:click={closeModals}>
    <div class="modal-content success" on:click|stopPropagation>
      <div class="modal-header">
        <div class="success-icon">✅</div>
        <h2>Agent Created Successfully!</h2>
        <button type="button" class="close-btn" on:click={closeModals}>×</button>
      </div>
      
      <div class="modal-body">
        <div class="success-summary">
          <h3>{agentData.name}</h3>
          <p>Your agent has been created and deployed successfully.</p>
          
          <div class="success-details">
            <div class="detail-item">
              <span class="detail-label">Agent ID:</span>
              <span class="detail-value">{creationState.result?.prepare?.id || 'N/A'}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Role:</span>
              <span class="detail-value">{agentData.role}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Model:</span>
              <span class="detail-value">{agentData.model}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Capabilities:</span>
              <span class="detail-value">{agentData.capabilities?.length || 0} configured</span>
            </div>
            {#if creationState.result?.deploy?.endpoint}
              <div class="detail-item">
                <span class="detail-label">Endpoint:</span>
                <span class="detail-value endpoint">{creationState.result.deploy.endpoint}</span>
              </div>
            {/if}
          </div>
          
          {#if creationState.result?.test}
            <div class="test-results">
              <h4>Test Results</h4>
              <div class="test-summary">
                <div class="test-item">
                  <span class="test-name">Connectivity:</span>
                  <span class="test-status pass">✓ {creationState.result.test.connectivity.responseTime}ms</span>
                </div>
                <div class="test-item">
                  <span class="test-name">Model Access:</span>
                  <span class="test-status pass">✓ {creationState.result.test.modelAccess.modelVersion}</span>
                </div>
                <div class="test-item">
                  <span class="test-name">Capabilities:</span>
                  <span class="test-status pass">✓ {creationState.result.test.capabilities.filter(c => c.status === 'pass').length}/{creationState.result.test.capabilities.length} passed</span>
                </div>
              </div>
            </div>
          {/if}
        </div>
      </div>
      
      <div class="modal-actions">
        <button 
          type="button" 
          class="btn-secondary"
          on:click={() => handleSuccessAction('view')}
        >
          View Agent
        </button>
        <button 
          type="button" 
          class="btn-secondary"
          on:click={() => handleSuccessAction('test')}
        >
          Test Agent
        </button>
        <button 
          type="button" 
          class="btn-primary"
          on:click={() => handleSuccessAction('dashboard')}
        >
          Go to Dashboard
        </button>
      </div>
    </div>
  </div>
{/if}

<!-- Error Modal -->
{#if showErrorModal && showModal}
  <div class="modal-overlay" on:click={closeModals}>
    <div class="modal-content error" on:click|stopPropagation>
      <div class="modal-header">
        <div class="error-icon">❌</div>
        <h2>Agent Creation Failed</h2>
        <button type="button" class="close-btn" on:click={closeModals}>×</button>
      </div>
      
      <div class="modal-body">
        <div class="error-summary">
          <h3>Unable to create {agentData.name}</h3>
          <p class="error-message">{creationState.error?.message || 'Unknown error occurred'}</p>
          
          <div class="error-details">
            <div class="detail-item">
              <span class="detail-label">Failed at:</span>
              <span class="detail-value">{creationState.currentStep}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Retry attempts:</span>
              <span class="detail-value">{creationState.retryCount}/{maxRetries}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Progress:</span>
              <span class="detail-value">{creationState.progress}%</span>
            </div>
          </div>
          
          <div class="error-suggestions">
            <h4>Troubleshooting</h4>
            <ul>
              {#if creationState.error?.message?.includes('validation')}
                <li>Check that all required fields are filled correctly</li>
                <li>Ensure prompt meets minimum length requirements</li>
                <li>Verify selected capabilities are appropriate for the role</li>
              {:else if creationState.error?.message?.includes('network') || creationState.error?.message?.includes('timeout')}
                <li>Check your internet connection</li>
                <li>Try again in a few moments</li>
                <li>Contact support if the issue persists</li>
              {:else if creationState.error?.message?.includes('model')}
                <li>Try selecting a different AI model</li>
                <li>Reduce the complexity of your prompt</li>
                <li>Check model availability status</li>
              {:else}
                <li>Review your agent configuration</li>
                <li>Try creating the agent again</li>
                <li>Contact support for assistance</li>
              {/if}
            </ul>
          </div>
        </div>
      </div>
      
      <div class="modal-actions">
        <button 
          type="button" 
          class="btn-secondary"
          on:click={closeModals}
        >
          Close
        </button>
        {#if autoRetry && creationState.retryCount < maxRetries}
          <button 
            type="button" 
            class="btn-secondary"
            on:click={retryCreation}
          >
            Retry Creation
          </button>
        {/if}
        <button 
          type="button" 
          class="btn-primary"
          on:click={() => dispatch('editConfig')}
        >
          Edit Configuration
        </button>
      </div>
    </div>
  </div>
{/if}

<style>
  .creation-progress {
    background: #1f2937;
    border: 1px solid #374151;
    border-radius: 8px;
    padding: 24px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
  
  .progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }
  
  .progress-header h3 {
    margin: 0;
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
  }
  
  .progress-stats {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .progress-percentage {
    font-size: 20px;
    font-weight: bold;
    color: #3b82f6;
  }
  
  .retry-indicator {
    background: #f59e0b;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
  }
  
  .progress-bar {
    width: 100%;
    height: 8px;
    background: #374151;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 16px;
  }
  
  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    transition: width 0.3s ease;
  }
  
  .current-step {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 12px;
    background: #111827;
    border-radius: 6px;
  }
  
  .step-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }
  
  .step-name {
    color: #ffffff;
    font-weight: 600;
    font-size: 14px;
  }
  
  .step-description {
    color: #9ca3af;
    font-size: 13px;
  }
  
  .step-spinner {
    color: #3b82f6;
    font-size: 18px;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
  
  .creation-steps {
    display: flex;
    justify-content: space-between;
    gap: 8px;
  }
  
  .step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    flex: 1;
  }
  
  .step-indicator {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #374151;
    color: #9ca3af;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    transition: all 0.3s;
  }
  
  .step-item.completed .step-indicator {
    background: #10b981;
    color: white;
  }
  
  .step-item.current .step-indicator {
    background: #3b82f6;
    color: white;
    animation: pulse 1.5s ease-in-out infinite;
  }
  
  @keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
  }
  
  .step-label {
    color: #d1d5db;
    font-size: 11px;
    text-align: center;
    max-width: 60px;
  }
  
  .step-item.completed .step-label {
    color: #10b981;
  }
  
  .step-item.current .step-label {
    color: #3b82f6;
    font-weight: 600;
  }
  
  /* Modal Styles */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
  }
  
  .modal-content {
    background: #1f2937;
    border: 1px solid #374151;
    border-radius: 12px;
    max-width: 600px;
    max-height: 90vh;
    width: 90vw;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  
  .modal-content.success {
    border-color: #10b981;
  }
  
  .modal-content.error {
    border-color: #ef4444;
  }
  
  .modal-header {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 24px;
    border-bottom: 1px solid #374151;
  }
  
  .success-icon, .error-icon {
    font-size: 24px;
    flex-shrink: 0;
  }
  
  .modal-header h2 {
    margin: 0;
    color: #ffffff;
    font-size: 18px;
    flex: 1;
  }
  
  .close-btn {
    background: none;
    border: none;
    color: #9ca3af;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
  }
  
  .close-btn:hover {
    color: #ffffff;
  }
  
  .modal-body {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
  }
  
  .success-summary h3, .error-summary h3 {
    margin: 0 0 8px 0;
    color: #ffffff;
    font-size: 16px;
  }
  
  .success-summary p, .error-summary p {
    margin: 0 0 16px 0;
    color: #d1d5db;
  }
  
  .error-message {
    color: #fca5a5;
    background: rgba(239, 68, 68, 0.1);
    padding: 12px;
    border-radius: 6px;
    border-left: 4px solid #ef4444;
  }
  
  .success-details, .error-details {
    margin-bottom: 20px;
  }
  
  .detail-item {
    display: flex;
    justify-content: space-between;
    padding: 6px 0;
    border-bottom: 1px solid #374151;
  }
  
  .detail-item:last-child {
    border-bottom: none;
  }
  
  .detail-label {
    color: #9ca3af;
    font-size: 14px;
  }
  
  .detail-value {
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
  }
  
  .detail-value.endpoint {
    font-family: 'Consolas', 'Monaco', monospace;
    background: #374151;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
  }
  
  .test-results {
    background: #111827;
    border: 1px solid #374151;
    border-radius: 6px;
    padding: 16px;
  }
  
  .test-results h4 {
    margin: 0 0 12px 0;
    color: #ffffff;
    font-size: 14px;
  }
  
  .test-summary {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .test-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .test-name {
    color: #d1d5db;
    font-size: 13px;
  }
  
  .test-status {
    font-size: 13px;
    font-weight: 500;
  }
  
  .test-status.pass {
    color: #10b981;
  }
  
  .error-suggestions {
    background: #111827;
    border: 1px solid #374151;
    border-radius: 6px;
    padding: 16px;
  }
  
  .error-suggestions h4 {
    margin: 0 0 8px 0;
    color: #ffffff;
    font-size: 14px;
  }
  
  .error-suggestions ul {
    margin: 0;
    padding-left: 16px;
  }
  
  .error-suggestions li {
    color: #d1d5db;
    font-size: 13px;
    line-height: 1.4;
    margin-bottom: 4px;
  }
  
  .modal-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    padding: 20px 24px;
    border-top: 1px solid #374151;
  }
  
  .btn-secondary {
    padding: 10px 20px;
    background: #374151;
    border: 1px solid #4b5563;
    border-radius: 6px;
    color: #d1d5db;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .btn-secondary:hover {
    background: #4b5563;
  }
  
  .btn-primary {
    padding: 10px 20px;
    background: #3b82f6;
    border: 1px solid #3b82f6;
    border-radius: 6px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .btn-primary:hover {
    background: #2563eb;
  }
</style>