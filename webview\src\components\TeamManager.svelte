<script>
    import { onMount } from 'svelte';
    import { vscode } from '../utils/vscode';
    
    let teams = [];
    let selectedTeam = null;
    let showCreateForm = false;
    let loading = false;
    let error = null;
    
    // Team creation form
    let newTeam = {
        name: '',
        description: '',
        maxMembers: 10,
        objectives: [''],
        communicationChannel: 'default'
    };
    
    // Team editing state
    let editingTeam = null;
    let editingObjective = null;
    
    // Available agents for assignment
    let availableAgents = [];
    let selectedAgentId = '';
    let selectedMemberRole = 'member';
    
    onMount(async () => {
        await loadTeams();
        await loadAvailableAgents();
    });
    
    async function loadTeams() {
        try {
            loading = true;
            error = null;
            const response = await vscode.postMessage({
                command: 'getTeams'
            });
            teams = response.teams || [];
        } catch (err) {
            error = `Failed to load teams: ${err.message}`;
            console.error('Error loading teams:', err);
        } finally {
            loading = false;
        }
    }
    
    async function loadAvailableAgents() {
        try {
            const response = await vscode.postMessage({
                command: 'getAgents'
            });
            availableAgents = response.agents || [];
        } catch (err) {
            console.error('Error loading available agents:', err);
        }
    }
    
    async function createTeam() {
        try {
            loading = true;
            error = null;
            
            // Filter out empty objectives
            const objectives = newTeam.objectives.filter(obj => obj.trim() !== '');
            
            const response = await vscode.postMessage({
                command: 'createTeam',
                data: {
                    ...newTeam,
                    objectives
                }
            });
            
            if (response.success) {
                await loadTeams();
                resetCreateForm();
                showCreateForm = false;
            } else {
                error = response.error || 'Failed to create team';
            }
        } catch (err) {
            error = `Failed to create team: ${err.message}`;
            console.error('Error creating team:', err);
        } finally {
            loading = false;
        }
    }
    
    async function deleteTeam(teamId) {
        if (!confirm('Are you sure you want to delete this team?')) return;
        
        try {
            loading = true;
            error = null;
            
            const response = await vscode.postMessage({
                command: 'deleteTeam',
                data: { teamId }
            });
            
            if (response.success) {
                await loadTeams();
                if (selectedTeam?.id === teamId) {
                    selectedTeam = null;
                }
            } else {
                error = response.error || 'Failed to delete team';
            }
        } catch (err) {
            error = `Failed to delete team: ${err.message}`;
            console.error('Error deleting team:', err);
        } finally {
            loading = false;
        }
    }
    
    async function addTeamMember() {
        if (!selectedTeam || !selectedAgentId) return;
        
        try {
            loading = true;
            error = null;
            
            const response = await vscode.postMessage({
                command: 'addTeamMember',
                data: {
                    teamId: selectedTeam.id,
                    agentId: selectedAgentId,
                    role: selectedMemberRole
                }
            });
            
            if (response.success) {
                await loadTeams();
                selectedTeam = teams.find(t => t.id === selectedTeam.id);
                selectedAgentId = '';
            } else {
                error = response.error || 'Failed to add team member';
            }
        } catch (err) {
            error = `Failed to add team member: ${err.message}`;
            console.error('Error adding team member:', err);
        } finally {
            loading = false;
        }
    }
    
    async function removeTeamMember(agentId) {
        if (!selectedTeam || !confirm('Remove this member from the team?')) return;
        
        try {
            loading = true;
            error = null;
            
            const response = await vscode.postMessage({
                command: 'removeTeamMember',
                data: {
                    teamId: selectedTeam.id,
                    agentId
                }
            });
            
            if (response.success) {
                await loadTeams();
                selectedTeam = teams.find(t => t.id === selectedTeam.id);
            } else {
                error = response.error || 'Failed to remove team member';
            }
        } catch (err) {
            error = `Failed to remove team member: ${err.message}`;
            console.error('Error removing team member:', err);
        } finally {
            loading = false;
        }
    }
    
    async function setTeamLeader(agentId) {
        if (!selectedTeam) return;
        
        try {
            loading = true;
            error = null;
            
            const response = await vscode.postMessage({
                command: 'setTeamLeader',
                data: {
                    teamId: selectedTeam.id,
                    agentId
                }
            });
            
            if (response.success) {
                await loadTeams();
                selectedTeam = teams.find(t => t.id === selectedTeam.id);
            } else {
                error = response.error || 'Failed to set team leader';
            }
        } catch (err) {
            error = `Failed to set team leader: ${err.message}`;
            console.error('Error setting team leader:', err);
        } finally {
            loading = false;
        }
    }
    
    async function updateTeamObjective(index, newObjective) {
        if (!selectedTeam) return;
        
        try {
            loading = true;
            error = null;
            
            const response = await vscode.postMessage({
                command: 'updateTeamObjective',
                data: {
                    teamId: selectedTeam.id,
                    index,
                    objective: newObjective
                }
            });
            
            if (response.success) {
                await loadTeams();
                selectedTeam = teams.find(t => t.id === selectedTeam.id);
                editingObjective = null;
            } else {
                error = response.error || 'Failed to update objective';
            }
        } catch (err) {
            error = `Failed to update objective: ${err.message}`;
            console.error('Error updating objective:', err);
        } finally {
            loading = false;
        }
    }
    
    async function addTeamObjective() {
        if (!selectedTeam) return;
        
        try {
            loading = true;
            error = null;
            
            const response = await vscode.postMessage({
                command: 'addTeamObjective',
                data: {
                    teamId: selectedTeam.id,
                    objective: 'New objective'
                }
            });
            
            if (response.success) {
                await loadTeams();
                selectedTeam = teams.find(t => t.id === selectedTeam.id);
            } else {
                error = response.error || 'Failed to add objective';
            }
        } catch (err) {
            error = `Failed to add objective: ${err.message}`;
            console.error('Error adding objective:', err);
        } finally {
            loading = false;
        }
    }
    
    async function removeTeamObjective(index) {
        if (!selectedTeam || !confirm('Remove this objective?')) return;
        
        try {
            loading = true;
            error = null;
            
            const response = await vscode.postMessage({
                command: 'removeTeamObjective',
                data: {
                    teamId: selectedTeam.id,
                    index
                }
            });
            
            if (response.success) {
                await loadTeams();
                selectedTeam = teams.find(t => t.id === selectedTeam.id);
            } else {
                error = response.error || 'Failed to remove objective';
            }
        } catch (err) {
            error = `Failed to remove objective: ${err.message}`;
            console.error('Error removing objective:', err);
        } finally {
            loading = false;
        }
    }
    
    function resetCreateForm() {
        newTeam = {
            name: '',
            description: '',
            maxMembers: 10,
            objectives: [''],
            communicationChannel: 'default'
        };
    }
    
    function addObjectiveInput() {
        newTeam.objectives = [...newTeam.objectives, ''];
    }
    
    function removeObjectiveInput(index) {
        newTeam.objectives = newTeam.objectives.filter((_, i) => i !== index);
    }
    
    function selectTeam(team) {
        selectedTeam = team;
        showCreateForm = false;
    }
    
    function getAgentName(agentId) {
        const agent = availableAgents.find(a => a.id === agentId);
        return agent ? agent.name : agentId;
    }
    
    function getAgentRole(agentId) {
        const agent = availableAgents.find(a => a.id === agentId);
        return agent ? agent.role : 'unknown';
    }
    
    function getRoleColor(role) {
        const colors = {
            'coder': 'bg-blue-100 text-blue-800',
            'debugger': 'bg-red-100 text-red-800',
            'pm': 'bg-green-100 text-green-800',
            'ux': 'bg-purple-100 text-purple-800',
            'qa': 'bg-yellow-100 text-yellow-800',
            'critic': 'bg-orange-100 text-orange-800',
            'planner': 'bg-indigo-100 text-indigo-800'
        };
        return colors[role] || 'bg-gray-100 text-gray-800';
    }
    
    function getTeamRoleColor(role) {
        const colors = {
            'leader': 'bg-gold-100 text-gold-800',
            'member': 'bg-blue-100 text-blue-800',
            'observer': 'bg-gray-100 text-gray-800'
        };
        return colors[role] || 'bg-gray-100 text-gray-800';
    }
</script>

<div class="team-manager h-full flex flex-col">
    <!-- Header -->
    <div class="flex justify-between items-center p-4 border-b">
        <h2 class="text-xl font-semibold">Team Manager</h2>
        <button
            on:click={() => showCreateForm = !showCreateForm}
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            disabled={loading}
        >
            {showCreateForm ? 'Cancel' : 'Create Team'}
        </button>
    </div>
    
    {#if error}
        <div class="mx-4 mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
        </div>
    {/if}
    
    <div class="flex-1 flex overflow-hidden">
        <!-- Team List -->
        <div class="w-1/3 border-r overflow-y-auto">
            {#if showCreateForm}
                <div class="p-4 border-b bg-gray-50">
                    <h3 class="font-semibold mb-3">Create New Team</h3>
                    <form on:submit|preventDefault={createTeam} class="space-y-3">
                        <div>
                            <label class="block text-sm font-medium mb-1">Team Name</label>
                            <input
                                bind:value={newTeam.name}
                                type="text"
                                class="w-full p-2 border rounded"
                                placeholder="Enter team name"
                                required
                            />
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium mb-1">Description</label>
                            <textarea
                                bind:value={newTeam.description}
                                class="w-full p-2 border rounded"
                                placeholder="Team description"
                                rows="2"
                            ></textarea>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium mb-1">Max Members</label>
                            <input
                                bind:value={newTeam.maxMembers}
                                type="number"
                                min="1"
                                max="50"
                                class="w-full p-2 border rounded"
                            />
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium mb-1">Communication Channel</label>
                            <input
                                bind:value={newTeam.communicationChannel}
                                type="text"
                                class="w-full p-2 border rounded"
                                placeholder="Channel name"
                            />
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium mb-1">Objectives</label>
                            {#each newTeam.objectives as objective, index}
                                <div class="flex mb-2">
                                    <input
                                        bind:value={newTeam.objectives[index]}
                                        type="text"
                                        class="flex-1 p-2 border rounded-l"
                                        placeholder="Enter objective"
                                    />
                                    <button
                                        type="button"
                                        on:click={() => removeObjectiveInput(index)}
                                        class="px-3 py-2 bg-red-500 text-white rounded-r hover:bg-red-600"
                                        disabled={newTeam.objectives.length <= 1}
                                    >
                                        ×
                                    </button>
                                </div>
                            {/each}
                            <button
                                type="button"
                                on:click={addObjectiveInput}
                                class="text-blue-500 hover:text-blue-700 text-sm"
                            >
                                + Add Objective
                            </button>
                        </div>
                        
                        <div class="flex space-x-2">
                            <button
                                type="submit"
                                class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                                disabled={loading}
                            >
                                Create Team
                            </button>
                            <button
                                type="button"
                                on:click={() => showCreateForm = false}
                                class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                            >
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>
            {/if}
            
            <div class="p-4">
                <h3 class="font-semibold mb-3">Teams ({teams.length})</h3>
                {#if loading && teams.length === 0}
                    <div class="text-center py-4">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                        <p class="mt-2 text-gray-600">Loading teams...</p>
                    </div>
                {:else if teams.length === 0}
                    <p class="text-gray-600 text-center py-4">No teams created yet</p>
                {:else}
                    <div class="space-y-2">
                        {#each teams as team}
                            <div
                                class="p-3 border rounded cursor-pointer hover:bg-gray-50 transition-colors {selectedTeam?.id === team.id ? 'bg-blue-50 border-blue-300' : ''}"
                                on:click={() => selectTeam(team)}
                            >
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h4 class="font-medium">{team.name}</h4>
                                        <p class="text-sm text-gray-600">{team.description}</p>
                                        <div class="flex items-center mt-1 text-xs text-gray-500">
                                            <span>{team.members?.length || 0} members</span>
                                            <span class="mx-1">•</span>
                                            <span>{team.status}</span>
                                        </div>
                                    </div>
                                    <button
                                        on:click|stopPropagation={() => deleteTeam(team.id)}
                                        class="text-red-500 hover:text-red-700 text-xs"
                                    >
                                        Delete
                                    </button>
                                </div>
                            </div>
                        {/each}
                    </div>
                {/if}
            </div>
        </div>
        
        <!-- Team Details -->
        <div class="flex-1 overflow-y-auto">
            {#if selectedTeam}
                <div class="p-4">
                    <div class="flex justify-between items-start mb-4">
                        <div>
                            <h3 class="text-lg font-semibold">{selectedTeam.name}</h3>
                            <p class="text-gray-600">{selectedTeam.description}</p>
                        </div>
                        <div class="text-right text-sm text-gray-500">
                            <p>Created: {new Date(selectedTeam.created_at).toLocaleDateString()}</p>
                            <p>Updated: {new Date(selectedTeam.updated_at).toLocaleDateString()}</p>
                        </div>
                    </div>
                    
                    <!-- Team Performance -->
                    <div class="mb-6 p-4 bg-gray-50 rounded">
                        <h4 class="font-medium mb-2">Performance Metrics</h4>
                        <div class="grid grid-cols-3 gap-4 text-sm">
                            <div>
                                <span class="text-gray-600">Tasks Completed:</span>
                                <span class="font-medium ml-1">{selectedTeam.tasks_completed}</span>
                            </div>
                            <div>
                                <span class="text-gray-600">Success Rate:</span>
                                <span class="font-medium ml-1">{(selectedTeam.success_rate * 100).toFixed(1)}%</span>
                            </div>
                            <div>
                                <span class="text-gray-600">Avg. Time:</span>
                                <span class="font-medium ml-1">{selectedTeam.avg_completion_time > 0 ? `${Math.round(selectedTeam.avg_completion_time / 60)}m` : 'N/A'}</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Team Members -->
                    <div class="mb-6">
                        <h4 class="font-medium mb-3">Team Members ({selectedTeam.members?.length || 0}/{selectedTeam.max_members})</h4>
                        
                        {#if selectedTeam.members && selectedTeam.members.length > 0}
                            <div class="space-y-2 mb-4">
                                {#each selectedTeam.members.filter(m => m.is_active) as member}
                                    <div class="flex items-center justify-between p-3 border rounded">
                                        <div class="flex items-center space-x-3">
                                            <div>
                                                <div class="font-medium">{getAgentName(member.agent_id)}</div>
                                                <div class="flex items-center space-x-2 text-sm">
                                                    <span class="px-2 py-1 rounded text-xs {getRoleColor(getAgentRole(member.agent_id))}">
                                                        {getAgentRole(member.agent_id)}
                                                    </span>
                                                    <span class="px-2 py-1 rounded text-xs {getTeamRoleColor(member.role)}">
                                                        {member.role}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            {#if member.role !== 'leader'}
                                                <button
                                                    on:click={() => setTeamLeader(member.agent_id)}
                                                    class="px-3 py-1 text-xs bg-yellow-500 text-white rounded hover:bg-yellow-600"
                                                >
                                                    Make Leader
                                                </button>
                                            {/if}
                                            <button
                                                on:click={() => removeTeamMember(member.agent_id)}
                                                class="px-3 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600"
                                            >
                                                Remove
                                            </button>
                                        </div>
                                    </div>
                                {/each}
                            </div>
                        {:else}
                            <p class="text-gray-600 text-center py-4">No team members yet</p>
                        {/if}
                        
                        <!-- Add Member -->
                        <div class="flex items-center space-x-2">
                            <select
                                bind:value={selectedAgentId}
                                class="flex-1 p-2 border rounded"
                            >
                                <option value="">Select agent to add</option>
                                {#each availableAgents.filter(a => !selectedTeam.members?.some(m => m.agent_id === a.id && m.is_active)) as agent}
                                    <option value={agent.id}>{agent.name} ({agent.role})</option>
                                {/each}
                            </select>
                            <select
                                bind:value={selectedMemberRole}
                                class="p-2 border rounded"
                            >
                                <option value="member">Member</option>
                                <option value="leader">Leader</option>
                                <option value="observer">Observer</option>
                            </select>
                            <button
                                on:click={addTeamMember}
                                class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                                disabled={!selectedAgentId || loading}
                            >
                                Add
                            </button>
                        </div>
                    </div>
                    
                    <!-- Team Objectives -->
                    <div class="mb-6">
                        <div class="flex justify-between items-center mb-3">
                            <h4 class="font-medium">Objectives</h4>
                            <button
                                on:click={addTeamObjective}
                                class="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600"
                            >
                                Add Objective
                            </button>
                        </div>
                        
                        {#if selectedTeam.objectives && selectedTeam.objectives.length > 0}
                            <div class="space-y-2">
                                {#each selectedTeam.objectives as objective, index}
                                    <div class="flex items-center space-x-2">
                                        {#if editingObjective === index}
                                            <input
                                                type="text"
                                                value={objective}
                                                class="flex-1 p-2 border rounded"
                                                on:blur={(e) => updateTeamObjective(index, e.target.value)}
                                                on:keydown={(e) => {
                                                    if (e.key === 'Enter') {
                                                        updateTeamObjective(index, e.target.value);
                                                    } else if (e.key === 'Escape') {
                                                        editingObjective = null;
                                                    }
                                                }}
                                                autofocus
                                            />
                                        {:else}
                                            <div
                                                class="flex-1 p-2 border rounded bg-gray-50 cursor-pointer hover:bg-gray-100"
                                                on:click={() => editingObjective = index}
                                            >
                                                {objective}
                                            </div>
                                        {/if}
                                        <button
                                            on:click={() => removeTeamObjective(index)}
                                            class="px-2 py-1 text-red-500 hover:text-red-700"
                                        >
                                            ×
                                        </button>
                                    </div>
                                {/each}
                            </div>
                        {:else}
                            <p class="text-gray-600 text-center py-4">No objectives set</p>
                        {/if}
                    </div>
                    
                    <!-- Communication Channel -->
                    <div class="mb-6">
                        <h4 class="font-medium mb-2">Communication</h4>
                        <div class="p-3 bg-gray-50 rounded">
                            <div class="flex items-center space-x-2">
                                <span class="text-gray-600">Channel:</span>
                                <span class="font-medium">{selectedTeam.communication_channel}</span>
                            </div>
                            <div class="flex items-center space-x-2 mt-1">
                                <span class="text-gray-600">Messages:</span>
                                <span class="font-medium">{selectedTeam.message_history?.length || 0}</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Current Task -->
                    <div>
                        <h4 class="font-medium mb-2">Current Task</h4>
                        <div class="p-3 bg-gray-50 rounded">
                            {#if selectedTeam.current_task}
                                <p>{selectedTeam.current_task}</p>
                            {:else}
                                <p class="text-gray-600 italic">No current task assigned</p>
                            {/if}
                        </div>
                    </div>
                </div>
            {:else}
                <div class="p-4 text-center text-gray-600">
                    <p>Select a team to view details</p>
                </div>
            {/if}
        </div>
    </div>
</div>

<style>
    .team-manager {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    
    .bg-gold-100 {
        background-color: #fef3c7;
    }
    
    .text-gold-800 {
        color: #92400e;
    }
</style>