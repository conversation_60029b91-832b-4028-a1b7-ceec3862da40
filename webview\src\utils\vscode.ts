// VS Code API interface for webview communication
interface VsCodeApi {
  postMessage(message: any): void;
  setState(state: any): void;
  getState(): any;
}

// Get VS Code API (available in webview context)
declare function acquireVsCodeApi(): VsCodeApi;

// Create a mock API for development/testing
const createMockVsCodeApi = (): VsCodeApi => {
  // Mock data for development
  const mockAgents = [
    {
      id: 'agent-1',
      name: 'Code Analyzer',
      role: 'coder',
      model: 'gpt-4',
      status: 'idle',
      usage: {
        totalRequests: 42,
        avgResponseTime: 1200,
        successRate: 95
      }
    },
    {
      id: 'agent-2',
      name: 'Documentation Writer',
      role: 'writer',
      model: 'claude-3',
      status: 'active',
      usage: {
        totalRequests: 28,
        avgResponseTime: 800,
        successRate: 98
      }
    }
  ];

  const mockWorkspaceInfo = {
    projectType: 'TypeScript/Node.js',
    isGitRepository: true,
    rootPath: '/mock/workspace/path',
    name: 'Mock Workspace'
  };

  return {
    postMessage: (message: any) => {
      console.log('Mock VS Code API - postMessage:', message);

      // Simulate async responses from the extension
      setTimeout(() => {
        let response: any = null;

        switch (message.command) {
          case 'getAgentStatus':
            response = {
              command: 'updateAgentStatus',
              agents: mockAgents
            };
            break;

          case 'getWorkspaceInfo':
            response = {
              command: 'workspaceInfo',
              info: mockWorkspaceInfo
            };
            break;

          case 'selectFiles':
            response = {
              command: 'filesSelected',
              files: [
                'src/main.ts',
                'src/components/App.svelte',
                'package.json'
              ]
            };
            break;

          case 'startTask':
            // Simulate task starting
            window.dispatchEvent(new MessageEvent('message', {
              data: { command: 'taskStarted', taskId: 'mock-task-1', taskDescription: message.taskDescription }
            }));

            // Update agent status to running
            const runningAgents = mockAgents.map(agent => ({ ...agent, status: 'running' }));
            response = {
              command: 'updateAgentStatus',
              agents: runningAgents
            };
            break;

          case 'stopTask':
            response = {
              command: 'taskStopped'
            };
            // Reset agent status
            setTimeout(() => {
              window.dispatchEvent(new MessageEvent('message', {
                data: {
                  command: 'updateAgentStatus',
                  agents: mockAgents
                }
              }));
            }, 100);
            break;

          case 'clearOutput':
            response = {
              command: 'clearOutput'
            };
            break;
        }

        if (response) {
          window.dispatchEvent(new MessageEvent('message', { data: response }));
        }
      }, 200); // Simulate network delay
    },
    setState: (state: any) => {
      console.log('Mock VS Code API - setState:', state);
      localStorage.setItem('vscode-webview-state', JSON.stringify(state));
    },
    getState: () => {
      console.log('Mock VS Code API - getState');
      const stored = localStorage.getItem('vscode-webview-state');
      return stored ? JSON.parse(stored) : null;
    }
  };
};

// Safely acquire VS Code API with fallback
function getVsCodeApi(): VsCodeApi {
  try {
    // Check if we're in a VS Code webview context
    if (typeof acquireVsCodeApi !== 'undefined') {
      return acquireVsCodeApi();
    }
  } catch (error) {
    console.warn('VS Code API not available, using mock implementation:', error);
  }

  // Fallback to mock implementation for development
  return createMockVsCodeApi();
}

export const vscodeApi = getVsCodeApi();