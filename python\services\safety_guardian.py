"""
Safety Guardian for Metamorphic Reactor
Content filtering, safety checks, and ethical AI guardrails
"""

import asyncio
import logging
import re
import json
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass
import hashlib
import aiosqlite
from pathlib import Path

logger = logging.getLogger(__name__)

class SafetyLevel(str, Enum):
    """Safety check severity levels"""
    SAFE = "safe"
    WARNING = "warning"
    BLOCKED = "blocked"
    CRITICAL = "critical"

class ContentCategory(str, Enum):
    """Content categories for filtering"""
    SAFE = "safe"
    HARMFUL = "harmful"
    OFFENSIVE = "offensive"
    PERSONAL_INFO = "personal_info"
    MALICIOUS_CODE = "malicious_code"
    HATE_SPEECH = "hate_speech"
    VIOLENCE = "violence"
    ADULT_CONTENT = "adult_content"
    MISINFORMATION = "misinformation"
    SPAM = "spam"

class CheckType(str, Enum):
    """Types of safety checks"""
    CONTENT_FILTER = "content_filter"
    CODE_ANALYSIS = "code_analysis"
    PII_DETECTION = "pii_detection"
    PROMPT_INJECTION = "prompt_injection"
    RATE_ABUSE = "rate_abuse"
    ETHICAL_CHECK = "ethical_check"

@dataclass
class SafetyCheck:
    """Individual safety check result"""
    check_type: CheckType
    category: ContentCategory
    level: SafetyLevel
    confidence: float
    message: str
    details: Dict[str, Any] = None
    blocked_content: Optional[str] = None

@dataclass
class SafetyResult:
    """Complete safety analysis result"""
    allowed: bool
    overall_level: SafetyLevel
    checks: List[SafetyCheck]
    filtered_content: Optional[str] = None
    warnings: List[str] = None
    metadata: Dict[str, Any] = None

class SafetyGuardian:
    """
    Comprehensive safety and content filtering system
    
    Features:
    - Multi-layered content filtering
    - Code safety analysis
    - PII detection and masking
    - Prompt injection prevention
    - Rate abuse detection
    - Ethical AI guidelines enforcement
    - Configurable safety policies
    """
    
    def __init__(self, db_path: str = "data/safety_logs.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Safety patterns and rules
        self.blocked_patterns = self._load_blocked_patterns()
        self.warning_patterns = self._load_warning_patterns()
        self.pii_patterns = self._load_pii_patterns()
        self.code_danger_patterns = self._load_code_danger_patterns()
        
        # Configuration
        self.strict_mode = False
        self.mask_pii = True
        self.log_all_checks = True
        self.block_threshold = 0.8  # Confidence threshold for blocking
        self.warning_threshold = 0.6
        
        # Rate abuse tracking
        self.user_violations: Dict[str, List[datetime]] = {}
        self.violation_threshold = 5  # Max violations per hour
        
        self._initialized = False
    
    async def initialize(self):
        """Initialize the safety guardian"""
        if self._initialized:
            return
        
        await self._create_tables()
        self._initialized = True
        logger.info("Safety Guardian initialized successfully")
    
    async def _create_tables(self):
        """Create database tables for safety logging"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                CREATE TABLE IF NOT EXISTS safety_logs (
                    log_id TEXT PRIMARY KEY,
                    user_id TEXT,
                    task_id TEXT,
                    agent_id TEXT,
                    check_type TEXT NOT NULL,
                    category TEXT NOT NULL,
                    level TEXT NOT NULL,
                    confidence REAL NOT NULL,
                    message TEXT NOT NULL,
                    content_hash TEXT,
                    blocked_content TEXT,
                    details TEXT,
                    timestamp TEXT NOT NULL
                )
            """)
            
            await db.execute("""
                CREATE TABLE IF NOT EXISTS violation_history (
                    violation_id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    violation_type TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    count INTEGER DEFAULT 1,
                    first_occurrence TEXT NOT NULL,
                    last_occurrence TEXT NOT NULL,
                    details TEXT
                )
            """)
            
            # Indexes
            await db.execute("CREATE INDEX IF NOT EXISTS idx_safety_user ON safety_logs (user_id)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_safety_timestamp ON safety_logs (timestamp)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_violations_user ON violation_history (user_id)")
            
            await db.commit()
    
    async def check_content_safety(
        self,
        content: str,
        user_id: Optional[str] = None,
        task_id: Optional[str] = None,
        agent_id: Optional[str] = None,
        context: Dict[str, Any] = None
    ) -> SafetyResult:
        """
        Perform comprehensive safety check on content
        
        Args:
            content: Content to check
            user_id: User identifier
            task_id: Task identifier
            agent_id: Agent identifier
            context: Additional context
            
        Returns:
            SafetyResult: Complete safety analysis
        """
        await self.initialize()
        
        checks = []
        warnings = []
        filtered_content = content
        
        # 1. Content filtering
        content_check = await self._check_content_filter(content)
        checks.append(content_check)
        
        # 2. PII detection
        pii_check = await self._check_pii(content)
        checks.append(pii_check)
        if self.mask_pii and pii_check.blocked_content:
            filtered_content = pii_check.blocked_content
        
        # 3. Code analysis (if content contains code)
        if self._contains_code(content):
            code_check = await self._check_code_safety(content)
            checks.append(code_check)
        
        # 4. Prompt injection detection
        injection_check = await self._check_prompt_injection(content)
        checks.append(injection_check)
        
        # 5. Rate abuse check
        if user_id:
            abuse_check = await self._check_rate_abuse(user_id)
            if abuse_check:
                checks.append(abuse_check)
        
        # 6. Ethical guidelines
        ethical_check = await self._check_ethical_guidelines(content, context)
        checks.append(ethical_check)
        
        # Determine overall safety level
        overall_level = self._determine_overall_level(checks)
        allowed = overall_level not in [SafetyLevel.BLOCKED, SafetyLevel.CRITICAL]
        
        # Apply strict mode
        if self.strict_mode and overall_level == SafetyLevel.WARNING:
            allowed = False
            overall_level = SafetyLevel.BLOCKED
        
        # Log safety checks
        if self.log_all_checks or overall_level in [SafetyLevel.BLOCKED, SafetyLevel.CRITICAL]:
            for check in checks:
                await self._log_safety_check(check, user_id, task_id, agent_id, content)
        
        # Track violations
        if not allowed and user_id:
            await self._track_violation(user_id, overall_level)
        
        return SafetyResult(
            allowed=allowed,
            overall_level=overall_level,
            checks=checks,
            filtered_content=filtered_content if filtered_content != content else None,
            warnings=[c.message for c in checks if c.level == SafetyLevel.WARNING],
            metadata={
                "check_count": len(checks),
                "strict_mode": self.strict_mode,
                "user_id": user_id,
                "task_id": task_id,
                "agent_id": agent_id
            }
        )
    
    async def _check_content_filter(self, content: str) -> SafetyCheck:
        """Check content against blocked and warning patterns"""
        
        content_lower = content.lower()
        
        # Check blocked patterns
        for pattern, category in self.blocked_patterns:
            if re.search(pattern, content_lower, re.IGNORECASE):
                return SafetyCheck(
                    check_type=CheckType.CONTENT_FILTER,
                    category=category,
                    level=SafetyLevel.BLOCKED,
                    confidence=0.9,
                    message=f"Content blocked due to {category.value} content",
                    details={"pattern_matched": pattern}
                )
        
        # Check warning patterns
        for pattern, category in self.warning_patterns:
            if re.search(pattern, content_lower, re.IGNORECASE):
                return SafetyCheck(
                    check_type=CheckType.CONTENT_FILTER,
                    category=category,
                    level=SafetyLevel.WARNING,
                    confidence=0.7,
                    message=f"Content flagged for potential {category.value}",
                    details={"pattern_matched": pattern}
                )
        
        return SafetyCheck(
            check_type=CheckType.CONTENT_FILTER,
            category=ContentCategory.SAFE,
            level=SafetyLevel.SAFE,
            confidence=0.8,
            message="Content passed filter checks"
        )
    
    async def _check_pii(self, content: str) -> SafetyCheck:
        """Detect and optionally mask personally identifiable information"""
        
        pii_found = []
        masked_content = content
        
        for pattern, pii_type in self.pii_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                pii_found.append({
                    "type": pii_type,
                    "text": match.group(),
                    "start": match.start(),
                    "end": match.end()
                })
                
                # Mask the PII
                if self.mask_pii:
                    mask = f"[{pii_type.upper()}_REDACTED]"
                    masked_content = masked_content[:match.start()] + mask + masked_content[match.end():]
        
        if pii_found:
            level = SafetyLevel.WARNING if len(pii_found) <= 2 else SafetyLevel.BLOCKED
            return SafetyCheck(
                check_type=CheckType.PII_DETECTION,
                category=ContentCategory.PERSONAL_INFO,
                level=level,
                confidence=0.9,
                message=f"Detected {len(pii_found)} PII items",
                details={"pii_items": pii_found},
                blocked_content=masked_content if self.mask_pii else None
            )
        
        return SafetyCheck(
            check_type=CheckType.PII_DETECTION,
            category=ContentCategory.SAFE,
            level=SafetyLevel.SAFE,
            confidence=0.8,
            message="No PII detected"
        )
    
    def _contains_code(self, content: str) -> bool:
        """Check if content contains code"""
        code_indicators = [
            r'(?:def|function|class|import|from|#include)',
            r'[{}\[\];]',
            r'(?:if|else|for|while|try|catch)',
            r'(?:console\.log|print\(|echo)',
            r'(?://|/\*|\*/|#)',
        ]
        
        for indicator in code_indicators:
            if re.search(indicator, content, re.IGNORECASE):
                return True
        
        return False
    
    async def _check_code_safety(self, content: str) -> SafetyCheck:
        """Analyze code for potentially dangerous operations"""
        
        dangerous_found = []
        
        for pattern, danger_type, severity in self.code_danger_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                dangerous_found.append({
                    "type": danger_type,
                    "severity": severity,
                    "text": match.group(),
                    "line": content[:match.start()].count('\n') + 1
                })
        
        if dangerous_found:
            # Determine severity level
            max_severity = max(item["severity"] for item in dangerous_found)
            if max_severity >= 3:
                level = SafetyLevel.BLOCKED
            elif max_severity >= 2:
                level = SafetyLevel.WARNING
            else:
                level = SafetyLevel.WARNING
            
            return SafetyCheck(
                check_type=CheckType.CODE_ANALYSIS,
                category=ContentCategory.MALICIOUS_CODE,
                level=level,
                confidence=0.8,
                message=f"Detected {len(dangerous_found)} potentially dangerous code patterns",
                details={"dangerous_patterns": dangerous_found}
            )
        
        return SafetyCheck(
            check_type=CheckType.CODE_ANALYSIS,
            category=ContentCategory.SAFE,
            level=SafetyLevel.SAFE,
            confidence=0.7,
            message="Code analysis passed"
        )
    
    async def _check_prompt_injection(self, content: str) -> SafetyCheck:
        """Detect potential prompt injection attempts"""
        
        injection_patterns = [
            r'ignore\s+(?:all\s+)?(?:previous\s+)?(?:instructions|commands|prompts)',
            r'forget\s+(?:everything|all\s+previous)',
            r'new\s+(?:instructions|system\s+message)',
            r'act\s+as\s+(?:if|though)',
            r'pretend\s+(?:you\s+are|to\s+be)',
            r'roleplay\s+as',
            r'jailbreak|bypass|override',
            r'system\s*:\s*you\s+are\s+now',
        ]
        
        content_lower = content.lower()
        
        for pattern in injection_patterns:
            if re.search(pattern, content_lower):
                return SafetyCheck(
                    check_type=CheckType.PROMPT_INJECTION,
                    category=ContentCategory.HARMFUL,
                    level=SafetyLevel.BLOCKED,
                    confidence=0.85,
                    message="Potential prompt injection detected",
                    details={"pattern_matched": pattern}
                )
        
        return SafetyCheck(
            check_type=CheckType.PROMPT_INJECTION,
            category=ContentCategory.SAFE,
            level=SafetyLevel.SAFE,
            confidence=0.8,
            message="No prompt injection detected"
        )
    
    async def _check_rate_abuse(self, user_id: str) -> Optional[SafetyCheck]:
        """Check for rate abuse patterns"""
        
        now = datetime.utcnow()
        cutoff = now - timedelta(hours=1)
        
        # Get recent violations for user
        if user_id not in self.user_violations:
            self.user_violations[user_id] = []
        
        # Clean old violations
        self.user_violations[user_id] = [
            v for v in self.user_violations[user_id] if v > cutoff
        ]
        
        violation_count = len(self.user_violations[user_id])
        
        if violation_count >= self.violation_threshold:
            return SafetyCheck(
                check_type=CheckType.RATE_ABUSE,
                category=ContentCategory.SPAM,
                level=SafetyLevel.BLOCKED,
                confidence=0.95,
                message=f"Rate abuse detected: {violation_count} violations in 1 hour",
                details={"violation_count": violation_count, "threshold": self.violation_threshold}
            )
        
        return None
    
    async def _check_ethical_guidelines(self, content: str, context: Dict[str, Any]) -> SafetyCheck:
        """Check against ethical AI guidelines"""
        
        ethical_concerns = []
        
        # Check for attempts to create harmful content
        harmful_requests = [
            r'how\s+to\s+(?:hack|break|exploit|attack)',
            r'create\s+(?:virus|malware|exploit)',
            r'bypass\s+(?:security|authentication|protection)',
            r'illegal\s+(?:activities|methods|ways)',
            r'harm\s+(?:people|someone|others)',
        ]
        
        content_lower = content.lower()
        for pattern in harmful_requests:
            if re.search(pattern, content_lower):
                ethical_concerns.append(f"Request for potentially harmful information: {pattern}")
        
        # Check for attempts to impersonate or deceive
        impersonation_patterns = [
            r'pretend\s+to\s+be\s+(?:a\s+)?(?:human|person|real)',
            r'act\s+like\s+(?:a\s+)?(?:human|person|real)',
            r'don\'t\s+(?:tell|mention|say).*(?:ai|artificial|bot)',
        ]
        
        for pattern in impersonation_patterns:
            if re.search(pattern, content_lower):
                ethical_concerns.append(f"Potential deception attempt: {pattern}")
        
        if ethical_concerns:
            return SafetyCheck(
                check_type=CheckType.ETHICAL_CHECK,
                category=ContentCategory.HARMFUL,
                level=SafetyLevel.WARNING,
                confidence=0.7,
                message="Ethical guidelines concern detected",
                details={"concerns": ethical_concerns}
            )
        
        return SafetyCheck(
            check_type=CheckType.ETHICAL_CHECK,
            category=ContentCategory.SAFE,
            level=SafetyLevel.SAFE,
            confidence=0.8,
            message="Ethical guidelines check passed"
        )
    
    def _determine_overall_level(self, checks: List[SafetyCheck]) -> SafetyLevel:
        """Determine overall safety level from individual checks"""
        
        levels = [check.level for check in checks]
        
        if SafetyLevel.CRITICAL in levels:
            return SafetyLevel.CRITICAL
        elif SafetyLevel.BLOCKED in levels:
            return SafetyLevel.BLOCKED
        elif SafetyLevel.WARNING in levels:
            return SafetyLevel.WARNING
        else:
            return SafetyLevel.SAFE
    
    async def _log_safety_check(
        self,
        check: SafetyCheck,
        user_id: Optional[str],
        task_id: Optional[str],
        agent_id: Optional[str],
        content: str
    ):
        """Log safety check to database"""
        
        content_hash = hashlib.sha256(content.encode()).hexdigest()[:16]
        log_id = f"safety_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}_{content_hash}"
        
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                INSERT INTO safety_logs (
                    log_id, user_id, task_id, agent_id, check_type, category,
                    level, confidence, message, content_hash, blocked_content,
                    details, timestamp
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                log_id, user_id, task_id, agent_id, check.check_type.value,
                check.category.value, check.level.value, check.confidence,
                check.message, content_hash, check.blocked_content,
                json.dumps(check.details) if check.details else None,
                datetime.utcnow().isoformat()
            ))
            await db.commit()
    
    async def _track_violation(self, user_id: str, severity: SafetyLevel):
        """Track safety violations for a user"""
        
        now = datetime.utcnow()
        
        # Add to in-memory tracking
        if user_id not in self.user_violations:
            self.user_violations[user_id] = []
        self.user_violations[user_id].append(now)
        
        # Update database
        violation_id = f"{user_id}_{severity.value}"
        
        async with aiosqlite.connect(self.db_path) as db:
            # Check if violation type exists for user
            async with db.execute("""
                SELECT count, first_occurrence FROM violation_history 
                WHERE user_id = ? AND violation_type = ?
            """, (user_id, severity.value)) as cursor:
                row = await cursor.fetchone()
            
            if row:
                # Update existing record
                await db.execute("""
                    UPDATE violation_history 
                    SET count = count + 1, last_occurrence = ?
                    WHERE user_id = ? AND violation_type = ?
                """, (now.isoformat(), user_id, severity.value))
            else:
                # Create new record
                await db.execute("""
                    INSERT INTO violation_history (
                        violation_id, user_id, violation_type, severity,
                        count, first_occurrence, last_occurrence
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    violation_id, user_id, severity.value, severity.value,
                    1, now.isoformat(), now.isoformat()
                ))
            
            await db.commit()
    
    def _load_blocked_patterns(self) -> List[Tuple[str, ContentCategory]]:
        """Load patterns for blocked content"""
        return [
            # Hate speech
            (r'\b(?:kill|murder|assassinate)\s+(?:all\s+)?(?:jews|muslims|christians|blacks|whites)', ContentCategory.HATE_SPEECH),
            (r'\b(?:nazi|hitler|genocide|holocaust)\s+(?:was\s+)?(?:good|right|justified)', ContentCategory.HATE_SPEECH),
            
            # Violence
            (r'how\s+to\s+(?:kill|murder|hurt|harm)\s+(?:someone|people|humans)', ContentCategory.VIOLENCE),
            (r'instructions\s+for\s+(?:making|building|creating)\s+(?:bombs|explosives|weapons)', ContentCategory.VIOLENCE),
            
            # Malicious code
            (r'rm\s+-rf\s+/', ContentCategory.MALICIOUS_CODE),
            (r'format\s+c:', ContentCategory.MALICIOUS_CODE),
            (r':\(\)\s*\{\s*:\s*\|\s*:\s*&\s*\}\s*;', ContentCategory.MALICIOUS_CODE),  # Fork bomb
            
            # Adult content
            (r'\b(?:explicit|graphic)\s+(?:sexual|porn|nude)', ContentCategory.ADULT_CONTENT),
        ]
    
    def _load_warning_patterns(self) -> List[Tuple[str, ContentCategory]]:
        """Load patterns for warning content"""
        return [
            # Potentially harmful
            (r'(?:hack|crack|break\s+into)\s+(?:systems?|networks?|accounts?)', ContentCategory.HARMFUL),
            (r'bypass\s+(?:security|firewalls?|protections?)', ContentCategory.HARMFUL),
            
            # Misinformation indicators
            (r'(?:vaccines?|covid)\s+(?:cause|are)\s+(?:autism|dangerous|fake)', ContentCategory.MISINFORMATION),
            (r'earth\s+is\s+flat', ContentCategory.MISINFORMATION),
            
            # Spam indicators
            (r'(?:buy|purchase|order)\s+now\s+(?:for|at)\s+(?:only|\$)', ContentCategory.SPAM),
            (r'(?:free|win|won)\s+(?:money|\$|prize)', ContentCategory.SPAM),
        ]
    
    def _load_pii_patterns(self) -> List[Tuple[str, str]]:
        """Load patterns for PII detection"""
        return [
            # Email addresses
            (r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', 'email'),
            
            # Phone numbers
            (r'\b(?:\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}\b', 'phone'),
            
            # Social Security Numbers
            (r'\b\d{3}-\d{2}-\d{4}\b', 'ssn'),
            
            # Credit card numbers (basic pattern)
            (r'\b(?:\d{4}[-\s]?){3}\d{4}\b', 'credit_card'),
            
            # IP addresses
            (r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b', 'ip_address'),
        ]
    
    def _load_code_danger_patterns(self) -> List[Tuple[str, str, int]]:
        """Load patterns for dangerous code detection"""
        return [
            # File system operations (severity 3 = block, 2 = warn, 1 = info)
            (r'(?:rm|del|delete|unlink)\s+(?:-rf?\s+)?[/\\]', 'file_deletion', 3),
            (r'(?:format|fdisk|mkfs)', 'disk_format', 3),
            (r'shutdown|reboot|halt', 'system_control', 2),
            
            # Network operations
            (r'(?:wget|curl|fetch)\s+.*(?:http|ftp)', 'network_request', 2),
            (r'(?:nc|netcat|telnet)\s+.*\d+', 'network_connection', 2),
            
            # Process execution
            (r'(?:exec|system|shell_exec|eval|subprocess)', 'code_execution', 3),
            (r'(?:os\.system|commands\.)', 'system_command', 3),
            
            # Database operations
            (r'(?:drop\s+table|truncate|delete\s+from)', 'database_destructive', 2),
            
            # Cryptography/security
            (r'(?:password|passwd|secret|key)\s*=\s*["\'][^"\']+["\']', 'hardcoded_secret', 2),
        ]
    
    async def get_safety_statistics(self, days: int = 30) -> Dict[str, Any]:
        """Get safety statistics"""
        
        await self.initialize()
        
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        stats = {
            "period_days": days,
            "total_checks": 0,
            "by_level": {},
            "by_category": {},
            "by_check_type": {},
            "top_violations": [],
            "blocked_rate": 0.0
        }
        
        async with aiosqlite.connect(self.db_path) as db:
            # Total checks and by level
            async with db.execute("""
                SELECT level, COUNT(*) FROM safety_logs 
                WHERE timestamp >= ? 
                GROUP BY level
            """, (cutoff_date.isoformat(),)) as cursor:
                async for row in cursor:
                    stats["by_level"][row[0]] = row[1]
                    stats["total_checks"] += row[1]
            
            # By category
            async with db.execute("""
                SELECT category, COUNT(*) FROM safety_logs 
                WHERE timestamp >= ? 
                GROUP BY category
            """, (cutoff_date.isoformat(),)) as cursor:
                async for row in cursor:
                    stats["by_category"][row[0]] = row[1]
            
            # By check type
            async with db.execute("""
                SELECT check_type, COUNT(*) FROM safety_logs 
                WHERE timestamp >= ? 
                GROUP BY check_type
            """, (cutoff_date.isoformat(),)) as cursor:
                async for row in cursor:
                    stats["by_check_type"][row[0]] = row[1]
            
            # Top violating users
            async with db.execute("""
                SELECT user_id, COUNT(*) as violations FROM safety_logs 
                WHERE timestamp >= ? AND level IN ('blocked', 'critical')
                AND user_id IS NOT NULL
                GROUP BY user_id 
                ORDER BY violations DESC 
                LIMIT 10
            """, (cutoff_date.isoformat(),)) as cursor:
                async for row in cursor:
                    stats["top_violations"].append({
                        "user_id": row[0],
                        "violations": row[1]
                    })
        
        # Calculate blocked rate
        blocked_count = stats["by_level"].get("blocked", 0) + stats["by_level"].get("critical", 0)
        if stats["total_checks"] > 0:
            stats["blocked_rate"] = blocked_count / stats["total_checks"]
        
        return stats
    
    async def update_configuration(self, config: Dict[str, Any]):
        """Update safety configuration"""
        
        if "strict_mode" in config:
            self.strict_mode = config["strict_mode"]
        
        if "mask_pii" in config:
            self.mask_pii = config["mask_pii"]
        
        if "block_threshold" in config:
            self.block_threshold = max(0.0, min(1.0, config["block_threshold"]))
        
        if "warning_threshold" in config:
            self.warning_threshold = max(0.0, min(1.0, config["warning_threshold"]))
        
        if "violation_threshold" in config:
            self.violation_threshold = max(1, config["violation_threshold"])
        
        logger.info(f"Updated safety configuration: {config}")
    
    async def cleanup(self):
        """Cleanup resources"""
        logger.info("Safety Guardian cleanup completed")


# Global safety guardian instance
safety_guardian = SafetyGuardian()