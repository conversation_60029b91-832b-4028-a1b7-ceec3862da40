<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  
  const dispatch = createEventDispatcher();
  
  export let selectedRole: string = 'coder';
  export let layout: 'grid' | 'list' | 'dropdown' | 'tabs' = 'grid';
  export let size: 'small' | 'medium' | 'large' = 'medium';
  export let showDescriptions: boolean = true;
  export let showCapabilities: boolean = false;
  export let showMetrics: boolean = false;
  export let allowMultiple: boolean = false;
  export let disabled: boolean = false;
  export let filterByCategory: string | null = null;
  export let selectedRoles: string[] = [];
  
  interface RoleInfo {
    value: string;
    label: string;
    emoji: string;
    description: string;
    category: 'development' | 'management' | 'design' | 'quality' | 'coordination';
    capabilities: string[];
    complexity: number; // 1-5 scale
    teamSize: 'individual' | 'small' | 'medium' | 'large';
    experience: 'junior' | 'mid' | 'senior' | 'expert';
    color: string;
    bgColor: string;
    borderColor: string;
    recommended?: boolean;
  }
  
  const roleOptions: RoleInfo[] = [
    {
      value: 'coder',
      label: 'Coder',
      emoji: '👨‍💻',
      description: 'Skilled software developer for code generation, implementation, and programming tasks',
      category: 'development',
      capabilities: ['code_generation', 'refactoring', 'debugging', 'testing', 'documentation', 'api_design'],
      complexity: 4,
      teamSize: 'individual',
      experience: 'mid',
      color: 'text-blue-400',
      bgColor: 'bg-blue-900',
      borderColor: 'border-blue-500',
      recommended: true
    },
    {
      value: 'debugger',
      label: 'Debugger',
      emoji: '🐛',
      description: 'Expert at finding and fixing code issues, performance problems, and system errors',
      category: 'development',
      capabilities: ['error_analysis', 'stack_trace_analysis', 'performance_profiling', 'memory_debugging', 'log_analysis'],
      complexity: 5,
      teamSize: 'individual',
      experience: 'senior',
      color: 'text-red-400',
      bgColor: 'bg-red-900',
      borderColor: 'border-red-500',
      recommended: true
    },
    {
      value: 'planner',
      label: 'Planner',
      emoji: '🧠',
      description: 'Strategic planner for task decomposition, project organization, and workflow design',
      category: 'coordination',
      capabilities: ['task_decomposition', 'resource_allocation', 'timeline_management', 'dependency_analysis', 'risk_assessment'],
      complexity: 4,
      teamSize: 'medium',
      experience: 'senior',
      color: 'text-purple-400',
      bgColor: 'bg-purple-900',
      borderColor: 'border-purple-500',
      recommended: true
    },
    {
      value: 'critic',
      label: 'Critic',
      emoji: '🔍',
      description: 'Code reviewer and quality assurance specialist focused on maintaining high standards',
      category: 'quality',
      capabilities: ['code_review', 'quality_assurance', 'security_analysis', 'performance_review', 'design_critique'],
      complexity: 4,
      teamSize: 'individual',
      experience: 'senior',
      color: 'text-yellow-400',
      bgColor: 'bg-yellow-900',
      borderColor: 'border-yellow-500',
      recommended: true
    },
    {
      value: 'pm',
      label: 'Project Manager',
      emoji: '📋',
      description: 'Project manager for team coordination, stakeholder management, and delivery oversight',
      category: 'management',
      capabilities: ['stakeholder_management', 'requirements_gathering', 'sprint_planning', 'team_coordination', 'progress_tracking'],
      complexity: 3,
      teamSize: 'large',
      experience: 'senior',
      color: 'text-green-400',
      bgColor: 'bg-green-900',
      borderColor: 'border-green-500'
    },
    {
      value: 'ux',
      label: 'UX Designer',
      emoji: '🎨',
      description: 'User experience designer and researcher focused on creating intuitive interfaces',
      category: 'design',
      capabilities: ['user_research', 'wireframing', 'prototyping', 'usability_testing', 'accessibility_analysis'],
      complexity: 3,
      teamSize: 'small',
      experience: 'mid',
      color: 'text-pink-400',
      bgColor: 'bg-pink-900',
      borderColor: 'border-pink-500'
    },
    {
      value: 'qa',
      label: 'QA Engineer',
      emoji: '🧪',
      description: 'Quality assurance and testing specialist ensuring software reliability and performance',
      category: 'quality',
      capabilities: ['test_planning', 'test_execution', 'regression_testing', 'automation_testing', 'performance_testing'],
      complexity: 3,
      teamSize: 'small',
      experience: 'mid',
      color: 'text-orange-400',
      bgColor: 'bg-orange-900',
      borderColor: 'border-orange-500'
    },
    {
      value: 'orchestrator',
      label: 'Orchestrator',
      emoji: '🎯',
      description: 'Coordinates team activities, manages workflows, and ensures effective collaboration',
      category: 'coordination',
      capabilities: ['agent_coordination', 'task_distribution', 'consensus_building', 'conflict_resolution', 'workflow_management'],
      complexity: 5,
      teamSize: 'large',
      experience: 'expert',
      color: 'text-indigo-400',
      bgColor: 'bg-indigo-900',
      borderColor: 'border-indigo-500'
    }
  ];
  
  // Filter roles by category if specified
  $: filteredRoles = filterByCategory 
    ? roleOptions.filter(role => role.category === filterByCategory)
    : roleOptions;
  
  $: selectedRoleInfo = roleOptions.find(r => r.value === selectedRole);
  
  // Group roles by category
  $: rolesByCategory = filteredRoles.reduce((acc, role) => {
    if (!acc[role.category]) {
      acc[role.category] = [];
    }
    acc[role.category].push(role);
    return acc;
  }, {} as Record<string, RoleInfo[]>);
  
  // Size classes
  $: sizeClasses = {
    small: 'p-2 text-sm',
    medium: 'p-3 text-base',
    large: 'p-4 text-lg'
  }[size];
  
  function selectRole(roleValue: string) {
    if (disabled) return;
    
    if (allowMultiple) {
      if (selectedRoles.includes(roleValue)) {
        selectedRoles = selectedRoles.filter(r => r !== roleValue);
      } else {
        selectedRoles = [...selectedRoles, roleValue];
      }
      dispatch('rolesChanged', { roles: selectedRoles });
    } else {
      selectedRole = roleValue;
      dispatch('roleSelected', { 
        role: roleValue, 
        info: roleOptions.find(r => r.value === roleValue) 
      });
    }
  }
  
  function getCategoryIcon(category: string): string {
    switch (category) {
      case 'development': return '⚡';
      case 'management': return '📊';
      case 'design': return '🎨';
      case 'quality': return '✅';
      case 'coordination': return '🎯';
      default: return '🔧';
    }
  }
  
  function getCategoryLabel(category: string): string {
    switch (category) {
      case 'development': return 'Development';
      case 'management': return 'Management';
      case 'design': return 'Design';
      case 'quality': return 'Quality';
      case 'coordination': return 'Coordination';
      default: return 'Other';
    }
  }
  
  function getComplexityLabel(complexity: number): string {
    switch (complexity) {
      case 1: return 'Basic';
      case 2: return 'Simple';
      case 3: return 'Moderate';
      case 4: return 'Complex';
      case 5: return 'Advanced';
      default: return 'Unknown';
    }
  }
  
  function getTeamSizeLabel(teamSize: string): string {
    switch (teamSize) {
      case 'individual': return 'Individual';
      case 'small': return 'Small Team';
      case 'medium': return 'Medium Team';
      case 'large': return 'Large Team';
      default: return 'Any';
    }
  }
  
  function getExperienceLabel(experience: string): string {
    switch (experience) {
      case 'junior': return 'Junior';
      case 'mid': return 'Mid-Level';
      case 'senior': return 'Senior';
      case 'expert': return 'Expert';
      default: return 'Any';
    }
  }
  
  function isRoleSelected(roleValue: string): boolean {
    return allowMultiple
      ? selectedRoles.includes(roleValue)
      : selectedRole === roleValue;
  }

  // Dropdown state
  let isOpen = false;
  let activeCategory = Object.keys(rolesByCategory)[0];

  // Close dropdown when clicking outside
  function handleClickOutside(event: Event) {
    const target = event.target as Element;
    if (!target.closest('.role-selector')) {
      isOpen = false;
    }
  }

  if (typeof window !== 'undefined') {
    document.addEventListener('click', handleClickOutside);
  }
</script>

<div class="role-selector">
  {#if layout === 'grid'}
    <!-- Grid Layout -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {#each filteredRoles as role}
        <button
          type="button"
          class="text-left border rounded-lg transition-all duration-200 {sizeClasses} {isRoleSelected(role.value) ? `${role.bgColor} ${role.borderColor} shadow-lg` : 'bg-gray-800 border-gray-700 hover:bg-gray-700 hover:border-gray-600'} {disabled ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-md'}"
          on:click={() => selectRole(role.value)}
          {disabled}
        >
          <div class="flex items-center gap-2 mb-2">
            <span class="text-2xl">{role.emoji}</span>
            <div class="flex-1">
              <div class="font-semibold flex items-center gap-1">
                {role.label}
                {#if role.recommended}
                  <span class="text-xs bg-green-600 text-white px-1 rounded">★</span>
                {/if}
              </div>
              <div class="text-xs {role.color} opacity-75">{getCategoryLabel(role.category)}</div>
            </div>
          </div>
          
          {#if showDescriptions}
            <p class="text-sm text-gray-300 mb-2">{role.description}</p>
          {/if}
          
          {#if showCapabilities}
            <div class="flex flex-wrap gap-1 mb-2">
              {#each role.capabilities.slice(0, 3) as capability}
                <span class="text-xs bg-gray-700 text-gray-300 px-2 py-1 rounded">{capability}</span>
              {/each}
              {#if role.capabilities.length > 3}
                <span class="text-xs text-gray-400">+{role.capabilities.length - 3}</span>
              {/if}
            </div>
          {/if}
          
          {#if showMetrics}
            <div class="flex justify-between text-xs text-gray-400">
              <span>Complexity: {getComplexityLabel(role.complexity)}</span>
              <span>{getExperienceLabel(role.experience)}</span>
            </div>
          {/if}
        </button>
      {/each}
    </div>
    
  {:else if layout === 'list'}
    <!-- List Layout -->
    <div class="space-y-3">
      {#each filteredRoles as role}
        <button
          type="button"
          class="w-full text-left border rounded-lg transition-all duration-200 {sizeClasses} {isRoleSelected(role.value) ? `${role.bgColor} ${role.borderColor} shadow-lg` : 'bg-gray-800 border-gray-700 hover:bg-gray-700'} {disabled ? 'opacity-50 cursor-not-allowed' : ''}"
          on:click={() => selectRole(role.value)}
          {disabled}
        >
          <div class="flex items-center gap-4">
            <span class="text-3xl">{role.emoji}</span>
            <div class="flex-1">
              <div class="flex items-center gap-2 mb-1">
                <h3 class="font-semibold">{role.label}</h3>
                {#if role.recommended}
                  <span class="text-xs bg-green-600 text-white px-1 rounded">Recommended</span>
                {/if}
                <span class="text-xs {role.color} bg-gray-700 px-2 py-1 rounded">{getCategoryLabel(role.category)}</span>
              </div>
              {#if showDescriptions}
                <p class="text-sm text-gray-300 mb-2">{role.description}</p>
              {/if}
              {#if showCapabilities}
                <div class="flex flex-wrap gap-1 mb-1">
                  {#each role.capabilities.slice(0, 5) as capability}
                    <span class="text-xs bg-gray-700 text-gray-300 px-2 py-1 rounded">{capability}</span>
                  {/each}
                  {#if role.capabilities.length > 5}
                    <span class="text-xs text-gray-400">+{role.capabilities.length - 5} more</span>
                  {/if}
                </div>
              {/if}
            </div>
            {#if showMetrics}
              <div class="text-xs text-gray-400 text-right">
                <div>Complexity: {getComplexityLabel(role.complexity)}</div>
                <div>Team: {getTeamSizeLabel(role.teamSize)}</div>
                <div>Level: {getExperienceLabel(role.experience)}</div>
              </div>
            {/if}
          </div>
        </button>
      {/each}
    </div>
    
  {:else if layout === 'dropdown'}
    <!-- Dropdown Layout -->
    <div class="relative">
      <button
        type="button"
        class="w-full {sizeClasses} bg-gray-800 border border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors text-left flex items-center justify-between {disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-700'}"
        on:click={() => !disabled && (isOpen = !isOpen)}
        {disabled}
      >
        <div class="flex items-center gap-2">
          <span class="text-xl">{selectedRoleInfo?.emoji || '🔧'}</span>
          <div>
            <div class="font-medium">{selectedRoleInfo?.label || selectedRole}</div>
            <div class="text-xs {selectedRoleInfo?.color || 'text-gray-400'}">{selectedRoleInfo ? getCategoryLabel(selectedRoleInfo.category) : ''}</div>
          </div>
        </div>
        <div class="transform transition-transform {isOpen ? 'rotate-180' : ''}">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </div>
      </button>
      
      {#if isOpen}
        <div class="absolute z-50 w-full mt-1 bg-gray-800 border border-gray-700 rounded-md shadow-lg max-h-80 overflow-y-auto">
          {#each Object.entries(rolesByCategory) as [category, roles]}
            <div class="p-2 border-b border-gray-700">
              <div class="text-xs font-medium text-gray-400 mb-1 flex items-center gap-1">
                <span>{getCategoryIcon(category)}</span>
                {getCategoryLabel(category)}
              </div>
              {#each roles as role}
                <button
                  type="button"
                  class="w-full text-left p-2 rounded hover:bg-gray-700 transition-colors {isRoleSelected(role.value) ? role.bgColor : ''}"
                  on:click={() => selectRole(role.value)}
                >
                  <div class="flex items-center gap-2">
                    <span class="text-lg">{role.emoji}</span>
                    <div class="flex-1">
                      <div class="font-medium flex items-center gap-1">
                        {role.label}
                        {#if role.recommended}
                          <span class="text-xs bg-green-600 text-white px-1 rounded">★</span>
                        {/if}
                      </div>
                      {#if showDescriptions}
                        <div class="text-xs text-gray-400">{role.description}</div>
                      {/if}
                    </div>
                  </div>
                </button>
              {/each}
            </div>
          {/each}
        </div>
      {/if}
    </div>
    
  {:else if layout === 'tabs'}
    <!-- Tabs Layout -->
    <div class="space-y-4">
      <!-- Category Tabs -->
      <div class="flex flex-wrap gap-2 border-b border-gray-700 pb-2">
        {#each Object.keys(rolesByCategory) as category}
          <button
            type="button"
            class="px-3 py-1 text-sm rounded-md transition-colors {activeCategory === category ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}"
            on:click={() => activeCategory = category}
          >
            <span class="mr-1">{getCategoryIcon(category)}</span>
            {getCategoryLabel(category)}
          </button>
        {/each}
      </div>
      
      <!-- Roles in Active Category -->
      {#if activeCategory && rolesByCategory[activeCategory]}
        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
          {#each rolesByCategory[activeCategory] as role}
            <button
              type="button"
              class="text-left border rounded-lg transition-all duration-200 {sizeClasses} {isRoleSelected(role.value) ? `${role.bgColor} ${role.borderColor} shadow-lg` : 'bg-gray-800 border-gray-700 hover:bg-gray-700'} {disabled ? 'opacity-50 cursor-not-allowed' : ''}"
              on:click={() => selectRole(role.value)}
              {disabled}
            >
              <div class="flex items-center gap-2 mb-2">
                <span class="text-2xl">{role.emoji}</span>
                <div class="flex-1">
                  <div class="font-semibold flex items-center gap-1">
                    {role.label}
                    {#if role.recommended}
                      <span class="text-xs bg-green-600 text-white px-1 rounded">★</span>
                    {/if}
                  </div>
                </div>
              </div>
              {#if showDescriptions}
                <p class="text-sm text-gray-300">{role.description}</p>
              {/if}
            </button>
          {/each}
        </div>
      {/if}
    </div>
  {/if}
</div>

<!-- Role Info Panel -->
{#if selectedRoleInfo && (layout === 'grid' || layout === 'list') && (showCapabilities || showMetrics)}
  <div class="mt-6 p-4 {selectedRoleInfo.bgColor} border {selectedRoleInfo.borderColor} rounded-lg">
    <h4 class="font-semibold mb-2 flex items-center gap-2">
      <span class="text-2xl">{selectedRoleInfo.emoji}</span>
      {selectedRoleInfo.label}
      {#if selectedRoleInfo.recommended}
        <span class="text-xs bg-green-600 text-white px-1 rounded">Recommended</span>
      {/if}
    </h4>
    
    <p class="text-sm text-gray-300 mb-3">{selectedRoleInfo.description}</p>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
      <div>
        <div class="text-xs font-medium text-gray-400 mb-1">Category</div>
        <div class="{selectedRoleInfo.color}">{getCategoryLabel(selectedRoleInfo.category)}</div>
      </div>
      
      <div>
        <div class="text-xs font-medium text-gray-400 mb-1">Complexity</div>
        <div>{getComplexityLabel(selectedRoleInfo.complexity)}</div>
      </div>
      
      <div>
        <div class="text-xs font-medium text-gray-400 mb-1">Team Size</div>
        <div>{getTeamSizeLabel(selectedRoleInfo.teamSize)}</div>
      </div>
      
      <div>
        <div class="text-xs font-medium text-gray-400 mb-1">Experience Level</div>
        <div>{getExperienceLabel(selectedRoleInfo.experience)}</div>
      </div>
    </div>
    
    {#if showCapabilities}
      <div class="mt-3">
        <div class="text-xs font-medium text-gray-400 mb-2">Core Capabilities</div>
        <div class="flex flex-wrap gap-1">
          {#each selectedRoleInfo.capabilities as capability}
            <span class="text-xs bg-gray-700 text-gray-300 px-2 py-1 rounded">{capability}</span>
          {/each}
        </div>
      </div>
    {/if}
  </div>
{/if}



<style>
  .role-selector {
    position: relative;
  }
  
  /* Custom scrollbar for dropdown */
  .role-selector :global(.overflow-y-auto) {
    scrollbar-width: thin;
    scrollbar-color: #4B5563 #1F2937;
  }
  
  .role-selector :global(.overflow-y-auto::-webkit-scrollbar) {
    width: 6px;
  }
  
  .role-selector :global(.overflow-y-auto::-webkit-scrollbar-track) {
    background: #1F2937;
  }
  
  .role-selector :global(.overflow-y-auto::-webkit-scrollbar-thumb) {
    background: #4B5563;
    border-radius: 3px;
  }
  
  .role-selector :global(.overflow-y-auto::-webkit-scrollbar-thumb:hover) {
    background: #6B7280;
  }
</style>