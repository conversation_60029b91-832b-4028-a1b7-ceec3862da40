import * as vscode from 'vscode';
import * as path from 'path';

export interface WorkspaceInfo {
  rootPath: string | undefined;
  workspaceFolders: vscode.WorkspaceFolder[];
  isGitRepository: boolean;
  gitRoot: string | undefined;
  projectType: 'javascript' | 'typescript' | 'python' | 'java' | 'csharp' | 'other';
  packageFiles: string[];
}

export class WorkspaceManager {
  
  public static getWorkspaceInfo(): WorkspaceInfo {
    const workspaceFolders = Array.from(vscode.workspace.workspaceFolders || []);
    const rootPath = workspaceFolders.length > 0 ? workspaceFolders[0].uri.fsPath : undefined;
    
    return {
      rootPath,
      workspaceFolders,
      isGitRepository: this.isGitRepository(rootPath),
      gitRoot: this.findGitRoot(rootPath),
      projectType: this.detectProjectType(rootPath),
      packageFiles: this.findPackageFiles(rootPath)
    };
  }

  public static async pickWorkspaceFiles(options?: {
    title?: string;
    canPickMany?: boolean;
    filters?: { [name: string]: string[] };
  }): Promise<vscode.Uri[] | undefined> {
    const workspaceInfo = this.getWorkspaceInfo();
    
    if (workspaceInfo.workspaceFolders.length === 0) {
      vscode.window.showWarningMessage('No workspace folder is open');
      return undefined;
    }

    const defaultUri = workspaceInfo.workspaceFolders[0].uri;
    
    return await vscode.window.showOpenDialog({
      title: options?.title || 'Select files for task context',
      canSelectMany: options?.canPickMany ?? true,
      canSelectFiles: true,
      canSelectFolders: false,
      defaultUri,
      filters: options?.filters || {
        'All Files': ['*']
      }
    });
  }

  public static async pickWorkspaceFolder(): Promise<vscode.WorkspaceFolder | undefined> {
    const workspaceFolders = vscode.workspace.workspaceFolders;
    
    if (!workspaceFolders || workspaceFolders.length === 0) {
      vscode.window.showWarningMessage('No workspace folder is open');
      return undefined;
    }

    if (workspaceFolders.length === 1) {
      return workspaceFolders[0];
    }

    // Multiple workspace folders - let user pick
    const items = workspaceFolders.map(folder => ({
      label: folder.name,
      description: folder.uri.fsPath,
      folder
    }));

    const selected = await vscode.window.showQuickPick(items, {
      title: 'Select workspace folder',
      placeHolder: 'Choose the workspace folder for this task'
    });

    return selected?.folder;
  }

  public static getRelativePath(filePath: string, workspaceFolder?: vscode.WorkspaceFolder): string {
    if (!workspaceFolder) {
      const workspaceInfo = this.getWorkspaceInfo();
      if (workspaceInfo.workspaceFolders.length > 0) {
        workspaceFolder = workspaceInfo.workspaceFolders[0];
      }
    }

    if (workspaceFolder) {
      return path.relative(workspaceFolder.uri.fsPath, filePath);
    }

    return path.basename(filePath);
  }

  public static async getFileContent(uri: vscode.Uri, maxLines?: number): Promise<string> {
    try {
      const document = await vscode.workspace.openTextDocument(uri);
      const text = document.getText();
      
      if (maxLines) {
        const lines = text.split('\n');
        if (lines.length > maxLines) {
          return lines.slice(0, maxLines).join('\n') + `\n... (${lines.length - maxLines} more lines)`;
        }
      }
      
      return text;
    } catch (error) {
      console.error('Error reading file:', error);
      return `Error reading file: ${error}`;
    }
  }

  public static isGitRepository(rootPath?: string): boolean {
    if (!rootPath) return false;
    
    try {
      const fs = require('fs');
      const gitPath = path.join(rootPath, '.git');
      return fs.existsSync(gitPath);
    } catch (error) {
      return false;
    }
  }

  public static findGitRoot(startPath?: string): string | undefined {
    if (!startPath) return undefined;

    let currentPath = startPath;
    const fs = require('fs');
    
    while (currentPath !== path.dirname(currentPath)) {
      const gitPath = path.join(currentPath, '.git');
      if (fs.existsSync(gitPath)) {
        return currentPath;
      }
      currentPath = path.dirname(currentPath);
    }
    
    return undefined;
  }

  public static detectProjectType(rootPath?: string): 'javascript' | 'typescript' | 'python' | 'java' | 'csharp' | 'other' {
    if (!rootPath) return 'other';

    const fs = require('fs');
    
    // Check for package.json (JavaScript/TypeScript)
    if (fs.existsSync(path.join(rootPath, 'package.json'))) {
      // Check for TypeScript config
      if (fs.existsSync(path.join(rootPath, 'tsconfig.json')) || 
          fs.existsSync(path.join(rootPath, 'tsconfig.build.json'))) {
        return 'typescript';
      }
      return 'javascript';
    }
    
    // Check for Python
    if (fs.existsSync(path.join(rootPath, 'requirements.txt')) ||
        fs.existsSync(path.join(rootPath, 'pyproject.toml')) ||
        fs.existsSync(path.join(rootPath, 'setup.py'))) {
      return 'python';
    }
    
    // Check for Java
    if (fs.existsSync(path.join(rootPath, 'pom.xml')) ||
        fs.existsSync(path.join(rootPath, 'build.gradle')) ||
        fs.existsSync(path.join(rootPath, 'build.gradle.kts'))) {
      return 'java';
    }
    
    // Check for C#
    if (fs.existsSync(path.join(rootPath, '*.csproj')) ||
        fs.existsSync(path.join(rootPath, '*.sln'))) {
      return 'csharp';
    }
    
    return 'other';
  }

  public static findPackageFiles(rootPath?: string): string[] {
    if (!rootPath) return [];

    const fs = require('fs');
    const packageFiles: string[] = [];
    
    const checkFiles = [
      'package.json',
      'requirements.txt',
      'pyproject.toml',
      'setup.py',
      'pom.xml',
      'build.gradle',
      'build.gradle.kts',
      'Cargo.toml',
      'go.mod',
      'composer.json'
    ];
    
    for (const file of checkFiles) {
      const filePath = path.join(rootPath, file);
      if (fs.existsSync(filePath)) {
        packageFiles.push(file);
      }
    }
    
    return packageFiles;
  }

  public static async getCurrentFileContext(): Promise<{
    activeFile?: string;
    selectedText?: string;
    cursorPosition?: vscode.Position;
  }> {
    const activeEditor = vscode.window.activeTextEditor;
    
    if (!activeEditor) {
      return {};
    }

    return {
      activeFile: activeEditor.document.uri.fsPath,
      selectedText: activeEditor.document.getText(activeEditor.selection),
      cursorPosition: activeEditor.selection.active
    };
  }
}