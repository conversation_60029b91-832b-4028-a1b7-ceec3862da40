"""
Agents package for Metamorphic Reactor
Multi-agent LLM orchestration with autonomous consensus building
"""

from .agent_types import (
    AgentRole, TaskStatus, AgentStatus, ConsensusLevel,
    AgentConfig, AgentResponse, ConversationRound, TaskContext
)

from .base_agent import BaseAgent
from .planner_agent import PlannerAgent
from .critic_agent import CriticAgent
from .additional_agent import AdditionalAgent
from .agent_factory import AgentFactory, agent_factory

__all__ = [
    # Enums and types
    "AgentRole",
    "TaskStatus", 
    "AgentStatus",
    "ConsensusLevel",
    
    # Data models
    "AgentConfig",
    "AgentResponse", 
    "ConversationRound",
    "TaskContext",
    
    # Agent classes
    "BaseAgent",
    "PlannerAgent",
    "CriticAgent", 
    "AdditionalAgent",
    
    # Factory
    "AgentFactory",
    "agent_factory"
]