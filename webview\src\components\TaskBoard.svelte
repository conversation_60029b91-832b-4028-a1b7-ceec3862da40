<script>
    import { onMount, onDestroy } from 'svelte';
    import { vscode } from '../utils/vscode';
    
    let tasks = [];
    let loading = false;
    let error = null;
    let selectedTask = null;
    let showCreateModal = false;
    let showAssignModal = false;
    let refreshInterval;
    
    // Task creation form
    let newTask = {
        title: '',
        description: '',
        priority: 'normal',
        dependencies: [],
        required_capabilities: [],
        deadline: '',
        context: {}
    };
    
    // Task assignment form
    let assignmentData = {
        task_id: '',
        agent_id: '',
        team_id: '',
        priority_boost: 0
    };
    
    // Filters and sorting
    let statusFilter = 'all';
    let priorityFilter = 'all';
    let agentFilter = 'all';
    let sortBy = 'created_at';
    let sortOrder = 'desc';
    
    // Available agents and teams
    let availableAgents = [];
    let availableTeams = [];
    
    // Task analytics
    let analytics = {
        total_tasks: 0,
        completed_tasks: 0,
        success_rate: 0,
        avg_completion_time: 0,
        active_agents: 0,
        bottlenecks: []
    };
    
    // Task columns for Kanban view
    const taskColumns = [
        { status: 'pending', title: 'Pending', color: 'bg-yellow-100' },
        { status: 'assigned', title: 'Assigned', color: 'bg-blue-100' },
        { status: 'in_progress', title: 'In Progress', color: 'bg-purple-100' },
        { status: 'completed', title: 'Completed', color: 'bg-green-100' },
        { status: 'failed', title: 'Failed', color: 'bg-red-100' }
    ];
    
    onMount(async () => {
        await loadTasks();
        await loadAgents();
        await loadTeams();
        await loadAnalytics();
        
        // Set up real-time updates
        refreshInterval = setInterval(async () => {
            await loadTasks();
            await loadAnalytics();
        }, 5000);
    });
    
    onDestroy(() => {
        if (refreshInterval) {
            clearInterval(refreshInterval);
        }
    });
    
    async function loadTasks() {
        try {
            loading = true;
            error = null;
            const response = await vscode.postMessage({
                command: 'getTasks',
                data: {
                    status: statusFilter !== 'all' ? statusFilter : null,
                    priority: priorityFilter !== 'all' ? priorityFilter : null,
                    agent_id: agentFilter !== 'all' ? agentFilter : null,
                    sort_by: sortBy,
                    sort_order: sortOrder
                }
            });
            
            if (response.success) {
                tasks = response.tasks || [];
            } else {
                error = response.error || 'Failed to load tasks';
            }
        } catch (err) {
            error = `Failed to load tasks: ${err.message}`;
            console.error('Error loading tasks:', err);
        } finally {
            loading = false;
        }
    }
    
    async function loadAgents() {
        try {
            const response = await vscode.postMessage({
                command: 'getAgents'
            });
            
            if (response.success) {
                availableAgents = response.agents || [];
            }
        } catch (err) {
            console.error('Error loading agents:', err);
        }
    }
    
    async function loadTeams() {
        try {
            const response = await vscode.postMessage({
                command: 'getTeams'
            });
            
            if (response.success) {
                availableTeams = response.teams || [];
            }
        } catch (err) {
            console.error('Error loading teams:', err);
        }
    }
    
    async function loadAnalytics() {
        try {
            const response = await vscode.postMessage({
                command: 'getTaskAnalytics'
            });
            
            if (response.success) {
                analytics = response.analytics || analytics;
            }
        } catch (err) {
            console.error('Error loading analytics:', err);
        }
    }
    
    async function createTask() {
        try {
            loading = true;
            error = null;
            
            const response = await vscode.postMessage({
                command: 'createTask',
                data: {
                    ...newTask,
                    dependencies: newTask.dependencies.filter(d => d.trim() !== ''),
                    required_capabilities: newTask.required_capabilities.filter(c => c.trim() !== ''),
                    deadline: newTask.deadline || null
                }
            });
            
            if (response.success) {
                await loadTasks();
                await loadAnalytics();
                resetCreateForm();
                showCreateModal = false;
            } else {
                error = response.error || 'Failed to create task';
            }
        } catch (err) {
            error = `Failed to create task: ${err.message}`;
            console.error('Error creating task:', err);
        } finally {
            loading = false;
        }
    }
    
    async function assignTask() {
        try {
            loading = true;
            error = null;
            
            const response = await vscode.postMessage({
                command: 'assignTask',
                data: assignmentData
            });
            
            if (response.success) {
                await loadTasks();
                await loadAnalytics();
                showAssignModal = false;
                resetAssignmentForm();
            } else {
                error = response.error || 'Failed to assign task';
            }
        } catch (err) {
            error = `Failed to assign task: ${err.message}`;
            console.error('Error assigning task:', err);
        } finally {
            loading = false;
        }
    }
    
    async function updateTaskStatus(taskId, newStatus) {
        try {
            const response = await vscode.postMessage({
                command: 'updateTaskStatus',
                data: {
                    task_id: taskId,
                    status: newStatus
                }
            });
            
            if (response.success) {
                await loadTasks();
                await loadAnalytics();
            } else {
                error = response.error || 'Failed to update task status';
            }
        } catch (err) {
            error = `Failed to update task status: ${err.message}`;
            console.error('Error updating task status:', err);
        }
    }
    
    async function deleteTask(taskId) {
        if (!confirm('Are you sure you want to delete this task?')) return;
        
        try {
            loading = true;
            error = null;
            
            const response = await vscode.postMessage({
                command: 'deleteTask',
                data: { task_id: taskId }
            });
            
            if (response.success) {
                await loadTasks();
                await loadAnalytics();
                if (selectedTask?.id === taskId) {
                    selectedTask = null;
                }
            } else {
                error = response.error || 'Failed to delete task';
            }
        } catch (err) {
            error = `Failed to delete task: ${err.message}`;
            console.error('Error deleting task:', err);
        } finally {
            loading = false;
        }
    }
    
    async function reassignTask(taskId) {
        try {
            const response = await vscode.postMessage({
                command: 'reassignTask',
                data: { task_id: taskId }
            });
            
            if (response.success) {
                await loadTasks();
                await loadAnalytics();
            } else {
                error = response.error || 'Failed to reassign task';
            }
        } catch (err) {
            error = `Failed to reassign task: ${err.message}`;
            console.error('Error reassigning task:', err);
        }
    }
    
    function resetCreateForm() {
        newTask = {
            title: '',
            description: '',
            priority: 'normal',
            dependencies: [],
            required_capabilities: [],
            deadline: '',
            context: {}
        };
    }
    
    function resetAssignmentForm() {
        assignmentData = {
            task_id: '',
            agent_id: '',
            team_id: '',
            priority_boost: 0
        };
    }
    
    function openAssignModal(task) {
        assignmentData.task_id = task.id;
        showAssignModal = true;
    }
    
    function getPriorityColor(priority) {
        const colors = {
            'critical': 'bg-red-500 text-white',
            'high': 'bg-orange-500 text-white',
            'normal': 'bg-blue-500 text-white',
            'low': 'bg-gray-500 text-white'
        };
        return colors[priority] || colors['normal'];
    }
    
    function getStatusColor(status) {
        const colors = {
            'pending': 'bg-yellow-100 text-yellow-800',
            'assigned': 'bg-blue-100 text-blue-800',
            'in_progress': 'bg-purple-100 text-purple-800',
            'completed': 'bg-green-100 text-green-800',
            'failed': 'bg-red-100 text-red-800'
        };
        return colors[status] || colors['pending'];
    }
    
    function getAgentName(agentId) {
        const agent = availableAgents.find(a => a.id === agentId);
        return agent ? agent.name : agentId;
    }
    
    function getTeamName(teamId) {
        const team = availableTeams.find(t => t.id === teamId);
        return team ? team.name : teamId;
    }
    
    function formatDate(dateString) {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleDateString();
    }
    
    function formatDuration(seconds) {
        if (!seconds || seconds <= 0) return 'N/A';
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
    }
    
    function getTasksByStatus(status) {
        return tasks.filter(task => task.status === status);
    }
    
    function addDependency() {
        newTask.dependencies = [...newTask.dependencies, ''];
    }
    
    function removeDependency(index) {
        newTask.dependencies = newTask.dependencies.filter((_, i) => i !== index);
    }
    
    function addCapability() {
        newTask.required_capabilities = [...newTask.required_capabilities, ''];
    }
    
    function removeCapability(index) {
        newTask.required_capabilities = newTask.required_capabilities.filter((_, i) => i !== index);
    }
    
    // Reactive statements for filtering
    $: filteredTasks = tasks.filter(task => {
        if (statusFilter !== 'all' && task.status !== statusFilter) return false;
        if (priorityFilter !== 'all' && task.priority !== priorityFilter) return false;
        if (agentFilter !== 'all' && task.assigned_agent !== agentFilter) return false;
        return true;
    });
</script>

<div class="task-board h-full flex flex-col">
    <!-- Header -->
    <div class="flex justify-between items-center p-4 border-b">
        <div class="flex items-center space-x-4">
            <h2 class="text-xl font-semibold">Task Board</h2>
            <div class="flex items-center space-x-2 text-sm">
                <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded">
                    {analytics.total_tasks} Total
                </span>
                <span class="px-2 py-1 bg-green-100 text-green-800 rounded">
                    {analytics.success_rate.toFixed(1)}% Success
                </span>
                <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded">
                    {analytics.active_agents} Active Agents
                </span>
            </div>
        </div>
        <div class="flex items-center space-x-2">
            <button
                on:click={() => showCreateModal = true}
                class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
                Create Task
            </button>
            <button
                on:click={loadTasks}
                class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
                disabled={loading}
            >
                Refresh
            </button>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="flex items-center space-x-4 p-4 border-b bg-gray-50">
        <div class="flex items-center space-x-2">
            <label class="text-sm font-medium">Status:</label>
            <select bind:value={statusFilter} on:change={loadTasks} class="px-2 py-1 border rounded">
                <option value="all">All</option>
                <option value="pending">Pending</option>
                <option value="assigned">Assigned</option>
                <option value="in_progress">In Progress</option>
                <option value="completed">Completed</option>
                <option value="failed">Failed</option>
            </select>
        </div>
        
        <div class="flex items-center space-x-2">
            <label class="text-sm font-medium">Priority:</label>
            <select bind:value={priorityFilter} on:change={loadTasks} class="px-2 py-1 border rounded">
                <option value="all">All</option>
                <option value="critical">Critical</option>
                <option value="high">High</option>
                <option value="normal">Normal</option>
                <option value="low">Low</option>
            </select>
        </div>
        
        <div class="flex items-center space-x-2">
            <label class="text-sm font-medium">Agent:</label>
            <select bind:value={agentFilter} on:change={loadTasks} class="px-2 py-1 border rounded">
                <option value="all">All</option>
                {#each availableAgents as agent}
                    <option value={agent.id}>{agent.name}</option>
                {/each}
            </select>
        </div>
        
        <div class="flex items-center space-x-2">
            <label class="text-sm font-medium">Sort:</label>
            <select bind:value={sortBy} on:change={loadTasks} class="px-2 py-1 border rounded">
                <option value="created_at">Created</option>
                <option value="priority">Priority</option>
                <option value="deadline">Deadline</option>
                <option value="status">Status</option>
            </select>
            <select bind:value={sortOrder} on:change={loadTasks} class="px-2 py-1 border rounded">
                <option value="desc">Desc</option>
                <option value="asc">Asc</option>
            </select>
        </div>
    </div>
    
    {#if error}
        <div class="mx-4 mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
        </div>
    {/if}
    
    <!-- Analytics Dashboard -->
    <div class="p-4 border-b bg-gray-50">
        <h3 class="font-semibold mb-2">Analytics</h3>
        <div class="grid grid-cols-4 gap-4 text-sm">
            <div class="text-center">
                <div class="text-2xl font-bold text-blue-600">{analytics.total_tasks}</div>
                <div class="text-gray-600">Total Tasks</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-green-600">{analytics.completed_tasks}</div>
                <div class="text-gray-600">Completed</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-purple-600">{analytics.success_rate.toFixed(1)}%</div>
                <div class="text-gray-600">Success Rate</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-orange-600">{formatDuration(analytics.avg_completion_time)}</div>
                <div class="text-gray-600">Avg Time</div>
            </div>
        </div>
        
        {#if analytics.bottlenecks && analytics.bottlenecks.length > 0}
            <div class="mt-4">
                <h4 class="font-medium text-sm mb-2">Bottlenecks:</h4>
                <div class="flex flex-wrap gap-2">
                    {#each analytics.bottlenecks as bottleneck}
                        <span class="px-2 py-1 bg-red-100 text-red-800 rounded text-xs">
                            {bottleneck}
                        </span>
                    {/each}
                </div>
            </div>
        {/if}
    </div>
    
    <!-- Kanban Board -->
    <div class="flex-1 overflow-hidden">
        <div class="h-full flex">
            {#each taskColumns as column}
                <div class="flex-1 flex flex-col border-r">
                    <div class="p-3 {column.color} border-b">
                        <h3 class="font-semibold text-sm">
                            {column.title}
                            <span class="ml-2 px-2 py-1 bg-white rounded text-xs">
                                {getTasksByStatus(column.status).length}
                            </span>
                        </h3>
                    </div>
                    
                    <div class="flex-1 overflow-y-auto p-2 space-y-2">
                        {#each getTasksByStatus(column.status) as task}
                            <div class="bg-white border rounded p-3 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                                 on:click={() => selectedTask = task}>
                                <div class="flex justify-between items-start mb-2">
                                    <h4 class="font-medium text-sm">{task.title}</h4>
                                    <span class="px-2 py-1 rounded text-xs {getPriorityColor(task.priority)}">
                                        {task.priority}
                                    </span>
                                </div>
                                
                                <p class="text-xs text-gray-600 mb-2 line-clamp-2">{task.description}</p>
                                
                                <div class="flex justify-between items-center text-xs">
                                    <div class="flex items-center space-x-2">
                                        {#if task.assigned_agent}
                                            <span class="text-blue-600">
                                                👤 {getAgentName(task.assigned_agent)}
                                            </span>
                                        {/if}
                                        {#if task.deadline}
                                            <span class="text-orange-600">
                                                📅 {formatDate(task.deadline)}
                                            </span>
                                        {/if}
                                    </div>
                                    
                                    <div class="flex items-center space-x-1">
                                        {#if task.dependencies && task.dependencies.length > 0}
                                            <span class="text-gray-500">🔗 {task.dependencies.length}</span>
                                        {/if}
                                        
                                        {#if task.status === 'pending' || task.status === 'assigned'}
                                            <button
                                                on:click|stopPropagation={() => openAssignModal(task)}
                                                class="text-blue-500 hover:text-blue-700"
                                                title="Assign Task"
                                            >
                                                ➜
                                            </button>
                                        {/if}
                                        
                                        {#if task.status === 'failed'}
                                            <button
                                                on:click|stopPropagation={() => reassignTask(task.id)}
                                                class="text-yellow-500 hover:text-yellow-700"
                                                title="Reassign Task"
                                            >
                                                🔄
                                            </button>
                                        {/if}
                                        
                                        <button
                                            on:click|stopPropagation={() => deleteTask(task.id)}
                                            class="text-red-500 hover:text-red-700"
                                            title="Delete Task"
                                        >
                                            ×
                                        </button>
                                    </div>
                                </div>
                            </div>
                        {/each}
                    </div>
                </div>
            {/each}
        </div>
    </div>
</div>

<!-- Create Task Modal -->
{#if showCreateModal}
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
            <h3 class="text-lg font-semibold mb-4">Create New Task</h3>
            
            <form on:submit|preventDefault={createTask} class="space-y-4">
                <div>
                    <label class="block text-sm font-medium mb-1">Title</label>
                    <input
                        bind:value={newTask.title}
                        type="text"
                        class="w-full p-2 border rounded"
                        placeholder="Enter task title"
                        required
                    />
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-1">Description</label>
                    <textarea
                        bind:value={newTask.description}
                        class="w-full p-2 border rounded"
                        placeholder="Task description"
                        rows="3"
                        required
                    ></textarea>
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-1">Priority</label>
                    <select bind:value={newTask.priority} class="w-full p-2 border rounded">
                        <option value="low">Low</option>
                        <option value="normal">Normal</option>
                        <option value="high">High</option>
                        <option value="critical">Critical</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-1">Deadline</label>
                    <input
                        bind:value={newTask.deadline}
                        type="datetime-local"
                        class="w-full p-2 border rounded"
                    />
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-1">Dependencies</label>
                    {#each newTask.dependencies as dependency, index}
                        <div class="flex mb-2">
                            <input
                                bind:value={newTask.dependencies[index]}
                                type="text"
                                class="flex-1 p-2 border rounded-l"
                                placeholder="Task ID or name"
                            />
                            <button
                                type="button"
                                on:click={() => removeDependency(index)}
                                class="px-3 py-2 bg-red-500 text-white rounded-r hover:bg-red-600"
                            >
                                ×
                            </button>
                        </div>
                    {/each}
                    <button
                        type="button"
                        on:click={addDependency}
                        class="text-blue-500 hover:text-blue-700 text-sm"
                    >
                        + Add Dependency
                    </button>
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-1">Required Capabilities</label>
                    {#each newTask.required_capabilities as capability, index}
                        <div class="flex mb-2">
                            <input
                                bind:value={newTask.required_capabilities[index]}
                                type="text"
                                class="flex-1 p-2 border rounded-l"
                                placeholder="Capability name"
                            />
                            <button
                                type="button"
                                on:click={() => removeCapability(index)}
                                class="px-3 py-2 bg-red-500 text-white rounded-r hover:bg-red-600"
                            >
                                ×
                            </button>
                        </div>
                    {/each}
                    <button
                        type="button"
                        on:click={addCapability}
                        class="text-blue-500 hover:text-blue-700 text-sm"
                    >
                        + Add Capability
                    </button>
                </div>
                
                <div class="flex justify-end space-x-2">
                    <button
                        type="button"
                        on:click={() => showCreateModal = false}
                        class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                    >
                        Cancel
                    </button>
                    <button
                        type="submit"
                        class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                        disabled={loading}
                    >
                        Create Task
                    </button>
                </div>
            </form>
        </div>
    </div>
{/if}

<!-- Task Assignment Modal -->
{#if showAssignModal}
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 class="text-lg font-semibold mb-4">Assign Task</h3>
            
            <form on:submit|preventDefault={assignTask} class="space-y-4">
                <div>
                    <label class="block text-sm font-medium mb-1">Agent</label>
                    <select bind:value={assignmentData.agent_id} class="w-full p-2 border rounded">
                        <option value="">Select agent</option>
                        {#each availableAgents as agent}
                            <option value={agent.id}>{agent.name} ({agent.role})</option>
                        {/each}
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-1">Team (Optional)</label>
                    <select bind:value={assignmentData.team_id} class="w-full p-2 border rounded">
                        <option value="">Select team</option>
                        {#each availableTeams as team}
                            <option value={team.id}>{team.name}</option>
                        {/each}
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-1">Priority Boost</label>
                    <input
                        bind:value={assignmentData.priority_boost}
                        type="number"
                        min="0"
                        max="10"
                        class="w-full p-2 border rounded"
                        placeholder="0-10"
                    />
                </div>
                
                <div class="flex justify-end space-x-2">
                    <button
                        type="button"
                        on:click={() => showAssignModal = false}
                        class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                    >
                        Cancel
                    </button>
                    <button
                        type="submit"
                        class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                        disabled={loading || !assignmentData.agent_id}
                    >
                        Assign Task
                    </button>
                </div>
            </form>
        </div>
    </div>
{/if}

<!-- Task Details Modal -->
{#if selectedTask}
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-start mb-4">
                <h3 class="text-lg font-semibold">{selectedTask.title}</h3>
                <button
                    on:click={() => selectedTask = null}
                    class="text-gray-500 hover:text-gray-700"
                >
                    ×
                </button>
            </div>
            
            <div class="space-y-4">
                <div class="flex items-center space-x-4">
                    <span class="px-3 py-1 rounded text-sm {getStatusColor(selectedTask.status)}">
                        {selectedTask.status}
                    </span>
                    <span class="px-3 py-1 rounded text-sm {getPriorityColor(selectedTask.priority)}">
                        {selectedTask.priority}
                    </span>
                </div>
                
                <div>
                    <h4 class="font-medium mb-2">Description</h4>
                    <p class="text-gray-700">{selectedTask.description}</p>
                </div>
                
                {#if selectedTask.assigned_agent}
                    <div>
                        <h4 class="font-medium mb-2">Assigned Agent</h4>
                        <p class="text-blue-600">{getAgentName(selectedTask.assigned_agent)}</p>
                    </div>
                {/if}
                
                {#if selectedTask.assigned_team}
                    <div>
                        <h4 class="font-medium mb-2">Assigned Team</h4>
                        <p class="text-purple-600">{getTeamName(selectedTask.assigned_team)}</p>
                    </div>
                {/if}
                
                {#if selectedTask.dependencies && selectedTask.dependencies.length > 0}
                    <div>
                        <h4 class="font-medium mb-2">Dependencies</h4>
                        <div class="flex flex-wrap gap-2">
                            {#each selectedTask.dependencies as dep}
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 rounded text-sm">
                                    {dep}
                                </span>
                            {/each}
                        </div>
                    </div>
                {/if}
                
                {#if selectedTask.required_capabilities && selectedTask.required_capabilities.length > 0}
                    <div>
                        <h4 class="font-medium mb-2">Required Capabilities</h4>
                        <div class="flex flex-wrap gap-2">
                            {#each selectedTask.required_capabilities as cap}
                                <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded text-sm">
                                    {cap}
                                </span>
                            {/each}
                        </div>
                    </div>
                {/if}
                
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <span class="text-gray-600">Created:</span>
                        <span class="ml-2">{formatDate(selectedTask.created_at)}</span>
                    </div>
                    <div>
                        <span class="text-gray-600">Updated:</span>
                        <span class="ml-2">{formatDate(selectedTask.updated_at)}</span>
                    </div>
                    {#if selectedTask.deadline}
                        <div>
                            <span class="text-gray-600">Deadline:</span>
                            <span class="ml-2">{formatDate(selectedTask.deadline)}</span>
                        </div>
                    {/if}
                    {#if selectedTask.completion_time}
                        <div>
                            <span class="text-gray-600">Completion Time:</span>
                            <span class="ml-2">{formatDuration(selectedTask.completion_time)}</span>
                        </div>
                    {/if}
                </div>
                
                {#if selectedTask.result}
                    <div>
                        <h4 class="font-medium mb-2">Result</h4>
                        <div class="p-3 bg-gray-50 rounded">
                            <pre class="text-sm whitespace-pre-wrap">{JSON.stringify(selectedTask.result, null, 2)}</pre>
                        </div>
                    </div>
                {/if}
                
                <div class="flex justify-end space-x-2">
                    {#if selectedTask.status === 'pending' || selectedTask.status === 'assigned'}
                        <button
                            on:click={() => openAssignModal(selectedTask)}
                            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                        >
                            Assign
                        </button>
                    {/if}
                    
                    {#if selectedTask.status === 'failed'}
                        <button
                            on:click={() => reassignTask(selectedTask.id)}
                            class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600"
                        >
                            Reassign
                        </button>
                    {/if}
                    
                    <button
                        on:click={() => deleteTask(selectedTask.id)}
                        class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
                    >
                        Delete
                    </button>
                </div>
            </div>
        </div>
    </div>
{/if}

<style>
    .task-board {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>