<script lang="ts">
  import { createEventDispatcher, onMount } from 'svelte';
  import AgentCard from './AgentCard.svelte';
  import AgentCreator from './AgentCreator.svelte';
  
  const dispatch = createEventDispatcher();
  
  export let agents: any[] = [];
  export let isCreating: boolean = false;
  export let showAdvancedCreation: boolean = false;
  export let showCreateButton: boolean = true;
  export let showQuickActions: boolean = true;
  export let showStats: boolean = true;
  export let showFilters: boolean = true;
  export let layout: 'grid' | 'list' | 'compact' = 'grid';
  
  interface AgentStats {
    total: number;
    active: number;
    inactive: number;
    draft: number;
    totalRequests: number;
    avgResponseTime: number;
    successRate: number;
  }
  
  let showCreateModal = false;
  let selectedFilter = 'all';
  let searchQuery = '';
  let sortBy = 'created';
  let sortOrder = 'desc';
  let bulkActions = false;
  let selectedAgents: string[] = [];
  let showCreationOptions = false;
  let showAdvancedFilters = false;
  let savedFilters: any[] = [];
  let currentFilterName = '';
  let advancedFilters = {
    dateRange: { start: '', end: '' },
    usageRange: { min: 0, max: 1000 },
    models: [],
    teams: [],
    tags: [],
    performanceThreshold: 0,
    lastUsedDays: 30
  };
  let agentStats: AgentStats = {
    total: 0,
    active: 0,
    inactive: 0,
    draft: 0,
    totalRequests: 0,
    avgResponseTime: 0,
    successRate: 0
  };
  
  // Filter options
  const filterOptions = [
    { value: 'all', label: 'All Agents', icon: '🤖' },
    { value: 'active', label: 'Active', icon: '🟢' },
    { value: 'inactive', label: 'Inactive', icon: '🔴' },
    { value: 'draft', label: 'Draft', icon: '📝' },
    { value: 'error', label: 'Error', icon: '❌' },
    { value: 'coder', label: 'Coders', icon: '💻' },
    { value: 'analyst', label: 'Analysts', icon: '📊' },
    { value: 'writer', label: 'Writers', icon: '✍️' },
    { value: 'qa', label: 'QA', icon: '✅' },
    { value: 'debugger', label: 'Debuggers', icon: '🔍' },
    { value: 'pm', label: 'PMs', icon: '📋' },
    { value: 'ux', label: 'UX', icon: '🎨' }
  ];
  
  // Advanced search operators
  const searchOperators = [
    { op: 'name:', description: 'Search by name' },
    { op: 'role:', description: 'Search by role' },
    { op: 'model:', description: 'Search by model' },
    { op: 'team:', description: 'Search by team' },
    { op: 'tag:', description: 'Search by tag' },
    { op: 'status:', description: 'Search by status' },
    { op: 'created:', description: 'Search by creation date' },
    { op: 'usage:', description: 'Search by usage count' }
  ];
  
  // Sort options
  const sortOptions = [
    { value: 'created', label: 'Created Date' },
    { value: 'name', label: 'Name' },
    { value: 'role', label: 'Role' },
    { value: 'usage', label: 'Usage' },
    { value: 'performance', label: 'Performance' },
    { value: 'lastUsed', label: 'Last Used' }
  ];
  
  // Quick action templates
  const quickActions = [
    {
      id: 'code-reviewer',
      name: 'Code Reviewer',
      description: 'Create a code review specialist',
      icon: '👨‍💻',
      role: 'coder',
      estimatedTime: '2 min'
    },
    {
      id: 'data-analyst',
      name: 'Data Analyst',
      description: 'Create a data analysis expert',
      icon: '📈',
      role: 'analyst',
      estimatedTime: '3 min'
    },
    {
      id: 'documentation-writer',
      name: 'Tech Writer',
      description: 'Create a documentation specialist',
      icon: '📚',
      role: 'writer',
      estimatedTime: '2 min'
    },
    {
      id: 'qa-tester',
      name: 'QA Tester',
      description: 'Create a quality assurance specialist',
      icon: '🔍',
      role: 'qa',
      estimatedTime: '3 min'
    }
  ];
  
  // Filtered and sorted agents
  $: filteredAgents = agents
    .filter(agent => {
      // Advanced search with operators
      if (searchQuery.trim()) {
        if (!matchesAdvancedSearch(agent, searchQuery)) {
          return false;
        }
      }
      
      // Basic filter
      if (!matchesBasicFilter(agent, selectedFilter)) {
        return false;
      }
      
      // Advanced filters
      if (showAdvancedFilters && !matchesAdvancedFilters(agent, advancedFilters)) {
        return false;
      }
      
      return true;
    })
    .sort((a, b) => {
      let aValue, bValue;
      
      switch (sortBy) {
        case 'name':
          aValue = a.name?.toLowerCase() || '';
          bValue = b.name?.toLowerCase() || '';
          break;
        case 'role':
          aValue = a.role?.toLowerCase() || '';
          bValue = b.role?.toLowerCase() || '';
          break;
        case 'usage':
          aValue = a.usage?.totalRequests || 0;
          bValue = b.usage?.totalRequests || 0;
          break;
        case 'performance':
          aValue = a.usage?.successRate || 0;
          bValue = b.usage?.successRate || 0;
          break;
        case 'lastUsed':
          aValue = a.usage?.lastUsed ? new Date(a.usage.lastUsed).getTime() : 0;
          bValue = b.usage?.lastUsed ? new Date(b.usage.lastUsed).getTime() : 0;
          break;
        case 'created':
        default:
          aValue = new Date(a.created || 0).getTime();
          bValue = new Date(b.created || 0).getTime();
          break;
      }
      
      if (typeof aValue === 'string') {
        return sortOrder === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
      } else {
        return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
      }
    });
  
  // Update stats when agents change
  $: {
    agentStats = calculateStats(agents);
  }
  
  function calculateStats(agentList: any[]): AgentStats {
    const stats = {
      total: agentList.length,
      active: agentList.filter(a => a.status === 'active').length,
      inactive: agentList.filter(a => a.status === 'inactive').length,
      draft: agentList.filter(a => a.status === 'draft').length,
      totalRequests: agentList.reduce((sum, a) => sum + (a.usage?.totalRequests || 0), 0),
      avgResponseTime: 0,
      successRate: 0
    };
    
    const activeAgents = agentList.filter(a => a.usage);
    if (activeAgents.length > 0) {
      stats.avgResponseTime = Math.round(
        activeAgents.reduce((sum, a) => sum + (a.usage?.avgResponseTime || 0), 0) / activeAgents.length
      );
      stats.successRate = Math.round(
        activeAgents.reduce((sum, a) => sum + (a.usage?.successRate || 0), 0) / activeAgents.length
      );
    }
    
    return stats;
  }
  
  function openCreateModal() {
    showCreateModal = true;
  }
  
  function closeCreateModal() {
    showCreateModal = false;
  }
  
  // Moved to end of file to avoid duplicate
  
  function handleQuickAction(action) {
    dispatch('quickAction', { action });
  }
  
  function handleBulkAction(action: string) {
    dispatch('bulkAction', { action, agents: selectedAgents });
    selectedAgents = [];
    bulkActions = false;
  }
  
  function toggleAgentSelection(agentId: string) {
    if (selectedAgents.includes(agentId)) {
      selectedAgents = selectedAgents.filter(id => id !== agentId);
    } else {
      selectedAgents = [...selectedAgents, agentId];
    }
  }
  
  function selectAllAgents() {
    selectedAgents = filteredAgents.map(a => a.id);
  }
  
  function clearSelection() {
    selectedAgents = [];
  }
  
  // Advanced search functions
  function matchesAdvancedSearch(agent: any, query: string): boolean {
    const lowerQuery = query.toLowerCase();
    
    // Check for search operators
    const operatorMatch = lowerQuery.match(/^(\w+):(.*?)$/);
    if (operatorMatch) {
      const [, operator, value] = operatorMatch;
      return matchesOperator(agent, operator, value.trim());
    }
    
    // Multiple search terms (AND logic)
    const terms = lowerQuery.split(' ').filter(term => term.length > 0);
    return terms.every(term => matchesSimpleSearch(agent, term));
  }
  
  function matchesOperator(agent: any, operator: string, value: string): boolean {
    switch (operator) {
      case 'name':
        return agent.name?.toLowerCase().includes(value);
      case 'role':
        return agent.role?.toLowerCase().includes(value);
      case 'model':
        return agent.model?.toLowerCase().includes(value);
      case 'team':
        return agent.team?.toLowerCase().includes(value);
      case 'tag':
        return agent.metadata?.tags?.some(tag => tag.toLowerCase().includes(value));
      case 'status':
        return agent.status?.toLowerCase().includes(value);
      case 'created':
        return agent.created?.includes(value);
      case 'usage':
        const usageValue = parseInt(value);
        return !isNaN(usageValue) && (agent.usage?.totalRequests || 0) >= usageValue;
      default:
        return false;
    }
  }
  
  function matchesSimpleSearch(agent: any, term: string): boolean {
    return (
      agent.name?.toLowerCase().includes(term) ||
      agent.role?.toLowerCase().includes(term) ||
      agent.model?.toLowerCase().includes(term) ||
      agent.team?.toLowerCase().includes(term) ||
      agent.capabilities?.some(cap => cap.toLowerCase().includes(term)) ||
      agent.metadata?.description?.toLowerCase().includes(term) ||
      agent.metadata?.tags?.some(tag => tag.toLowerCase().includes(term))
    );
  }
  
  function matchesBasicFilter(agent: any, filter: string): boolean {
    if (filter === 'all') return true;
    if (filter === 'active') return agent.status === 'active';
    if (filter === 'inactive') return agent.status === 'inactive';
    if (filter === 'draft') return agent.status === 'draft';
    if (filter === 'error') return agent.status === 'error';
    
    // Role filters
    return agent.role?.toLowerCase() === filter;
  }
  
  function matchesAdvancedFilters(agent: any, filters: any): boolean {
    // Date range filter
    if (filters.dateRange.start || filters.dateRange.end) {
      const created = new Date(agent.created);
      if (filters.dateRange.start && created < new Date(filters.dateRange.start)) return false;
      if (filters.dateRange.end && created > new Date(filters.dateRange.end)) return false;
    }
    
    // Usage range filter
    const usage = agent.usage?.totalRequests || 0;
    if (usage < filters.usageRange.min || usage > filters.usageRange.max) return false;
    
    // Model filter
    if (filters.models.length > 0 && !filters.models.includes(agent.model)) return false;
    
    // Team filter
    if (filters.teams.length > 0 && !filters.teams.includes(agent.team)) return false;
    
    // Tags filter
    if (filters.tags.length > 0) {
      const agentTags = agent.metadata?.tags || [];
      if (!filters.tags.some(tag => agentTags.includes(tag))) return false;
    }
    
    // Performance threshold
    const performance = agent.usage?.successRate || 0;
    if (performance < filters.performanceThreshold) return false;
    
    // Last used days
    if (filters.lastUsedDays > 0 && agent.usage?.lastUsed) {
      const lastUsed = new Date(agent.usage.lastUsed);
      const daysSinceLastUsed = (new Date().getTime() - lastUsed.getTime()) / (1000 * 60 * 60 * 24);
      if (daysSinceLastUsed > filters.lastUsedDays) return false;
    }
    
    return true;
  }
  
  function saveCurrentFilter() {
    if (!currentFilterName.trim()) return;
    
    const filter = {
      name: currentFilterName,
      query: searchQuery,
      basicFilter: selectedFilter,
      advancedFilters: { ...advancedFilters },
      sortBy,
      sortOrder,
      created: new Date().toISOString()
    };
    
    savedFilters = [...savedFilters, filter];
    currentFilterName = '';
    localStorage.setItem('agentDashboardFilters', JSON.stringify(savedFilters));
  }
  
  function loadSavedFilter(filter: any) {
    searchQuery = filter.query || '';
    selectedFilter = filter.basicFilter || 'all';
    advancedFilters = { ...advancedFilters, ...filter.advancedFilters };
    sortBy = filter.sortBy || 'created';
    sortOrder = filter.sortOrder || 'desc';
  }
  
  function deleteSavedFilter(index: number) {
    savedFilters = savedFilters.filter((_, i) => i !== index);
    localStorage.setItem('agentDashboardFilters', JSON.stringify(savedFilters));
  }
  
  function clearAllFilters() {
    searchQuery = '';
    selectedFilter = 'all';
    advancedFilters = {
      dateRange: { start: '', end: '' },
      usageRange: { min: 0, max: 1000 },
      models: [],
      teams: [],
      tags: [],
      performanceThreshold: 0,
      lastUsedDays: 30
    };
    showAdvancedFilters = false;
  }
  
  function exportFilteredAgents() {
    const exportData = {
      agents: filteredAgents,
      filters: {
        query: searchQuery,
        basicFilter: selectedFilter,
        advancedFilters: showAdvancedFilters ? advancedFilters : null,
        sortBy,
        sortOrder
      },
      exportDate: new Date().toISOString(),
      totalCount: filteredAgents.length
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `filtered-agents-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
  
  // Get available filter options dynamically
  $: availableModels = [...new Set(agents.map(a => a.model).filter(Boolean))];
  $: availableTeams = [...new Set(agents.map(a => a.team).filter(Boolean))];
  $: availableTags = [...new Set(agents.flatMap(a => a.metadata?.tags || []))];
  
  // Load saved filters on mount
  onMount(() => {
    const saved = localStorage.getItem('agentDashboardFilters');
    if (saved) {
      try {
        savedFilters = JSON.parse(saved);
      } catch (error) {
        console.error('Error loading saved filters:', error);
      }
    }
  });
  
  function handleAgentAction(event) {
    dispatch('agentAction', event.detail);
  }
  
  function exportAgents() {
    const exportData = {
      agents: agents.map(agent => ({
        ...agent,
        exported: new Date().toISOString()
      })),
      stats: agentStats,
      exportDate: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `agents-export-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    dispatch('export', { count: agents.length });
  }
  
  function importAgents() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const data = JSON.parse(e.target?.result as string);
            dispatch('import', { data });
          } catch (error) {
            dispatch('importError', { error });
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  }
  
  onMount(() => {
    // Initialize stats
    agentStats = calculateStats(agents);
    
    // Close dropdown when clicking outside
    document.addEventListener('click', (e) => {
      if (!e.target.closest('.creation-controls')) {
        showCreationOptions = false;
      }
    });
  });
  
  function resetCreationState() {
    showAdvancedCreation = false;
    showCreationOptions = false;
  }
  
  function handleAgentCreated(event) {
    const { agent } = event.detail;
    dispatch('agentCreated', { agent });
    resetCreationState();
    closeCreateModal();
  }
</script>

<div class="agent-dashboard">
  <!-- Dashboard Header -->
  <div class="dashboard-header">
    <div class="header-title">
      <h1>Agent Dashboard</h1>
      <p class="header-subtitle">Manage and monitor your AI agents</p>
    </div>
    
    <div class="header-actions">
      {#if showCreateButton}
        <div class="creation-controls">
          <button 
            type="button" 
            class="btn-primary create-btn"
            class:loading={isCreating}
            on:click={openCreateModal}
            disabled={isCreating}
          >
            <span class="btn-icon">{isCreating ? '⏳' : '+'}</span>
            {isCreating ? 'Creating...' : 'Create Agent'}
          </button>
          
          <button 
            type="button" 
            class="btn-secondary dropdown-btn"
            on:click={() => showCreationOptions = !showCreationOptions}
          >
            <span class="btn-icon">▼</span>
          </button>
          
          {#if showCreationOptions}
            <div class="creation-dropdown">
              <button class="dropdown-item" on:click={() => { showAdvancedCreation = true; openCreateModal(); }}>
                <span class="dropdown-icon">⚙️</span>
                Advanced Creation
              </button>
              <button class="dropdown-item" on:click={() => dispatch('duplicateAgent', { agentId: null })}>
                <span class="dropdown-icon">📋</span>
                Clone Existing
              </button>
              <button class="dropdown-item" on:click={() => dispatch('createFromTemplate')}>
                <span class="dropdown-icon">📄</span>
                From Template
              </button>
              <button class="dropdown-item" on:click={() => dispatch('bulkCreate')}>
                <span class="dropdown-icon">🔢</span>
                Bulk Creation
              </button>
            </div>
          {/if}
        </div>
      {/if}
      
      <div class="header-menu">
        {#if agents.length > 0}
          <button 
            type="button" 
            class="btn-secondary"
            class:active={bulkActions}
            on:click={() => bulkActions = !bulkActions}
          >
            <span class="btn-icon">☑️</span>
            Bulk Actions
          </button>
        {/if}
        
        <button type="button" class="btn-secondary" on:click={exportAgents}>
          <span class="btn-icon">📤</span>
          Export
        </button>
        <button type="button" class="btn-secondary" on:click={importAgents}>
          <span class="btn-icon">📥</span>
          Import
        </button>
      </div>
    </div>
  </div>
  
  <!-- Stats Overview -->
  {#if showStats}
    <div class="stats-overview">
      <div class="stat-card">
        <div class="stat-icon">🤖</div>
        <div class="stat-info">
          <span class="stat-value">{agentStats.total}</span>
          <span class="stat-label">Total Agents</span>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">🟢</div>
        <div class="stat-info">
          <span class="stat-value">{agentStats.active}</span>
          <span class="stat-label">Active</span>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">📊</div>
        <div class="stat-info">
          <span class="stat-value">{agentStats.totalRequests.toLocaleString()}</span>
          <span class="stat-label">Total Requests</span>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">⚡</div>
        <div class="stat-info">
          <span class="stat-value">{agentStats.avgResponseTime}ms</span>
          <span class="stat-label">Avg Response</span>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">✅</div>
        <div class="stat-info">
          <span class="stat-value">{agentStats.successRate}%</span>
          <span class="stat-label">Success Rate</span>
        </div>
      </div>
    </div>
  {/if}
  
  <!-- Quick Actions -->
  {#if showQuickActions && agents.length === 0}
    <div class="quick-actions">
      <h3>Quick Start</h3>
      <p class="quick-actions-subtitle">Get started with pre-configured agent templates</p>
      
      <div class="quick-actions-grid">
        {#each quickActions as action}
          <button 
            type="button" 
            class="quick-action-card"
            on:click={() => handleQuickAction(action)}
          >
            <div class="action-icon">{action.icon}</div>
            <div class="action-info">
              <h4>{action.name}</h4>
              <p>{action.description}</p>
              <span class="action-time">~{action.estimatedTime}</span>
            </div>
          </button>
        {/each}
      </div>
    </div>
  {/if}
  
  <!-- Bulk Actions Bar -->
  {#if bulkActions && selectedAgents.length > 0}
    <div class="bulk-actions-bar">
      <div class="bulk-info">
        <span class="bulk-count">{selectedAgents.length}</span>
        <span class="bulk-label">agents selected</span>
      </div>
      
      <div class="bulk-controls">
        <button class="bulk-btn" on:click={() => handleBulkAction('start')}>Start All</button>
        <button class="bulk-btn" on:click={() => handleBulkAction('stop')}>Stop All</button>
        <button class="bulk-btn" on:click={() => handleBulkAction('delete')}>Delete All</button>
        <button class="bulk-btn" on:click={() => handleBulkAction('export')}>Export Selected</button>
        <button class="bulk-btn secondary" on:click={clearSelection}>Clear Selection</button>
      </div>
    </div>
  {/if}
  
  <!-- Advanced Filters -->
  {#if showAdvancedFilters}
    <div class="advanced-filters">
      <div class="filters-header">
        <h3>Advanced Filters</h3>
        <div class="filters-actions">
          <button class="filter-action-btn" on:click={clearAllFilters}>Clear All</button>
          <button class="filter-action-btn" on:click={() => showAdvancedFilters = false}>Close</button>
        </div>
      </div>
      
      <div class="filters-content">
        <!-- Date Range Filter -->
        <div class="filter-group">
          <label>Created Date Range</label>
          <div class="date-range">
            <input type="date" bind:value={advancedFilters.dateRange.start} />
            <span>to</span>
            <input type="date" bind:value={advancedFilters.dateRange.end} />
          </div>
        </div>
        
        <!-- Usage Range Filter -->
        <div class="filter-group">
          <label>Usage Range (Requests)</label>
          <div class="range-inputs">
            <input type="number" bind:value={advancedFilters.usageRange.min} min="0" placeholder="Min" />
            <span>to</span>
            <input type="number" bind:value={advancedFilters.usageRange.max} min="0" placeholder="Max" />
          </div>
        </div>
        
        <!-- Model Filter -->
        <div class="filter-group">
          <label>Models</label>
          <div class="multi-select">
            {#each availableModels as model}
              <label class="checkbox-label">
                <input type="checkbox" bind:group={advancedFilters.models} value={model} />
                {model}
              </label>
            {/each}
          </div>
        </div>
        
        <!-- Team Filter -->
        <div class="filter-group">
          <label>Teams</label>
          <div class="multi-select">
            {#each availableTeams as team}
              <label class="checkbox-label">
                <input type="checkbox" bind:group={advancedFilters.teams} value={team} />
                {team}
              </label>
            {/each}
          </div>
        </div>
        
        <!-- Tags Filter -->
        <div class="filter-group">
          <label>Tags</label>
          <div class="multi-select">
            {#each availableTags as tag}
              <label class="checkbox-label">
                <input type="checkbox" bind:group={advancedFilters.tags} value={tag} />
                {tag}
              </label>
            {/each}
          </div>
        </div>
        
        <!-- Performance Threshold -->
        <div class="filter-group">
          <label>Minimum Success Rate (%)</label>
          <input type="range" bind:value={advancedFilters.performanceThreshold} min="0" max="100" />
          <span class="range-value">{advancedFilters.performanceThreshold}%</span>
        </div>
        
        <!-- Last Used Days -->
        <div class="filter-group">
          <label>Last Used Within (Days)</label>
          <input type="number" bind:value={advancedFilters.lastUsedDays} min="1" max="365" />
        </div>
      </div>
      
      <!-- Save Filter -->
      <div class="save-filter">
        <input
          type="text"
          bind:value={currentFilterName}
          placeholder="Filter name..."
          class="filter-name-input"
        />
        <button class="save-btn" on:click={saveCurrentFilter} disabled={!currentFilterName.trim()}>
          Save Filter
        </button>
      </div>
      
      <!-- Saved Filters -->
      {#if savedFilters.length > 0}
        <div class="saved-filters">
          <h4>Saved Filters</h4>
          <div class="saved-filter-list">
            {#each savedFilters as filter, index}
              <div class="saved-filter-item">
                <span class="filter-name">{filter.name}</span>
                <div class="filter-actions">
                  <button class="load-btn" on:click={() => loadSavedFilter(filter)}>Load</button>
                  <button class="delete-btn" on:click={() => deleteSavedFilter(index)}>Delete</button>
                </div>
              </div>
            {/each}
          </div>
        </div>
      {/if}
    </div>
  {/if}
  
  <!-- Filters and Search -->
  {#if showFilters && agents.length > 0}
    <div class="filters-bar">
      <div class="filters-left">
        <!-- Bulk Selection -->
        {#if bulkActions}
          <div class="bulk-selection">
            <button class="select-btn" on:click={selectAllAgents}>Select All</button>
            <button class="select-btn" on:click={clearSelection}>Clear</button>
          </div>
        {/if}
        
        <!-- Search -->
        <div class="search-container">
          <input
            type="text"
            placeholder="Search agents... (try name:myagent, role:coder, status:active)"
            bind:value={searchQuery}
            class="search-input"
            title="Use operators like name:, role:, model:, team:, tag:, status:, created:, usage:"
          />
          <div class="search-icon">🔍</div>
          
          {#if searchQuery}
            <button class="clear-search" on:click={() => searchQuery = ''}>×</button>
          {/if}
        </div>
        
        <!-- Filters -->
        <div class="filter-tabs">
          {#each filterOptions as filter}
            <button
              type="button"
              class="filter-tab"
              class:active={selectedFilter === filter.value}
              on:click={() => selectedFilter = filter.value}
            >
              <span class="filter-icon">{filter.icon}</span>
              <span class="filter-label">{filter.label}</span>
              {#if filter.value !== 'all'}
                <span class="filter-count">
                  {filter.value === 'active' ? agentStats.active :
                   filter.value === 'inactive' ? agentStats.inactive :
                   filter.value === 'draft' ? agentStats.draft :
                   agents.filter(a => a.role === filter.value).length}
                </span>
              {/if}
            </button>
          {/each}
        </div>
      </div>
      
      <div class="filters-right">
        <!-- Sort -->
        <div class="sort-container">
          <select bind:value={sortBy} class="sort-select">
            {#each sortOptions as option}
              <option value={option.value}>{option.label}</option>
            {/each}
          </select>
          <button
            type="button"
            class="sort-order-btn"
            on:click={() => sortOrder = sortOrder === 'asc' ? 'desc' : 'asc'}
          >
            {sortOrder === 'asc' ? '↑' : '↓'}
          </button>
        </div>
        
        <!-- Layout -->
        <div class="layout-controls">
          <button
            type="button"
            class="layout-btn"
            class:active={layout === 'grid'}
            on:click={() => layout = 'grid'}
          >
            ⊞
          </button>
          <button
            type="button"
            class="layout-btn"
            class:active={layout === 'list'}
            on:click={() => layout = 'list'}
          >
            ☰
          </button>
          
          <button
            type="button"
            class="filter-btn"
            class:active={showAdvancedFilters}
            on:click={() => showAdvancedFilters = !showAdvancedFilters}
            title="Advanced Filters"
          >
            🔧
          </button>
          
          {#if filteredAgents.length > 0}
            <button
              type="button"
              class="filter-btn"
              on:click={exportFilteredAgents}
              title="Export Filtered Results"
            >
              📤
            </button>
          {/if}
        </div>
      </div>
    </div>
  {/if}
  
  <!-- Agents Display -->
  <div class="agents-container">
    {#if filteredAgents.length > 0}
      <div class="agents-grid {layout}">
        {#each filteredAgents as agent (agent.id)}
          <AgentCard 
            {agent} 
            {layout}
            showCheckbox={bulkActions}
            selected={selectedAgents.includes(agent.id)}
            on:action={handleAgentAction}
            on:select={() => toggleAgentSelection(agent.id)}
          />
        {/each}
      </div>
      
      <!-- Results Info -->
      <div class="results-info">
        <span>
          Showing {filteredAgents.length} of {agents.length} agents
          {#if searchQuery}
            for "{searchQuery}"
          {/if}
          {#if selectedFilter !== 'all'}
            in {filterOptions.find(f => f.value === selectedFilter)?.label}
          {/if}
        </span>
      </div>
      
    {:else if agents.length > 0}
      <!-- No filtered results -->
      <div class="no-results">
        <div class="no-results-icon">🔍</div>
        <h3>No agents found</h3>
        <p>Try adjusting your search or filter criteria</p>
        <button 
          type="button" 
          class="btn-secondary"
          on:click={() => { searchQuery = ''; selectedFilter = 'all'; }}
        >
          Clear Filters
        </button>
      </div>
      
    {:else}
      <!-- No agents at all -->
      <div class="empty-state">
        <div class="empty-icon">🤖</div>
        <h2>No agents yet</h2>
        <p>Create your first AI agent to get started with automated assistance</p>
        {#if showCreateButton}
          <button 
            type="button" 
            class="btn-primary"
            on:click={openCreateModal}
          >
            Create Your First Agent
          </button>
        {/if}
      </div>
    {/if}
  </div>
</div>

<!-- Create Agent Modal -->
{#if showCreateModal}
  <div class="modal-overlay" on:click={closeCreateModal}>
    <div class="modal-content" on:click|stopPropagation>
      <AgentCreator
        advanced={showAdvancedCreation}
        on:close={closeCreateModal}
        on:agentCreated={handleAgentCreated}
      />
    </div>
  </div>
{/if}

<style>
  .agent-dashboard {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: #ffffff;
    background: #0f172a;
    min-height: 100vh;
    padding: 24px;
  }
  
  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #1e293b;
  }
  
  .header-title h1 {
    margin: 0 0 4px 0;
    font-size: 28px;
    font-weight: 700;
    color: #ffffff;
  }
  
  .header-subtitle {
    margin: 0;
    color: #64748b;
    font-size: 16px;
  }
  
  .header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .creation-controls {
    position: relative;
    display: flex;
    align-items: center;
  }
  
  .dropdown-btn {
    padding: 12px 8px;
    border-left: none;
    border-radius: 0 8px 8px 0;
  }
  
  .create-btn {
    border-radius: 8px 0 0 8px;
  }
  
  .create-btn.loading {
    opacity: 0.7;
    cursor: not-allowed;
  }
  
  .creation-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: #1e293b;
    border: 1px solid #334155;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    min-width: 200px;
    z-index: 10;
    margin-top: 4px;
  }
  
  .dropdown-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: transparent;
    border: none;
    color: #e2e8f0;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;
    width: 100%;
    text-align: left;
  }
  
  .dropdown-item:hover {
    background: #334155;
  }
  
  .dropdown-item:first-child {
    border-radius: 7px 7px 0 0;
  }
  
  .dropdown-item:last-child {
    border-radius: 0 0 7px 7px;
  }
  
  .dropdown-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
  }
  
  .create-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
  }
  
  .header-menu {
    display: flex;
    gap: 8px;
  }
  
  .btn-primary {
    background: #3b82f6;
    border: 1px solid #3b82f6;
    color: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .btn-primary:hover {
    background: #2563eb;
    border-color: #2563eb;
  }
  
  .btn-secondary {
    background: #1e293b;
    border: 1px solid #334155;
    color: #e2e8f0;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 6px;
  }
  
  .btn-secondary:hover {
    background: #334155;
  }
  
  .btn-secondary.active {
    background: #3b82f6;
    border-color: #3b82f6;
    color: #ffffff;
  }
  
  .btn-icon {
    font-size: 14px;
  }
  
  /* Stats Overview */
  .stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
  }
  
  .stat-card {
    background: #1e293b;
    border: 1px solid #334155;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .stat-icon {
    font-size: 24px;
    width: 48px;
    height: 48px;
    background: #0f172a;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .stat-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }
  
  .stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #ffffff;
    line-height: 1;
  }
  
  .stat-label {
    font-size: 14px;
    color: #64748b;
  }
  
  /* Bulk Actions */
  .bulk-actions-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #3b82f6;
    border-radius: 8px;
    margin-bottom: 16px;
  }
  
  .bulk-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #ffffff;
  }
  
  .bulk-count {
    font-weight: 700;
    font-size: 16px;
  }
  
  .bulk-label {
    font-size: 14px;
  }
  
  .bulk-controls {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .bulk-btn {
    padding: 6px 12px;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 13px;
    font-weight: 500;
  }
  
  .bulk-btn:hover {
    background: rgba(255, 255, 255, 0.3);
  }
  
  .bulk-btn.secondary {
    background: transparent;
    border-color: rgba(255, 255, 255, 0.5);
  }
  
  .bulk-selection {
    display: flex;
    gap: 8px;
    margin-right: 16px;
  }
  
  .select-btn {
    padding: 6px 12px;
    background: #1e293b;
    border: 1px solid #334155;
    border-radius: 4px;
    color: #e2e8f0;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 13px;
  }
  
  .select-btn:hover {
    background: #334155;
  }
  
  /* Quick Actions */
  .quick-actions {
    background: #1e293b;
    border: 1px solid #334155;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
  }
  
  .quick-actions h3 {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 600;
    color: #ffffff;
  }
  
  .quick-actions-subtitle {
    margin: 0 0 20px 0;
    color: #64748b;
    font-size: 14px;
  }
  
  .quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
  }
  
  .quick-action-card {
    background: #0f172a;
    border: 1px solid #334155;
    border-radius: 8px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 16px;
    text-align: left;
  }
  
  .quick-action-card:hover {
    border-color: #3b82f6;
    transform: translateY(-1px);
  }
  
  .action-icon {
    font-size: 32px;
    width: 56px;
    height: 56px;
    background: #1e293b;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }
  
  .action-info h4 {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
  }
  
  .action-info p {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #64748b;
    line-height: 1.4;
  }
  
  .action-time {
    font-size: 12px;
    color: #3b82f6;
    font-weight: 500;
  }
  
  /* Filters Bar */
  .filters-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    margin-bottom: 24px;
    padding: 16px;
    background: #1e293b;
    border: 1px solid #334155;
    border-radius: 8px;
  }
  
  .filters-left {
    display: flex;
    align-items: center;
    gap: 20px;
    flex: 1;
  }
  
  .filters-right {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .search-container {
    position: relative;
  }
  
  .search-input {
    padding: 8px 36px 8px 12px;
    background: #0f172a;
    border: 1px solid #334155;
    border-radius: 6px;
    color: #ffffff;
    font-size: 14px;
    width: 240px;
    outline: none;
  }
  
  .search-input:focus {
    border-color: #3b82f6;
  }
  
  .search-input::placeholder {
    color: #64748b;
  }
  
  .search-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #64748b;
    font-size: 14px;
  }
  
  .filter-tabs {
    display: flex;
    gap: 4px;
  }
  
  .filter-tab {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background: transparent;
    border: 1px solid transparent;
    border-radius: 6px;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;
  }
  
  .filter-tab:hover {
    background: #0f172a;
    color: #e2e8f0;
  }
  
  .filter-tab.active {
    background: #3b82f6;
    border-color: #3b82f6;
    color: #ffffff;
  }
  
  .filter-icon {
    font-size: 12px;
  }
  
  .filter-count {
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 500;
  }
  
  .sort-container {
    display: flex;
    align-items: center;
    gap: 4px;
  }
  
  .sort-select {
    padding: 6px 8px;
    background: #0f172a;
    border: 1px solid #334155;
    border-radius: 4px;
    color: #ffffff;
    font-size: 13px;
    outline: none;
  }
  
  .sort-order-btn {
    padding: 6px 8px;
    background: #0f172a;
    border: 1px solid #334155;
    border-radius: 4px;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;
  }
  
  .sort-order-btn:hover {
    color: #ffffff;
  }
  
  .layout-controls {
    display: flex;
    gap: 2px;
  }
  
  .layout-btn {
    padding: 6px 8px;
    background: transparent;
    border: 1px solid #334155;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;
  }
  
  .layout-btn:first-child {
    border-radius: 4px 0 0 4px;
  }
  
  .layout-btn:last-child {
    border-radius: 0 4px 4px 0;
  }
  
  .layout-btn:hover {
    color: #ffffff;
  }
  
  .layout-btn.active {
    background: #3b82f6;
    border-color: #3b82f6;
    color: #ffffff;
  }
  
  /* Agents Display */
  .agents-container {
    margin-bottom: 24px;
  }
  
  .agents-grid.grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
  }
  
  .agents-grid.list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .results-info {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #1e293b;
    color: #64748b;
    font-size: 14px;
    text-align: center;
  }
  
  /* Empty States */
  .empty-state, .no-results {
    text-align: center;
    padding: 60px 20px;
    background: #1e293b;
    border: 1px solid #334155;
    border-radius: 12px;
  }
  
  .empty-icon, .no-results-icon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.6;
  }
  
  .empty-state h2, .no-results h3 {
    margin: 0 0 12px 0;
    color: #ffffff;
    font-size: 24px;
    font-weight: 600;
  }
  
  .empty-state p, .no-results p {
    margin: 0 0 24px 0;
    color: #64748b;
    font-size: 16px;
    line-height: 1.5;
  }
  
  /* Modal */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
  }
  
  .modal-content {
    background: #1e293b;
    border: 1px solid #334155;
    border-radius: 12px;
    max-width: 900px;
    max-height: 90vh;
    width: 90vw;
    overflow: hidden;
  }
  
  /* Advanced Filters */
  .advanced-filters {
    background: #1e293b;
    border: 1px solid #334155;
    border-radius: 8px;
    margin-bottom: 16px;
    overflow: hidden;
  }
  
  .filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #334155;
    background: #0f172a;
  }
  
  .filters-header h3 {
    margin: 0;
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
  }
  
  .filters-actions {
    display: flex;
    gap: 8px;
  }
  
  .filter-action-btn {
    padding: 6px 12px;
    background: #374151;
    border: 1px solid #4b5563;
    border-radius: 4px;
    color: #d1d5db;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s;
  }
  
  .filter-action-btn:hover {
    background: #4b5563;
  }
  
  .filters-content {
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }
  
  .filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .filter-group label {
    color: #e2e8f0;
    font-size: 14px;
    font-weight: 500;
  }
  
  .filter-group input[type="date"],
  .filter-group input[type="number"],
  .filter-group input[type="range"] {
    padding: 6px 8px;
    background: #0f172a;
    border: 1px solid #334155;
    border-radius: 4px;
    color: #ffffff;
    font-size: 13px;
    outline: none;
  }
  
  .filter-group input:focus {
    border-color: #3b82f6;
  }
  
  .date-range {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .date-range span {
    color: #94a3b8;
    font-size: 12px;
  }
  
  .range-inputs {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .range-inputs input {
    flex: 1;
  }
  
  .range-inputs span {
    color: #94a3b8;
    font-size: 12px;
  }
  
  .multi-select {
    display: flex;
    flex-direction: column;
    gap: 4px;
    max-height: 120px;
    overflow-y: auto;
  }
  
  .checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .checkbox-label:hover {
    background: #334155;
  }
  
  .checkbox-label input[type="checkbox"] {
    width: 14px;
    height: 14px;
    accent-color: #3b82f6;
  }
  
  .range-value {
    color: #3b82f6;
    font-size: 12px;
    font-weight: 500;
  }
  
  .save-filter {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 20px;
    border-top: 1px solid #334155;
    background: #0f172a;
  }
  
  .filter-name-input {
    flex: 1;
    padding: 8px 12px;
    background: #1e293b;
    border: 1px solid #334155;
    border-radius: 4px;
    color: #ffffff;
    font-size: 14px;
    outline: none;
  }
  
  .filter-name-input:focus {
    border-color: #3b82f6;
  }
  
  .save-btn {
    padding: 8px 16px;
    background: #3b82f6;
    border: 1px solid #3b82f6;
    border-radius: 4px;
    color: #ffffff;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
  }
  
  .save-btn:hover {
    background: #2563eb;
  }
  
  .save-btn:disabled {
    background: #6b7280;
    cursor: not-allowed;
  }
  
  .saved-filters {
    padding: 16px 20px;
    border-top: 1px solid #334155;
  }
  
  .saved-filters h4 {
    margin: 0 0 12px 0;
    color: #ffffff;
    font-size: 14px;
    font-weight: 600;
  }
  
  .saved-filter-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .saved-filter-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #0f172a;
    border-radius: 4px;
  }
  
  .filter-name {
    color: #e2e8f0;
    font-size: 13px;
    font-weight: 500;
  }
  
  .filter-actions {
    display: flex;
    gap: 6px;
  }
  
  .load-btn, .delete-btn {
    padding: 4px 8px;
    border: 1px solid #4b5563;
    border-radius: 3px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .load-btn {
    background: #374151;
    color: #d1d5db;
  }
  
  .load-btn:hover {
    background: #4b5563;
  }
  
  .delete-btn {
    background: #7f1d1d;
    color: #fecaca;
  }
  
  .delete-btn:hover {
    background: #991b1b;
  }
  
  /* Close dropdown when clicking outside */
  @media (max-width: 768px) {
    .creation-controls {
      flex-direction: column;
      align-items: stretch;
    }
    
    .bulk-actions-bar {
      flex-direction: column;
      gap: 12px;
    }
    
    .bulk-controls {
      flex-wrap: wrap;
      justify-content: center;
    }
    
    .filters-content {
      grid-template-columns: 1fr;
    }
    
    .date-range {
      flex-direction: column;
      align-items: stretch;
    }
    
    .range-inputs {
      flex-direction: column;
      align-items: stretch;
    }
    
    .save-filter {
      flex-direction: column;
      align-items: stretch;
    }
  }
</style>