"""
Admin API routes for Metamorphic Reactor
Management endpoints for API keys, rate limits, and safety configuration
"""

from fastapi import APIRouter, HTTPException, Depends, Security
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, <PERSON>
from typing import List, Dict, Any, Optional
from datetime import datetime

from ..services import (
    api_key_manager, rate_limiter, safety_guardian,
    LLMProvider, KeyStatus, LimitType, LimitScope, SafetyLevel
)

# Security
security = HTTPBearer()

def verify_admin_token(credentials: HTTPAuthorizationCredentials = Security(security)):
    """Verify admin access token - implement proper authentication in production"""
    token = credentials.credentials
    # In production, implement proper JWT verification or API key validation
    if token != "admin_token_placeholder":
        raise HTTPException(status_code=403, detail="Invalid admin token")
    return token

# Request/Response models
class AddAPIKeyRequest(BaseModel):
    provider: LLMProvider
    api_key: str = Field(..., min_length=10)
    daily_quota: Optional[int] = None
    monthly_quota: Optional[int] = None
    cost_limit_usd: Optional[float] = None
    priority: int = Field(default=1, ge=1, le=10)
    metadata: Dict[str, Any] = Field(default_factory=dict)

class APIKeyResponse(BaseModel):
    key_id: str
    provider: str
    status: str
    usage_count: int
    daily_quota: Optional[int]
    daily_usage: int
    monthly_quota: Optional[int]
    monthly_usage: int
    cost_limit_usd: Optional[float]
    cost_used_usd: float
    priority: int
    created_at: str

class AddRateLimitRequest(BaseModel):
    limit_id: str
    scope: LimitScope
    scope_id: str
    limit_type: LimitType
    limit_value: float
    window_seconds: int
    burst_allowance: float = 1.2
    adaptive: bool = False
    priority: int = 1

class SafetyConfigRequest(BaseModel):
    strict_mode: Optional[bool] = None
    mask_pii: Optional[bool] = None
    block_threshold: Optional[float] = None
    warning_threshold: Optional[float] = None
    violation_threshold: Optional[int] = None

# Router instances
admin_router = APIRouter()

# API Key Management
@admin_router.post("/api-keys", response_model=dict)
async def add_api_key(
    request: AddAPIKeyRequest,
    _: str = Depends(verify_admin_token)
):
    """Add a new API key to the system"""
    try:
        key_id = await api_key_manager.add_api_key(
            provider=request.provider,
            api_key=request.api_key,
            daily_quota=request.daily_quota,
            monthly_quota=request.monthly_quota,
            cost_limit_usd=request.cost_limit_usd,
            priority=request.priority,
            metadata=request.metadata
        )
        
        return {
            "message": "API key added successfully",
            "key_id": key_id,
            "provider": request.provider.value
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to add API key: {str(e)}")

@admin_router.get("/api-keys", response_model=List[APIKeyResponse])
async def list_api_keys(
    provider: Optional[LLMProvider] = None,
    _: str = Depends(verify_admin_token)
):
    """List all API keys (without exposing actual keys)"""
    try:
        keys = await api_key_manager.list_api_keys(provider=provider)
        
        return [
            APIKeyResponse(
                key_id=key["key_id"],
                provider=key["provider"],
                status=key["status"],
                usage_count=key["usage_count"],
                daily_quota=key["daily_quota"],
                daily_usage=key["daily_usage"],
                monthly_quota=key["monthly_quota"],
                monthly_usage=key["monthly_usage"],
                cost_limit_usd=key["cost_limit_usd"],
                cost_used_usd=key["cost_used_usd"],
                priority=key["priority"],
                created_at=key["created_at"]
            )
            for key in keys
        ]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list API keys: {str(e)}")

@admin_router.delete("/api-keys/{key_id}")
async def remove_api_key(
    key_id: str,
    _: str = Depends(verify_admin_token)
):
    """Remove an API key"""
    try:
        success = await api_key_manager.remove_api_key(key_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="API key not found")
        
        return {"message": f"API key {key_id} removed successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to remove API key: {str(e)}")

@admin_router.get("/api-keys/usage")
async def get_api_usage_stats(
    provider: Optional[LLMProvider] = None,
    days: int = 30,
    _: str = Depends(verify_admin_token)
):
    """Get API usage statistics"""
    try:
        stats = await api_key_manager.get_usage_statistics(provider=provider, days=days)
        return stats
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get usage stats: {str(e)}")

# Rate Limiting Management
@admin_router.post("/rate-limits")
async def add_rate_limit(
    request: AddRateLimitRequest,
    _: str = Depends(verify_admin_token)
):
    """Add a new rate limit"""
    try:
        from ..services.rate_limiter import RateLimit
        
        rate_limit = RateLimit(
            limit_id=request.limit_id,
            scope=request.scope,
            scope_id=request.scope_id,
            limit_type=request.limit_type,
            limit_value=request.limit_value,
            window_seconds=request.window_seconds,
            burst_allowance=request.burst_allowance,
            adaptive=request.adaptive,
            priority=request.priority
        )
        
        success = await rate_limiter.add_rate_limit(rate_limit)
        
        if not success:
            raise HTTPException(status_code=500, detail="Failed to add rate limit")
        
        return {"message": f"Rate limit {request.limit_id} added successfully"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to add rate limit: {str(e)}")

@admin_router.get("/rate-limits/stats")
async def get_rate_limit_stats(
    scope: Optional[LimitScope] = None,
    scope_id: Optional[str] = None,
    hours: int = 24,
    _: str = Depends(verify_admin_token)
):
    """Get rate limiting statistics"""
    try:
        if scope and scope_id:
            stats = await rate_limiter.get_usage_stats(scope, scope_id, hours)
        else:
            stats = await rate_limiter.get_system_status()
        
        return stats
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get rate limit stats: {str(e)}")

@admin_router.post("/rate-limits/reset")
async def reset_rate_limits(
    scope: LimitScope,
    scope_id: str,
    limit_type: Optional[LimitType] = None,
    _: str = Depends(verify_admin_token)
):
    """Reset rate limits for a scope (emergency function)"""
    try:
        await rate_limiter.reset_usage(scope, scope_id, limit_type)
        
        return {
            "message": f"Rate limits reset for {scope.value}:{scope_id}",
            "limit_type": limit_type.value if limit_type else "all"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to reset rate limits: {str(e)}")

@admin_router.post("/rate-limits/system-load")
async def update_system_load(
    load_factor: float = Field(..., ge=0.1, le=10.0),
    _: str = Depends(verify_admin_token)
):
    """Update system load factor for adaptive throttling"""
    try:
        await rate_limiter.update_system_load(load_factor)
        
        return {
            "message": "System load factor updated",
            "load_factor": load_factor
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update system load: {str(e)}")

# Safety Management
@admin_router.post("/safety/config")
async def update_safety_config(
    request: SafetyConfigRequest,
    _: str = Depends(verify_admin_token)
):
    """Update safety configuration"""
    try:
        config_dict = {k: v for k, v in request.dict().items() if v is not None}
        
        await safety_guardian.update_configuration(config_dict)
        
        return {
            "message": "Safety configuration updated",
            "updated_fields": list(config_dict.keys())
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update safety config: {str(e)}")

@admin_router.get("/safety/stats")
async def get_safety_stats(
    days: int = 30,
    _: str = Depends(verify_admin_token)
):
    """Get safety statistics"""
    try:
        stats = await safety_guardian.get_safety_statistics(days=days)
        return stats
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get safety stats: {str(e)}")

@admin_router.post("/safety/check")
async def manual_safety_check(
    content: str = Field(..., min_length=1),
    user_id: Optional[str] = None,
    task_id: Optional[str] = None,
    _: str = Depends(verify_admin_token)
):
    """Perform manual safety check on content"""
    try:
        result = await safety_guardian.check_content_safety(
            content=content,
            user_id=user_id,
            task_id=task_id,
            context={"manual_check": True}
        )
        
        return {
            "allowed": result.allowed,
            "level": result.overall_level.value,
            "checks": [
                {
                    "type": check.check_type.value,
                    "category": check.category.value,
                    "level": check.level.value,
                    "confidence": check.confidence,
                    "message": check.message
                }
                for check in result.checks
            ],
            "filtered_content": result.filtered_content,
            "warnings": result.warnings
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to perform safety check: {str(e)}")

# System Status
@admin_router.get("/system/status")
async def get_system_status(_: str = Depends(verify_admin_token)):
    """Get overall system status"""
    try:
        return {
            "api_key_manager": {
                "total_keys": len(await api_key_manager.list_api_keys()),
                "providers": [provider.value for provider in LLMProvider]
            },
            "rate_limiter": await rate_limiter.get_system_status(),
            "safety_guardian": {
                "strict_mode": safety_guardian.strict_mode,
                "mask_pii": safety_guardian.mask_pii,
                "adaptive_enabled": rate_limiter.adaptive_enabled
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get system status: {str(e)}")

# Emergency Controls
@admin_router.post("/emergency/shutdown")
async def emergency_shutdown(_: str = Depends(verify_admin_token)):
    """Emergency shutdown of all services"""
    try:
        # In a real implementation, this would gracefully shutdown services
        logger.critical("Emergency shutdown initiated by admin")
        
        return {
            "message": "Emergency shutdown initiated",
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to initiate shutdown: {str(e)}")

@admin_router.post("/emergency/enable-strict-mode")
async def enable_strict_mode(_: str = Depends(verify_admin_token)):
    """Enable strict safety mode"""
    try:
        await safety_guardian.update_configuration({"strict_mode": True})
        
        return {
            "message": "Strict safety mode enabled",
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to enable strict mode: {str(e)}")