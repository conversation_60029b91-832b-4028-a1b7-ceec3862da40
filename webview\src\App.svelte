<script lang="ts">
  import { onMount } from 'svelte';
  import TaskInput from './components/TaskInput.svelte';
  import AgentDashboard from './components/AgentDashboard.svelte';
  import ConfigPanel from './components/ConfigPanel.svelte';
  import FileSelector from './components/FileSelector.svelte';
  import { vscodeApi } from './utils/vscode';
  
  let agents: any[] = [];
  let taskRunning = false;
  let showConfig = false;
  let lastError = '';
  let selectedFiles: string[] = [];
  let workspaceInfo: any = null;
  
  // Configuration state
  let maxAgents = 2;
  let apiKeys = {
    gemini: [],
    claude: [],
    openai: []
  };

  onMount(() => {
    // Listen for messages from the extension
    window.addEventListener('message', handleMessage);
    
    // Request initial agent status and workspace info
    vscodeApi.postMessage({ command: 'getAgentStatus' });
    vscodeApi.postMessage({ command: 'getWorkspaceInfo' });
    
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  });

  function handleMessage(event: MessageEvent) {
    const message = event.data;
    
    switch (message.command) {
      case 'updateAgentStatus':
        agents = message.agents || [];
        break;
      case 'taskStarted':
        taskRunning = true;
        lastError = '';
        break;
      case 'taskStopped':
        taskRunning = false;
        break;
      case 'error':
        lastError = message.message;
        taskRunning = false;
        break;
      case 'clearOutput':
        // Clear any output displays
        break;
      case 'workspaceInfo':
        workspaceInfo = message.info;
        break;
      case 'filesSelected':
        selectedFiles = message.files || [];
        break;
    }
  }

  function handleStartTask(event: CustomEvent<string>) {
    lastError = '';
    vscodeApi.postMessage({
      command: 'startTask',
      taskDescription: event.detail,
      contextFiles: selectedFiles
    });
  }

  function handleFilesSelected(event: CustomEvent<string[]>) {
    selectedFiles = event.detail;
  }

  function handleStopTask() {
    vscodeApi.postMessage({
      command: 'stopTask'
    });
  }

  function handleConfigUpdate(event: CustomEvent<any>) {
    const { maxAgents: newMaxAgents, apiKeys: newApiKeys } = event.detail;
    maxAgents = newMaxAgents;
    apiKeys = newApiKeys;
    
    // TODO: Send configuration update to extension
    vscodeApi.postMessage({
      command: 'updateConfig',
      maxAgents,
      apiKeys
    });
    
    showConfig = false;
  }
</script>

<main class="min-h-screen bg-gray-900 text-white p-6">
  <div class="max-w-6xl mx-auto">
    <header class="text-center mb-8">
      <div class="flex justify-between items-start">
        <div class="flex-1">
          <h1 class="text-4xl font-bold mb-2">🧬 Metamorphic Reactor</h1>
          <p class="text-gray-400">Multi-Agent LLM Orchestration with Autonomous Consensus</p>
        </div>
        
        <button
          on:click={() => showConfig = true}
          class="bg-gray-700 hover:bg-gray-600 px-4 py-2 rounded-lg flex items-center gap-2"
          title="Configuration"
        >
          ⚙️ Config
        </button>
      </div>
      
      {#if lastError}
        <div class="bg-red-600 bg-opacity-20 border border-red-600 rounded-lg p-3 mt-4">
          <p class="text-red-200 text-sm">
            <span class="font-semibold">Error:</span> {lastError}
          </p>
        </div>
      {/if}
    </header>

    <FileSelector 
      bind:selectedFiles
      {workspaceInfo}
      on:filesSelected={handleFilesSelected}
    />

    <TaskInput 
      on:startTask={handleStartTask}
      on:stopTask={handleStopTask}
      {taskRunning}
    />

    <AgentDashboard {agents} />
  </div>

  <!-- Configuration Panel -->
  <ConfigPanel 
    bind:isOpen={showConfig}
    {maxAgents}
    {apiKeys}
    on:updateConfig={handleConfigUpdate}
    on:close={() => showConfig = false}
  />
</main>