# Agent Profile JSON Export/Import Documentation

## Overview

The AgentFactory class now supports comprehensive JSON export and import functionality for agent profiles. This enables agent configuration backup, sharing, migration, and version control.

## Features

### Core Export/Import Methods

1. **`export_agent_profile(agent_id: str) -> Dict[str, Any]`**
   - Exports a single agent profile to JSON-compatible dictionary
   - Includes metadata with schema version, timestamp, and agent info
   - Handles datetime serialization automatically

2. **`import_agent_profile(json_data: Dict[str, Any]) -> AgentProfile`**
   - Imports a single agent profile from JSON data
   - Validates schema and data integrity
   - Handles ID collisions by generating new UUIDs
   - Provides comprehensive error handling

3. **`export_agent_profiles_batch(agent_ids: List[str]) -> Dict[str, Any]`**
   - Exports multiple agent profiles in a single operation
   - Includes summary metadata with agent count, names, and roles
   - Efficient batch processing

4. **`import_agent_profiles_batch(json_data: Dict[str, Any]) -> List[AgentProfile]`**
   - Imports multiple agent profiles from JSON data
   - Continues processing even if individual profiles fail
   - Returns list of successfully imported profiles

### File Operations

5. **`export_agent_profiles_to_json_file(agent_ids: List[str], file_path: str) -> bool`**
   - Exports agent profiles directly to JSON file
   - Handles single or batch export automatically
   - UTF-8 encoding with proper formatting

6. **`import_agent_profiles_from_json_file(file_path: str) -> List[AgentProfile]`**
   - Imports agent profiles from JSON file
   - Auto-detects single vs batch format
   - Comprehensive error handling for file operations

### Validation

7. **`validate_agent_profile_json(json_data: Dict[str, Any]) -> Dict[str, Any]`**
   - Validates JSON data without importing
   - Checks schema version compatibility
   - Validates all profile fields
   - Returns detailed validation report

## JSON Schema Structure

### Single Profile Export

```json
{
  "metadata": {
    "schema_version": "1.0.0",
    "export_timestamp": "2024-01-15T10:30:00.123456",
    "export_type": "single_profile",
    "agent_id": "uuid-string",
    "agent_name": "Agent Name",
    "agent_role": "coder"
  },
  "profile": {
    "id": "uuid-string",
    "name": "Agent Name",
    "role": "coder",
    "model": "gpt-4",
    "prompt": "System prompt...",
    "capabilities": ["capability1", "capability2"],
    "team_id": "team-uuid",
    "status": "idle",
    "temperature": 0.7,
    "max_tokens": 2000,
    "max_retries": 3,
    "timeout_seconds": 60,
    "consensus_threshold": 0.8,
    "success_rate": 0.95,
    "avg_response_time": 1.5,
    "tasks_completed": 42,
    "memory": [],
    "created_at": "2024-01-15T10:00:00.123456",
    "updated_at": "2024-01-15T10:30:00.123456"
  }
}
```

### Batch Profile Export

```json
{
  "metadata": {
    "schema_version": "1.0.0",
    "export_timestamp": "2024-01-15T10:30:00.123456",
    "export_type": "batch_profiles",
    "agent_count": 2,
    "agent_ids": ["uuid1", "uuid2"],
    "agent_names": ["Agent 1", "Agent 2"],
    "agent_roles": ["coder", "planner"]
  },
  "profiles": {
    "uuid1": { /* profile data */ },
    "uuid2": { /* profile data */ }
  }
}
```

## Usage Examples

### Basic Export/Import

```python
from agents.agent_factory import AgentFactory

factory = AgentFactory()

# Export single profile
export_data = factory.export_agent_profile("agent_123")

# Import profile
imported_profile = factory.import_agent_profile(export_data)
```

### Batch Operations

```python
# Export multiple profiles
agent_ids = ["agent_1", "agent_2", "agent_3"]
batch_export = factory.export_agent_profiles_batch(agent_ids)

# Import batch
imported_profiles = factory.import_agent_profiles_batch(batch_export)
```

### File Operations

```python
# Export to file
success = factory.export_agent_profiles_to_json_file(
    agent_ids=["agent_1", "agent_2"],
    file_path="/path/to/agents.json"
)

# Import from file
imported_profiles = factory.import_agent_profiles_from_json_file(
    file_path="/path/to/agents.json"
)
```

### Validation

```python
# Validate JSON before importing
validation_result = factory.validate_agent_profile_json(json_data)

if validation_result["valid"]:
    profiles = factory.import_agent_profiles_batch(json_data)
else:
    print(f"Validation errors: {validation_result['errors']}")
```

## Error Handling

### Common Exceptions

- **`ValueError`**: Raised for invalid data, missing agents, or validation failures
- **`FileNotFoundError`**: Raised when import file doesn't exist
- **`json.JSONDecodeError`**: Raised for malformed JSON files

### Error Recovery

The implementation includes robust error handling:

- **ID Collisions**: Automatically generates new UUIDs for conflicting agent IDs
- **Partial Failures**: Batch import continues even if individual profiles fail
- **Schema Mismatches**: Provides warnings for version compatibility issues
- **Data Validation**: Comprehensive validation with detailed error messages

## Versioning and Compatibility

### Schema Version: 1.0.0

The current schema version is `1.0.0`. Future versions will maintain backward compatibility where possible, with appropriate migration handling.

### Version Compatibility

- **Same Version**: Full compatibility guaranteed
- **Different Versions**: Warnings logged, best-effort import attempted
- **Major Version Changes**: May require manual intervention

## Security Considerations

### Data Sanitization

- All imported data is validated against AgentProfile schema
- Datetime fields are properly parsed and validated
- Enum values are validated against allowed options
- No code execution from imported data

### File Operations

- UTF-8 encoding prevents encoding attacks
- File paths are not automatically executed
- JSON parsing is safe from code injection

## Performance Considerations

### Memory Usage

- Large batch operations load all profiles into memory
- Consider chunking for very large profile sets
- Memory usage scales linearly with profile count

### File Size

- JSON export includes all profile data including memory
- Large prompts and memory can significantly increase file size
- Consider compression for large exports

## Logging

All export/import operations are logged with appropriate levels:

- **INFO**: Successful operations with counts and IDs
- **WARNING**: Schema version mismatches, ID collisions
- **ERROR**: Failed operations with detailed error messages

## Best Practices

### Export

1. **Regular Backups**: Export profiles regularly for disaster recovery
2. **Version Control**: Include schema version in filename for tracking
3. **Batch Operations**: Use batch export for multiple profiles
4. **Metadata**: Include meaningful metadata in exports

### Import

1. **Validation First**: Always validate before importing
2. **Test Environment**: Test imports in non-production environments
3. **ID Management**: Be aware of ID collision handling
4. **Error Handling**: Implement proper error handling in applications

### File Management

1. **Naming Convention**: Use descriptive filenames with timestamps
2. **Directory Structure**: Organize exports by date/project
3. **Backup Strategy**: Keep multiple versions of critical profiles
4. **Access Control**: Secure export files containing sensitive data

## Migration Support

The export/import system supports:

- **Environment Migration**: Moving profiles between development/production
- **System Upgrades**: Preserving profiles during system updates
- **Team Sharing**: Sharing configured agents between team members
- **Template Creation**: Creating reusable agent templates

## Future Enhancements

Planned improvements:

1. **Compression**: Optional compression for large exports
2. **Encryption**: Support for encrypted profile exports
3. **Incremental Export**: Export only changed profiles
4. **Cloud Storage**: Direct export to cloud storage services
5. **Migration Tools**: Automated migration between schema versions