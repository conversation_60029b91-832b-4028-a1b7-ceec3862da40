<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { vscodeApi } from '../utils/vscode';
  
  const dispatch = createEventDispatcher<{
    filesSelected: string[];
  }>();

  export let selectedFiles: string[] = [];
  export let workspaceInfo: any = null;

  function selectFiles() {
    vscodeApi.postMessage({
      command: 'selectFiles'
    });
  }

  function removeFile(index: number) {
    selectedFiles = selectedFiles.filter((_, i) => i !== index);
    dispatch('filesSelected', selectedFiles);
  }

  function clearFiles() {
    selectedFiles = [];
    dispatch('filesSelected', selectedFiles);
  }

  // Listen for file selection results
  window.addEventListener('message', (event) => {
    const message = event.data;
    if (message.command === 'filesSelected') {
      selectedFiles = message.files || [];
      dispatch('filesSelected', selectedFiles);
    }
  });
</script>

<div class="bg-gray-800 rounded-lg p-4 mb-4">
  <div class="flex justify-between items-center mb-3">
    <h3 class="text-lg font-semibold">Task Context Files</h3>
    <div class="flex gap-2">
      <button
        on:click={selectFiles}
        class="bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-sm"
      >
        📁 Select Files
      </button>
      {#if selectedFiles.length > 0}
        <button
          on:click={clearFiles}
          class="bg-gray-600 hover:bg-gray-700 px-3 py-1 rounded text-sm"
        >
          Clear All
        </button>
      {/if}
    </div>
  </div>

  {#if workspaceInfo}
    <div class="text-xs text-gray-400 mb-3">
      <div class="flex items-center gap-2">
        <span>📂 {workspaceInfo.projectType || 'Unknown'} project</span>
        {#if workspaceInfo.isGitRepository}
          <span>🔗 Git repository</span>
        {/if}
      </div>
    </div>
  {/if}

  {#if selectedFiles.length > 0}
    <div class="space-y-2">
      {#each selectedFiles as file, index}
        <div class="flex items-center justify-between bg-gray-700 rounded px-3 py-2">
          <div class="flex items-center gap-2">
            <span class="text-blue-400">📄</span>
            <span class="font-mono text-sm">{file}</span>
          </div>
          <button
            on:click={() => removeFile(index)}
            class="text-red-400 hover:text-red-300 text-sm"
          >
            ✕
          </button>
        </div>
      {/each}
    </div>
    
    <div class="text-xs text-gray-400 mt-3">
      {selectedFiles.length} file{selectedFiles.length !== 1 ? 's' : ''} selected
    </div>
  {:else}
    <div class="text-gray-400 text-sm text-center py-4 border-2 border-dashed border-gray-600 rounded">
      No files selected. Click "Select Files" to add context files for the agents.
    </div>
  {/if}
</div>