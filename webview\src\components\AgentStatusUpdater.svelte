<script lang="ts">
  import { createEventDispatcher, onMount, onDestroy } from 'svelte';
  
  const dispatch = createEventDispatcher();
  
  export let agents: any[] = [];
  export let updateInterval: number = 2000; // 2 seconds
  export let enableWebSocket: boolean = true;
  export let enablePolling: boolean = true;
  export let showNotifications: boolean = true;
  export let maxRetries: number = 3;
  
  interface AgentStatusUpdate {
    id: string;
    status: 'active' | 'inactive' | 'error' | 'starting' | 'stopping';
    timestamp: string;
    message?: string;
    metrics?: {
      cpuUsage?: number;
      memoryUsage?: number;
      requestCount?: number;
      errorCount?: number;
      lastRequest?: string;
    };
    health?: {
      status: 'healthy' | 'degraded' | 'unhealthy';
      uptime?: number;
      responseTime?: number;
      errors?: string[];
    };
  }
  
  interface SystemEvent {
    type: 'agent_created' | 'agent_deleted' | 'agent_updated' | 'system_alert';
    agentId?: string;
    data: any;
    timestamp: string;
  }
  
  let websocket: WebSocket | null = null;
  let pollInterval: NodeJS.Timeout | null = null;
  let reconnectAttempts = 0;
  let connectionStatus: 'connected' | 'disconnected' | 'connecting' | 'error' = 'disconnected';
  let lastUpdateTime: string = '';
  let updateQueue: AgentStatusUpdate[] = [];
  let notificationQueue: SystemEvent[] = [];
  
  // Connection states
  let wsUrl = 'ws://localhost:8080/ws/agents';
  let isReconnecting = false;
  let heartbeatInterval: NodeJS.Timeout | null = null;
  
  // Statistics
  let stats = {
    totalUpdates: 0,
    successfulUpdates: 0,
    failedUpdates: 0,
    averageResponseTime: 0,
    lastErrorTime: null as string | null,
    connectionUptime: 0
  };
  
  // Real-time agent monitoring
  let agentMetrics: Record<string, any> = {};
  let systemAlerts: any[] = [];
  
  onMount(() => {
    if (enableWebSocket) {
      initWebSocket();
    }
    
    if (enablePolling) {
      startPolling();
    }
    
    // Start connection uptime tracking
    const uptimeStart = Date.now();
    const uptimeInterval = setInterval(() => {
      if (connectionStatus === 'connected') {
        stats.connectionUptime = Date.now() - uptimeStart;
      }
    }, 1000);
    
    return () => {
      clearInterval(uptimeInterval);
    };
  });
  
  onDestroy(() => {
    cleanup();
  });
  
  function initWebSocket() {
    if (websocket && websocket.readyState === WebSocket.OPEN) {
      return;
    }
    
    connectionStatus = 'connecting';
    
    try {
      websocket = new WebSocket(wsUrl);
      
      websocket.onopen = handleWebSocketOpen;
      websocket.onmessage = handleWebSocketMessage;
      websocket.onclose = handleWebSocketClose;
      websocket.onerror = handleWebSocketError;
      
    } catch (error) {
      console.error('Failed to initialize WebSocket:', error);
      connectionStatus = 'error';
      scheduleReconnect();
    }
  }
  
  function handleWebSocketOpen(event: Event) {
    console.log('WebSocket connected');
    connectionStatus = 'connected';
    reconnectAttempts = 0;
    isReconnecting = false;
    
    // Send initial subscription message
    sendWebSocketMessage({
      type: 'subscribe',
      agentIds: agents.map(a => a.id),
      includeMetrics: true,
      includeHealth: true
    });
    
    // Start heartbeat
    startHeartbeat();
    
    dispatch('connectionStateChanged', { status: connectionStatus });
  }
  
  function handleWebSocketMessage(event: MessageEvent) {
    try {
      const data = JSON.parse(event.data);
      processRealtimeUpdate(data);
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
    }
  }
  
  function handleWebSocketClose(event: CloseEvent) {
    console.log('WebSocket disconnected:', event.code, event.reason);
    connectionStatus = 'disconnected';
    stopHeartbeat();
    
    dispatch('connectionStateChanged', { status: connectionStatus });
    
    if (!isReconnecting && event.code !== 1000) {
      scheduleReconnect();
    }
  }
  
  function handleWebSocketError(event: Event) {
    console.error('WebSocket error:', event);
    connectionStatus = 'error';
    stats.lastErrorTime = new Date().toISOString();
    stats.failedUpdates++;
    
    dispatch('connectionStateChanged', { status: connectionStatus });
  }
  
  function scheduleReconnect() {
    if (isReconnecting || reconnectAttempts >= maxRetries) {
      return;
    }
    
    isReconnecting = true;
    reconnectAttempts++;
    
    const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000);
    
    setTimeout(() => {
      if (reconnectAttempts <= maxRetries) {
        console.log(`Attempting to reconnect (${reconnectAttempts}/${maxRetries})...`);
        initWebSocket();
      }
    }, delay);
  }
  
  function startHeartbeat() {
    heartbeatInterval = setInterval(() => {
      if (websocket && websocket.readyState === WebSocket.OPEN) {
        sendWebSocketMessage({ type: 'ping' });
      }
    }, 30000); // 30 seconds
  }
  
  function stopHeartbeat() {
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval);
      heartbeatInterval = null;
    }
  }
  
  function sendWebSocketMessage(message: any) {
    if (websocket && websocket.readyState === WebSocket.OPEN) {
      websocket.send(JSON.stringify(message));
    }
  }
  
  function startPolling() {
    if (pollInterval) {
      return;
    }
    
    pollInterval = setInterval(async () => {
      if (connectionStatus !== 'connected' || !enablePolling) {
        await pollAgentStatus();
      }
    }, updateInterval);
  }
  
  function stopPolling() {
    if (pollInterval) {
      clearInterval(pollInterval);
      pollInterval = null;
    }
  }
  
  async function pollAgentStatus() {
    try {
      const startTime = Date.now();
      
      // Simulate API call to get agent statuses
      const updates = await simulateAgentStatusCall();
      
      const responseTime = Date.now() - startTime;
      stats.averageResponseTime = (stats.averageResponseTime + responseTime) / 2;
      stats.successfulUpdates++;
      
      updates.forEach(update => {
        processStatusUpdate(update);
      });
      
      lastUpdateTime = new Date().toISOString();
      
    } catch (error) {
      console.error('Failed to poll agent status:', error);
      stats.failedUpdates++;
      stats.lastErrorTime = new Date().toISOString();
    }
  }
  
  async function simulateAgentStatusCall(): Promise<AgentStatusUpdate[]> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));
    
    return agents.map(agent => {
      const statusOptions = ['active', 'inactive', 'error'] as const;
      const randomStatus = agent.status || statusOptions[Math.floor(Math.random() * statusOptions.length)];
      
      return {
        id: agent.id,
        status: randomStatus,
        timestamp: new Date().toISOString(),
        message: getStatusMessage(randomStatus),
        metrics: {
          cpuUsage: Math.round(Math.random() * 100),
          memoryUsage: Math.round(Math.random() * 100),
          requestCount: Math.floor(Math.random() * 1000),
          errorCount: Math.floor(Math.random() * 10),
          lastRequest: new Date(Date.now() - Math.random() * 3600000).toISOString()
        },
        health: {
          status: randomStatus === 'error' ? 'unhealthy' : 
                  randomStatus === 'active' ? 'healthy' : 'degraded',
          uptime: Math.floor(Math.random() * 86400),
          responseTime: Math.round(50 + Math.random() * 500),
          errors: randomStatus === 'error' ? ['Connection timeout', 'Memory limit exceeded'] : []
        }
      };
    });
  }
  
  function processRealtimeUpdate(data: any) {
    switch (data.type) {
      case 'status_update':
        processStatusUpdate(data.payload);
        break;
      case 'metrics_update':
        processMetricsUpdate(data.payload);
        break;
      case 'system_event':
        processSystemEvent(data.payload);
        break;
      case 'batch_update':
        data.payload.forEach(processStatusUpdate);
        break;
      case 'pong':
        // Heartbeat response
        break;
      default:
        console.warn('Unknown message type:', data.type);
    }
  }
  
  function processStatusUpdate(update: AgentStatusUpdate) {
    // Update agent metrics cache
    agentMetrics[update.id] = {
      ...agentMetrics[update.id],
      ...update.metrics,
      lastUpdate: update.timestamp
    };
    
    // Add to update queue for batch processing
    updateQueue.push(update);
    
    // Process queue if it gets too large
    if (updateQueue.length >= 10) {
      flushUpdateQueue();
    }
    
    stats.totalUpdates++;
    
    // Dispatch individual update event
    dispatch('agentStatusUpdated', {
      agentId: update.id,
      status: update.status,
      metrics: update.metrics,
      health: update.health,
      timestamp: update.timestamp
    });
    
    // Show notification for significant status changes
    if (showNotifications && isSignificantStatusChange(update)) {
      showStatusNotification(update);
    }
  }
  
  function processMetricsUpdate(metrics: any) {
    Object.keys(metrics).forEach(agentId => {
      agentMetrics[agentId] = {
        ...agentMetrics[agentId],
        ...metrics[agentId],
        lastUpdate: new Date().toISOString()
      };
    });
    
    dispatch('agentMetricsUpdated', { metrics });
  }
  
  function processSystemEvent(event: SystemEvent) {
    notificationQueue.push(event);
    
    if (event.type === 'system_alert') {
      systemAlerts.push({
        ...event,
        id: `alert_${Date.now()}`
      });
    }
    
    dispatch('systemEvent', event);
    
    if (showNotifications) {
      showSystemNotification(event);
    }
  }
  
  function flushUpdateQueue() {
    if (updateQueue.length === 0) return;
    
    const updates = [...updateQueue];
    updateQueue = [];
    
    dispatch('batchStatusUpdate', { updates });
  }
  
  function isSignificantStatusChange(update: AgentStatusUpdate): boolean {
    const currentAgent = agents.find(a => a.id === update.id);
    if (!currentAgent) return true;
    
    // Status change from/to error is significant
    if (update.status === 'error' || currentAgent.status === 'error') {
      return update.status !== currentAgent.status;
    }
    
    // Active/inactive changes are significant
    if (currentAgent.status !== update.status && 
        ['active', 'inactive'].includes(update.status)) {
      return true;
    }
    
    return false;
  }
  
  function showStatusNotification(update: AgentStatusUpdate) {
    const agent = agents.find(a => a.id === update.id);
    const agentName = agent?.name || update.id;
    
    const notification = {
      type: 'status',
      title: `Agent ${agentName}`,
      message: getStatusMessage(update.status),
      level: update.status === 'error' ? 'error' : 
             update.status === 'active' ? 'success' : 'info',
      timestamp: update.timestamp,
      agentId: update.id
    };
    
    dispatch('notification', notification);
  }
  
  function showSystemNotification(event: SystemEvent) {
    const notification = {
      type: 'system',
      title: getSystemEventTitle(event.type),
      message: event.data.message || 'System event occurred',
      level: event.type === 'system_alert' ? 'warning' : 'info',
      timestamp: event.timestamp
    };
    
    dispatch('notification', notification);
  }
  
  function getStatusMessage(status: string): string {
    switch (status) {
      case 'active': return 'Agent is running normally';
      case 'inactive': return 'Agent is stopped';
      case 'error': return 'Agent encountered an error';
      case 'starting': return 'Agent is starting up';
      case 'stopping': return 'Agent is shutting down';
      default: return 'Status unknown';
    }
  }
  
  function getSystemEventTitle(type: string): string {
    switch (type) {
      case 'agent_created': return 'Agent Created';
      case 'agent_deleted': return 'Agent Deleted';
      case 'agent_updated': return 'Agent Updated';
      case 'system_alert': return 'System Alert';
      default: return 'System Event';
    }
  }
  
  function cleanup() {
    if (websocket) {
      websocket.close(1000, 'Component unmounting');
      websocket = null;
    }
    
    stopPolling();
    stopHeartbeat();
    
    if (updateQueue.length > 0) {
      flushUpdateQueue();
    }
  }
  
  // Public methods for parent components
  export function forceUpdate() {
    if (enablePolling) {
      pollAgentStatus();
    }
  }
  
  export function reconnect() {
    if (websocket) {
      websocket.close();
    }
    reconnectAttempts = 0;
    initWebSocket();
  }
  
  export function subscribeToAgent(agentId: string) {
    if (websocket && websocket.readyState === WebSocket.OPEN) {
      sendWebSocketMessage({
        type: 'subscribe_agent',
        agentId
      });
    }
  }
  
  export function unsubscribeFromAgent(agentId: string) {
    if (websocket && websocket.readyState === WebSocket.OPEN) {
      sendWebSocketMessage({
        type: 'unsubscribe_agent',
        agentId
      });
    }
  }
  
  export function getAgentMetrics(agentId: string) {
    return agentMetrics[agentId] || null;
  }
  
  export function getConnectionStats() {
    return {
      ...stats,
      connectionStatus,
      lastUpdateTime,
      reconnectAttempts,
      agentCount: agents.length
    };
  }
  
  export function clearAlerts() {
    systemAlerts = [];
  }
  
  // Reactive statements for external access
  $: connectionStats = {
    status: connectionStatus,
    totalUpdates: stats.totalUpdates,
    successRate: stats.totalUpdates > 0 ? 
      (stats.successfulUpdates / stats.totalUpdates) * 100 : 0,
    averageResponseTime: stats.averageResponseTime,
    uptime: stats.connectionUptime
  };
  
  // Auto-flush update queue periodically
  setInterval(() => {
    if (updateQueue.length > 0) {
      flushUpdateQueue();
    }
  }, 5000);
</script>

<!-- Connection Status Indicator -->
<div class="status-updater">
  <div class="connection-status {connectionStatus}">
    <div class="status-indicator">
      <div class="status-dot"></div>
      <span class="status-text">
        {#if connectionStatus === 'connected'}
          Real-time updates active
        {:else if connectionStatus === 'connecting'}
          Connecting...
        {:else if connectionStatus === 'disconnected'}
          Disconnected
        {:else}
          Connection error
        {/if}
      </span>
    </div>
    
    {#if lastUpdateTime}
      <span class="last-update">
        Last update: {new Date(lastUpdateTime).toLocaleTimeString()}
      </span>
    {/if}
  </div>
  
  <!-- Connection Stats (for debugging) -->
  {#if connectionStats}
    <div class="connection-stats">
      <div class="stat-item">
        <span class="stat-label">Updates:</span>
        <span class="stat-value">{connectionStats.totalUpdates}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Success Rate:</span>
        <span class="stat-value">{connectionStats.successRate.toFixed(1)}%</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Response Time:</span>
        <span class="stat-value">{connectionStats.averageResponseTime.toFixed(0)}ms</span>
      </div>
    </div>
  {/if}
  
  <!-- System Alerts -->
  {#if systemAlerts.length > 0}
    <div class="system-alerts">
      <div class="alerts-header">
        <span class="alerts-title">System Alerts</span>
        <button type="button" class="clear-alerts-btn" on:click={clearAlerts}>
          Clear
        </button>
      </div>
      <div class="alerts-list">
        {#each systemAlerts.slice(-5) as alert}
          <div class="alert-item">
            <span class="alert-message">{alert.data.message}</span>
            <span class="alert-time">{new Date(alert.timestamp).toLocaleTimeString()}</span>
          </div>
        {/each}
      </div>
    </div>
  {/if}
</div>

<style>
  .status-updater {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
  
  .connection-status {
    background: #1e293b;
    border: 1px solid #334155;
    border-radius: 8px;
    padding: 12px 16px;
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 200px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
  
  .connection-status.connected {
    border-color: #10b981;
  }
  
  .connection-status.connecting {
    border-color: #f59e0b;
  }
  
  .connection-status.disconnected {
    border-color: #6b7280;
  }
  
  .connection-status.error {
    border-color: #ef4444;
  }
  
  .status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #6b7280;
  }
  
  .connected .status-dot {
    background: #10b981;
    animation: pulse 2s ease-in-out infinite;
  }
  
  .connecting .status-dot {
    background: #f59e0b;
    animation: pulse 1s ease-in-out infinite;
  }
  
  .error .status-dot {
    background: #ef4444;
    animation: pulse 0.5s ease-in-out infinite;
  }
  
  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }
  
  .status-text {
    color: #e2e8f0;
    font-size: 13px;
    font-weight: 500;
  }
  
  .last-update {
    color: #94a3b8;
    font-size: 11px;
  }
  
  .connection-stats {
    display: flex;
    gap: 12px;
    margin-top: 8px;
  }
  
  .stat-item {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }
  
  .stat-label {
    color: #64748b;
    font-size: 10px;
  }
  
  .stat-value {
    color: #e2e8f0;
    font-size: 11px;
    font-weight: 500;
  }
  
  .system-alerts {
    background: #1e293b;
    border: 1px solid #334155;
    border-radius: 8px;
    margin-top: 8px;
    overflow: hidden;
  }
  
  .alerts-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid #334155;
    background: #0f172a;
  }
  
  .alerts-title {
    color: #e2e8f0;
    font-size: 12px;
    font-weight: 600;
  }
  
  .clear-alerts-btn {
    background: none;
    border: none;
    color: #64748b;
    font-size: 11px;
    cursor: pointer;
    padding: 2px 4px;
  }
  
  .clear-alerts-btn:hover {
    color: #e2e8f0;
  }
  
  .alerts-list {
    max-height: 120px;
    overflow-y: auto;
  }
  
  .alert-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 12px;
    border-bottom: 1px solid #334155;
    font-size: 11px;
  }
  
  .alert-item:last-child {
    border-bottom: none;
  }
  
  .alert-message {
    color: #e2e8f0;
    flex: 1;
  }
  
  .alert-time {
    color: #64748b;
    font-size: 10px;
  }
</style>