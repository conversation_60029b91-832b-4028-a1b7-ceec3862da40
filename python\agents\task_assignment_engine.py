"""
Task Assignment Engine for Metamorphic Reactor
Handles intelligent task assignment, dependency analysis, and load balancing
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Set, Tuple, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import uuid
import json
from collections import defaultdict, deque
import heapq
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

class TaskStatus(str, Enum):
    """Task status values"""
    PENDING = "pending"
    ASSIGNED = "assigned"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    BLOCKED = "blocked"
    PAUSED = "paused"

class TaskPriority(str, Enum):
    """Task priority levels"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"
    CRITICAL = "critical"

class TaskType(str, Enum):
    """Task types"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    DEBUGGING = "debugging"
    REVIEW = "review"
    DEPLOYMENT = "deployment"
    ANALYSIS = "analysis"
    RESEARCH = "research"
    MAINTENANCE = "maintenance"

class AgentCapability(str, Enum):
    """Agent capabilities"""
    CODING = "coding"
    TESTING = "testing"
    DEBUGGING = "debugging"
    REVIEW = "review"
    DEPLOYMENT = "deployment"
    ANALYSIS = "analysis"
    RESEARCH = "research"
    MANAGEMENT = "management"
    COMMUNICATION = "communication"
    DESIGN = "design"

@dataclass
class TaskDependency:
    """Task dependency relationship"""
    task_id: str
    depends_on: str
    dependency_type: str = "completion"  # completion, start, milestone
    optional: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "task_id": self.task_id,
            "depends_on": self.depends_on,
            "dependency_type": self.dependency_type,
            "optional": self.optional
        }

@dataclass
class Task:
    """Task structure"""
    id: str
    title: str
    description: str
    task_type: TaskType
    priority: TaskPriority
    status: TaskStatus = TaskStatus.PENDING
    assigned_agent: Optional[str] = None
    created_by: str = "system"
    created_at: datetime = field(default_factory=datetime.utcnow)
    assigned_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    deadline: Optional[datetime] = None
    estimated_duration: Optional[timedelta] = None
    actual_duration: Optional[timedelta] = None
    required_capabilities: List[AgentCapability] = field(default_factory=list)
    context: Dict[str, Any] = field(default_factory=dict)
    dependencies: List[TaskDependency] = field(default_factory=list)
    progress: float = 0.0
    tags: List[str] = field(default_factory=list)
    retry_count: int = 0
    max_retries: int = 3
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "title": self.title,
            "description": self.description,
            "task_type": self.task_type.value,
            "priority": self.priority.value,
            "status": self.status.value,
            "assigned_agent": self.assigned_agent,
            "created_by": self.created_by,
            "created_at": self.created_at.isoformat(),
            "assigned_at": self.assigned_at.isoformat() if self.assigned_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "deadline": self.deadline.isoformat() if self.deadline else None,
            "estimated_duration": self.estimated_duration.total_seconds() if self.estimated_duration else None,
            "actual_duration": self.actual_duration.total_seconds() if self.actual_duration else None,
            "required_capabilities": [cap.value for cap in self.required_capabilities],
            "context": self.context,
            "dependencies": [dep.to_dict() for dep in self.dependencies],
            "progress": self.progress,
            "tags": self.tags,
            "retry_count": self.retry_count,
            "max_retries": self.max_retries
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Task':
        return cls(
            id=data["id"],
            title=data["title"],
            description=data["description"],
            task_type=TaskType(data["task_type"]),
            priority=TaskPriority(data["priority"]),
            status=TaskStatus(data["status"]),
            assigned_agent=data.get("assigned_agent"),
            created_by=data.get("created_by", "system"),
            created_at=datetime.fromisoformat(data["created_at"]),
            assigned_at=datetime.fromisoformat(data["assigned_at"]) if data.get("assigned_at") else None,
            started_at=datetime.fromisoformat(data["started_at"]) if data.get("started_at") else None,
            completed_at=datetime.fromisoformat(data["completed_at"]) if data.get("completed_at") else None,
            deadline=datetime.fromisoformat(data["deadline"]) if data.get("deadline") else None,
            estimated_duration=timedelta(seconds=data["estimated_duration"]) if data.get("estimated_duration") else None,
            actual_duration=timedelta(seconds=data["actual_duration"]) if data.get("actual_duration") else None,
            required_capabilities=[AgentCapability(cap) for cap in data.get("required_capabilities", [])],
            context=data.get("context", {}),
            dependencies=[TaskDependency(**dep) for dep in data.get("dependencies", [])],
            progress=data.get("progress", 0.0),
            tags=data.get("tags", []),
            retry_count=data.get("retry_count", 0),
            max_retries=data.get("max_retries", 3)
        )

@dataclass
class AgentProfile:
    """Agent profile with capabilities and performance metrics"""
    agent_id: str
    name: str
    capabilities: List[AgentCapability]
    current_load: int = 0
    max_concurrent_tasks: int = 5
    performance_score: float = 1.0
    availability: bool = True
    last_active: datetime = field(default_factory=datetime.utcnow)
    task_history: List[str] = field(default_factory=list)
    success_rate: float = 1.0
    average_completion_time: Optional[timedelta] = None
    specializations: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "agent_id": self.agent_id,
            "name": self.name,
            "capabilities": [cap.value for cap in self.capabilities],
            "current_load": self.current_load,
            "max_concurrent_tasks": self.max_concurrent_tasks,
            "performance_score": self.performance_score,
            "availability": self.availability,
            "last_active": self.last_active.isoformat(),
            "task_history": self.task_history,
            "success_rate": self.success_rate,
            "average_completion_time": self.average_completion_time.total_seconds() if self.average_completion_time else None,
            "specializations": self.specializations
        }
    
    def can_handle_task(self, task: Task) -> bool:
        """Check if agent can handle a task"""
        if not self.availability:
            return False
        
        if self.current_load >= self.max_concurrent_tasks:
            return False
        
        # Check if agent has required capabilities
        for required_cap in task.required_capabilities:
            if required_cap not in self.capabilities:
                return False
        
        return True
    
    def get_load_percentage(self) -> float:
        """Get current load as percentage"""
        return (self.current_load / self.max_concurrent_tasks) * 100 if self.max_concurrent_tasks > 0 else 0

class TaskAssignmentEngine:
    """
    Intelligent task assignment engine with dependency analysis and load balancing
    """
    
    def __init__(self):
        # Task management
        self.tasks: Dict[str, Task] = {}
        self.task_queue: List[Tuple[int, str]] = []  # Priority queue (priority, task_id)
        self.dependency_graph: Dict[str, Set[str]] = defaultdict(set)  # task_id -> dependencies
        self.dependents_graph: Dict[str, Set[str]] = defaultdict(set)  # task_id -> dependents
        
        # Advanced priority queue management
        self.priority_queue_manager = TaskPriorityQueueManager()
        
        # Task monitoring and tracking system
        self.monitoring_system = TaskMonitoringSystem()
        
        # Automatic reassignment system
        self.reassignment_system = AutomaticReassignmentSystem(self)
        
        # Task completion verification system
        self.verification_system = TaskCompletionVerificationSystem(self)
        
        # Agent management
        self.agents: Dict[str, AgentProfile] = {}
        self.agent_assignments: Dict[str, List[str]] = defaultdict(list)  # agent_id -> task_ids
        
        # Assignment strategies
        self.assignment_strategies: Dict[str, Callable] = {
            "round_robin": self._round_robin_assignment,
            "load_balanced": self._load_balanced_assignment,
            "capability_based": self._capability_based_assignment,
            "performance_based": self._performance_based_assignment,
            "hybrid": self._hybrid_assignment
        }
        
        self.current_strategy = "hybrid"
        
        # Monitoring and analytics
        self.assignment_history: List[Dict[str, Any]] = []
        self.performance_metrics: Dict[str, Any] = {
            "total_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "average_completion_time": 0.0,
            "assignment_efficiency": 0.0
        }
        
        # Configuration
        self.max_queue_size = 10000
        self.assignment_timeout = 300  # 5 minutes
        self.rebalance_interval = 3600  # 1 hour
        
        # Background tasks
        self.running = False
        self.monitor_task: Optional[asyncio.Task] = None
        self.rebalance_task: Optional[asyncio.Task] = None
        
        logger.info("TaskAssignmentEngine initialized")
    
    async def start(self):
        """Start the task assignment engine"""
        if self.running:
            return
        
        self.running = True
        
        # Start background tasks
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        self.rebalance_task = asyncio.create_task(self._rebalance_loop())
        
        # Start monitoring system
        await self.monitoring_system.start_monitoring()
        
        # Start reassignment system
        await self.reassignment_system.start()
        
        # Start verification system
        await self.verification_system.start()
        
        logger.info("TaskAssignmentEngine started")
    
    async def stop(self):
        """Stop the task assignment engine"""
        if not self.running:
            return
        
        self.running = False
        
        # Stop monitoring system
        await self.monitoring_system.stop_monitoring()
        
        # Stop reassignment system
        await self.reassignment_system.stop()
        
        # Stop verification system
        await self.verification_system.stop()
        
        # Cancel background tasks
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        
        if self.rebalance_task:
            self.rebalance_task.cancel()
            try:
                await self.rebalance_task
            except asyncio.CancelledError:
                pass
        
        logger.info("TaskAssignmentEngine stopped")
    
    def register_agent(self, agent_profile: AgentProfile):
        """Register an agent with the engine"""
        self.agents[agent_profile.agent_id] = agent_profile
        logger.info(f"Registered agent {agent_profile.agent_id} with capabilities: {agent_profile.capabilities}")
    
    def unregister_agent(self, agent_id: str):
        """Unregister an agent"""
        if agent_id in self.agents:
            # Reassign tasks from this agent
            assigned_tasks = self.agent_assignments.get(agent_id, [])
            for task_id in assigned_tasks:
                if task_id in self.tasks:
                    task = self.tasks[task_id]
                    if task.status in [TaskStatus.ASSIGNED, TaskStatus.IN_PROGRESS]:
                        task.assigned_agent = None
                        task.status = TaskStatus.PENDING
                        self._add_to_queue(task)
            
            # Remove agent
            del self.agents[agent_id]
            if agent_id in self.agent_assignments:
                del self.agent_assignments[agent_id]
            
            logger.info(f"Unregistered agent {agent_id}")
    
    def create_task(
        self,
        title: str,
        description: str,
        task_type: TaskType,
        priority: TaskPriority = TaskPriority.NORMAL,
        required_capabilities: List[AgentCapability] = None,
        context: Dict[str, Any] = None,
        dependencies: List[TaskDependency] = None,
        deadline: Optional[datetime] = None,
        estimated_duration: Optional[timedelta] = None,
        tags: List[str] = None,
        created_by: str = "system"
    ) -> str:
        """Create a new task"""
        task_id = str(uuid.uuid4())
        
        task = Task(
            id=task_id,
            title=title,
            description=description,
            task_type=task_type,
            priority=priority,
            required_capabilities=required_capabilities or [],
            context=context or {},
            dependencies=dependencies or [],
            deadline=deadline,
            estimated_duration=estimated_duration,
            tags=tags or [],
            created_by=created_by
        )
        
        self.tasks[task_id] = task
        self.performance_metrics["total_tasks"] += 1
        
        # Build dependency graph
        self._update_dependency_graph(task)
        
        # Start monitoring the task
        self.monitoring_system.track_task_created(task)
        
        # Add to queue if no dependencies or dependencies are satisfied
        if self._can_be_assigned(task):
            self._add_to_queue(task)
        
        logger.info(f"Created task {task_id}: {title}")
        return task_id
    
    def _update_dependency_graph(self, task: Task):
        """Update dependency graph with task dependencies"""
        for dep in task.dependencies:
            self.dependency_graph[task.id].add(dep.depends_on)
            self.dependents_graph[dep.depends_on].add(task.id)
    
    def analyze_dependencies(self, task_id: str) -> Dict[str, Any]:
        """Analyze task dependencies and provide insights"""
        if task_id not in self.tasks:
            return {"error": "Task not found"}
        
        task = self.tasks[task_id]
        
        # Direct dependencies
        direct_deps = [dep.depends_on for dep in task.dependencies]
        
        # Transitive dependencies (all dependencies in the chain)
        transitive_deps = self._get_transitive_dependencies(task_id)
        
        # Dependent tasks (tasks that depend on this task)
        direct_dependents = list(self.dependents_graph.get(task_id, set()))
        transitive_dependents = self._get_transitive_dependents(task_id)
        
        # Critical path analysis
        critical_path = self._find_critical_path(task_id)
        
        # Blocking analysis
        blocking_tasks = self._find_blocking_tasks(task_id)
        
        # Dependency health check
        dependency_issues = self._check_dependency_health(task_id)
        
        return {
            "task_id": task_id,
            "direct_dependencies": direct_deps,
            "transitive_dependencies": transitive_deps,
            "direct_dependents": direct_dependents,
            "transitive_dependents": transitive_dependents,
            "critical_path": critical_path,
            "blocking_tasks": blocking_tasks,
            "dependency_issues": dependency_issues,
            "can_be_assigned": self._can_be_assigned(task),
            "dependency_depth": len(transitive_deps),
            "dependent_count": len(transitive_dependents)
        }
    
    def _get_transitive_dependencies(self, task_id: str, visited: Set[str] = None) -> List[str]:
        """Get all transitive dependencies using DFS"""
        if visited is None:
            visited = set()
        
        if task_id in visited:
            return []  # Circular dependency detected
        
        visited.add(task_id)
        transitive_deps = []
        
        # Get direct dependencies
        direct_deps = self.dependency_graph.get(task_id, set())
        
        for dep_id in direct_deps:
            transitive_deps.append(dep_id)
            # Recursively get dependencies of dependencies
            transitive_deps.extend(self._get_transitive_dependencies(dep_id, visited.copy()))
        
        return list(set(transitive_deps))  # Remove duplicates
    
    def _get_transitive_dependents(self, task_id: str, visited: Set[str] = None) -> List[str]:
        """Get all transitive dependents using DFS"""
        if visited is None:
            visited = set()
        
        if task_id in visited:
            return []  # Circular dependency detected
        
        visited.add(task_id)
        transitive_dependents = []
        
        # Get direct dependents
        direct_dependents = self.dependents_graph.get(task_id, set())
        
        for dependent_id in direct_dependents:
            transitive_dependents.append(dependent_id)
            # Recursively get dependents of dependents
            transitive_dependents.extend(self._get_transitive_dependents(dependent_id, visited.copy()))
        
        return list(set(transitive_dependents))  # Remove duplicates
    
    def _find_critical_path(self, task_id: str) -> List[Dict[str, Any]]:
        """Find the critical path for a task"""
        if task_id not in self.tasks:
            return []
        
        # Use topological sort to find the longest path
        visited = set()
        path = []
        
        def dfs(current_task_id: str, current_path: List[str], current_duration: timedelta):
            if current_task_id in visited:
                return current_path, current_duration
            
            visited.add(current_task_id)
            
            if current_task_id not in self.tasks:
                return current_path, current_duration
            
            task = self.tasks[current_task_id]
            task_duration = task.estimated_duration or timedelta(hours=1)
            
            new_path = current_path + [current_task_id]
            new_duration = current_duration + task_duration
            
            # Check dependencies
            dependencies = self.dependency_graph.get(current_task_id, set())
            
            if not dependencies:
                return new_path, new_duration
            
            # Find the longest path through dependencies
            longest_path = []
            longest_duration = timedelta(0)
            
            for dep_id in dependencies:
                dep_path, dep_duration = dfs(dep_id, [], timedelta(0))
                if dep_duration > longest_duration:
                    longest_duration = dep_duration
                    longest_path = dep_path
            
            return longest_path + new_path, longest_duration + new_duration
        
        critical_path_tasks, total_duration = dfs(task_id, [], timedelta(0))
        
        # Format critical path with task details
        critical_path = []
        for task_id in critical_path_tasks:
            if task_id in self.tasks:
                task = self.tasks[task_id]
                critical_path.append({
                    "task_id": task_id,
                    "title": task.title,
                    "estimated_duration": task.estimated_duration.total_seconds() if task.estimated_duration else 3600,
                    "status": task.status.value,
                    "assigned_agent": task.assigned_agent
                })
        
        return critical_path
    
    def _find_blocking_tasks(self, task_id: str) -> List[Dict[str, Any]]:
        """Find tasks that are blocking this task"""
        if task_id not in self.tasks:
            return []
        
        task = self.tasks[task_id]
        blocking_tasks = []
        
        for dep in task.dependencies:
            if dep.optional:
                continue
            
            if dep.depends_on not in self.tasks:
                blocking_tasks.append({
                    "task_id": dep.depends_on,
                    "issue": "Task not found",
                    "dependency_type": dep.dependency_type
                })
                continue
            
            dep_task = self.tasks[dep.depends_on]
            
            # Check if dependency is satisfied
            is_blocking = False
            issue = ""
            
            if dep.dependency_type == "completion" and dep_task.status != TaskStatus.COMPLETED:
                is_blocking = True
                issue = f"Task not completed (status: {dep_task.status.value})"
            elif dep.dependency_type == "start" and dep_task.status == TaskStatus.PENDING:
                is_blocking = True
                issue = f"Task not started (status: {dep_task.status.value})"
            
            if is_blocking:
                blocking_tasks.append({
                    "task_id": dep.depends_on,
                    "title": dep_task.title,
                    "status": dep_task.status.value,
                    "assigned_agent": dep_task.assigned_agent,
                    "dependency_type": dep.dependency_type,
                    "issue": issue,
                    "estimated_completion": self._estimate_completion_time(dep_task)
                })
        
        return blocking_tasks
    
    def _estimate_completion_time(self, task: Task) -> Optional[str]:
        """Estimate when a task will be completed"""
        if task.status == TaskStatus.COMPLETED:
            return task.completed_at.isoformat() if task.completed_at else None
        
        if task.status == TaskStatus.IN_PROGRESS and task.started_at:
            if task.estimated_duration:
                estimated_completion = task.started_at + task.estimated_duration
                return estimated_completion.isoformat()
        
        if task.status == TaskStatus.ASSIGNED and task.assigned_at:
            if task.estimated_duration:
                # Assume task starts soon after assignment
                estimated_start = task.assigned_at + timedelta(hours=1)
                estimated_completion = estimated_start + task.estimated_duration
                return estimated_completion.isoformat()
        
        return None
    
    def _check_dependency_health(self, task_id: str) -> List[Dict[str, Any]]:
        """Check for dependency issues"""
        issues = []
        
        # Check for circular dependencies
        if self._has_circular_dependency(task_id):
            issues.append({
                "type": "circular_dependency",
                "severity": "critical",
                "description": "Circular dependency detected",
                "affected_tasks": self._find_circular_dependency_cycle(task_id)
            })
        
        # Check for missing dependencies
        if task_id in self.tasks:
            task = self.tasks[task_id]
            for dep in task.dependencies:
                if dep.depends_on not in self.tasks:
                    issues.append({
                        "type": "missing_dependency",
                        "severity": "high",
                        "description": f"Dependency task {dep.depends_on} not found",
                        "dependency_id": dep.depends_on
                    })
        
        # Check for orphaned dependencies
        orphaned_deps = self._find_orphaned_dependencies(task_id)
        if orphaned_deps:
            issues.append({
                "type": "orphaned_dependencies",
                "severity": "medium",
                "description": "Dependencies with no clear completion path",
                "orphaned_tasks": orphaned_deps
            })
        
        # Check for overdue dependencies
        overdue_deps = self._find_overdue_dependencies(task_id)
        if overdue_deps:
            issues.append({
                "type": "overdue_dependencies",
                "severity": "high",
                "description": "Dependencies that are overdue",
                "overdue_tasks": overdue_deps
            })
        
        return issues
    
    def _has_circular_dependency(self, task_id: str, visited: Set[str] = None, path: Set[str] = None) -> bool:
        """Check if task has circular dependencies"""
        if visited is None:
            visited = set()
        if path is None:
            path = set()
        
        if task_id in path:
            return True
        
        if task_id in visited:
            return False
        
        visited.add(task_id)
        path.add(task_id)
        
        # Check all dependencies
        dependencies = self.dependency_graph.get(task_id, set())
        for dep_id in dependencies:
            if self._has_circular_dependency(dep_id, visited, path):
                return True
        
        path.remove(task_id)
        return False
    
    def _find_circular_dependency_cycle(self, task_id: str) -> List[str]:
        """Find the circular dependency cycle"""
        visited = set()
        path = []
        
        def dfs(current_id: str) -> Optional[List[str]]:
            if current_id in path:
                # Found cycle, return from cycle start
                cycle_start = path.index(current_id)
                return path[cycle_start:] + [current_id]
            
            if current_id in visited:
                return None
            
            visited.add(current_id)
            path.append(current_id)
            
            dependencies = self.dependency_graph.get(current_id, set())
            for dep_id in dependencies:
                cycle = dfs(dep_id)
                if cycle:
                    return cycle
            
            path.remove(current_id)
            return None
        
        return dfs(task_id) or []
    
    def _find_orphaned_dependencies(self, task_id: str) -> List[str]:
        """Find dependencies that have no clear completion path"""
        orphaned = []
        
        dependencies = self._get_transitive_dependencies(task_id)
        
        for dep_id in dependencies:
            if dep_id not in self.tasks:
                orphaned.append(dep_id)
                continue
            
            dep_task = self.tasks[dep_id]
            
            # Check if task has no assigned agent and no clear path to assignment
            if (dep_task.status == TaskStatus.PENDING and 
                not dep_task.assigned_agent and 
                not self._can_be_assigned(dep_task)):
                orphaned.append(dep_id)
        
        return orphaned
    
    def _find_overdue_dependencies(self, task_id: str) -> List[Dict[str, Any]]:
        """Find dependencies that are overdue"""
        overdue = []
        now = datetime.utcnow()
        
        dependencies = self._get_transitive_dependencies(task_id)
        
        for dep_id in dependencies:
            if dep_id not in self.tasks:
                continue
            
            dep_task = self.tasks[dep_id]
            
            # Check if task is overdue
            if dep_task.deadline and dep_task.deadline < now and dep_task.status != TaskStatus.COMPLETED:
                overdue.append({
                    "task_id": dep_id,
                    "title": dep_task.title,
                    "deadline": dep_task.deadline.isoformat(),
                    "days_overdue": (now - dep_task.deadline).days,
                    "status": dep_task.status.value
                })
        
        return overdue
    
    def get_dependency_graph(self) -> Dict[str, Any]:
        """Get the complete dependency graph"""
        graph_data = {
            "nodes": [],
            "edges": []
        }
        
        # Add nodes (tasks)
        for task_id, task in self.tasks.items():
            graph_data["nodes"].append({
                "id": task_id,
                "title": task.title,
                "status": task.status.value,
                "priority": task.priority.value,
                "assigned_agent": task.assigned_agent,
                "progress": task.progress
            })
        
        # Add edges (dependencies)
        for task_id, dependencies in self.dependency_graph.items():
            for dep_id in dependencies:
                graph_data["edges"].append({
                    "from": dep_id,
                    "to": task_id,
                    "type": "dependency"
                })
        
        return graph_data
    
    def optimize_task_order(self) -> List[str]:
        """Optimize task execution order using topological sort"""
        # Kahn's algorithm for topological sorting
        in_degree = defaultdict(int)
        
        # Calculate in-degrees
        for task_id in self.tasks:
            in_degree[task_id] = 0
        
        for task_id, dependencies in self.dependency_graph.items():
            for dep_id in dependencies:
                in_degree[task_id] += 1
        
        # Queue for tasks with no dependencies
        queue = deque([task_id for task_id, degree in in_degree.items() if degree == 0])
        result = []
        
        while queue:
            current = queue.popleft()
            result.append(current)
            
            # Reduce in-degree for dependent tasks
            for dependent in self.dependents_graph.get(current, set()):
                in_degree[dependent] -= 1
                if in_degree[dependent] == 0:
                    queue.append(dependent)
        
        # Check for circular dependencies
        if len(result) != len(self.tasks):
            logger.warning("Circular dependencies detected in task graph")
        
        return result
    
    def suggest_task_breakdown(self, task_id: str) -> Dict[str, Any]:
        """Suggest how to break down a complex task"""
        if task_id not in self.tasks:
            return {"error": "Task not found"}
        
        task = self.tasks[task_id]
        suggestions = {
            "task_id": task_id,
            "current_complexity": self._calculate_task_complexity(task),
            "breakdown_suggestions": []
        }
        
        # Suggest breakdown based on task type
        if task.task_type == TaskType.DEVELOPMENT:
            suggestions["breakdown_suggestions"] = [
                "Design and architecture",
                "Implementation",
                "Unit testing",
                "Integration testing",
                "Code review",
                "Documentation"
            ]
        elif task.task_type == TaskType.TESTING:
            suggestions["breakdown_suggestions"] = [
                "Test planning",
                "Test case creation",
                "Test execution",
                "Bug reporting",
                "Regression testing"
            ]
        elif task.task_type == TaskType.RESEARCH:
            suggestions["breakdown_suggestions"] = [
                "Literature review",
                "Data collection",
                "Analysis",
                "Report writing",
                "Presentation"
            ]
        
        return suggestions
    
    def _calculate_task_complexity(self, task: Task) -> float:
        """Calculate task complexity score"""
        complexity = 0.0
        
        # Base complexity from description length
        complexity += len(task.description) / 1000
        
        # Complexity from dependencies
        complexity += len(task.dependencies) * 0.5
        
        # Complexity from required capabilities
        complexity += len(task.required_capabilities) * 0.3
        
        # Complexity from estimated duration
        if task.estimated_duration:
            complexity += task.estimated_duration.total_seconds() / 3600 / 24  # Days
        
        return min(complexity, 10.0)  # Cap at 10
    
    def _can_be_assigned(self, task: Task) -> bool:
        """Check if task can be assigned (dependencies satisfied)"""
        for dep in task.dependencies:
            if dep.optional:
                continue
            
            dependent_task = self.tasks.get(dep.depends_on)
            if not dependent_task:
                return False
            
            if dep.dependency_type == "completion" and dependent_task.status != TaskStatus.COMPLETED:
                return False
            elif dep.dependency_type == "start" and dependent_task.status == TaskStatus.PENDING:
                return False
        
        return True
    
    def _add_to_queue(self, task: Task):
        """Add task to priority queue"""
        # Use the advanced priority queue manager
        success = self.priority_queue_manager.add_task_to_queue(task)
        
        if success:
            # Also add to legacy queue for backward compatibility
            priority_values = {
                TaskPriority.LOW: 1,
                TaskPriority.NORMAL: 2,
                TaskPriority.HIGH: 3,
                TaskPriority.URGENT: 4,
                TaskPriority.CRITICAL: 5
            }
            
            priority = priority_values.get(task.priority, 2)
            
            # Add deadline urgency
            if task.deadline:
                time_to_deadline = task.deadline - datetime.utcnow()
                if time_to_deadline.total_seconds() < 3600:  # Less than 1 hour
                    priority += 2
                elif time_to_deadline.total_seconds() < 86400:  # Less than 1 day
                    priority += 1
            
            # Use negative priority for max heap behavior
            heapq.heappush(self.task_queue, (-priority, task.id))
            
            logger.debug(f"Added task {task.id} to queue with priority {priority}")
        else:
            logger.warning(f"Failed to add task {task.id} to priority queue manager")
    
    async def assign_next_task(self) -> Optional[str]:
        """Assign the next task from the queue"""
        # First try advanced priority queue manager
        task = self.priority_queue_manager.get_next_task()
        
        if not task:
            # Fallback to legacy queue
            if not self.task_queue:
                return None
            
            # Get highest priority task
            _, task_id = heapq.heappop(self.task_queue)
            
            if task_id not in self.tasks:
                return await self.assign_next_task()  # Task was removed, try next
            
            task = self.tasks[task_id]
        
        # Check if task can still be assigned
        if not self._can_be_assigned(task):
            if task_id not in [t.id for t in [task] if hasattr(task, 'id')]:
                return await self.assign_next_task()  # Dependencies not satisfied, try next
            else:
                # Put back in queue
                self._add_to_queue(task)
                return None
        
        # Find best agent for task
        best_agent = await self._find_best_agent(task)
        
        if not best_agent:
            # No available agent, put back in queue
            self._add_to_queue(task)
            return None
        
        # Assign task to agent
        await self._assign_task_to_agent(task, best_agent)
        
        return task.id
    
    async def _find_best_agent(self, task: Task) -> Optional[str]:
        """Find the best agent for a task using the current strategy"""
        strategy = self.assignment_strategies.get(self.current_strategy, self._hybrid_assignment)
        return await strategy(task)
    
    async def _round_robin_assignment(self, task: Task) -> Optional[str]:
        """Round-robin assignment strategy"""
        available_agents = [
            agent for agent in self.agents.values()
            if agent.can_handle_task(task)
        ]
        
        if not available_agents:
            return None
        
        # Simple round-robin based on agent_id
        available_agents.sort(key=lambda x: x.agent_id)
        return available_agents[len(self.assignment_history) % len(available_agents)].agent_id
    
    async def _load_balanced_assignment(self, task: Task) -> Optional[str]:
        """Load-balanced assignment strategy"""
        available_agents = [
            agent for agent in self.agents.values()
            if agent.can_handle_task(task)
        ]
        
        if not available_agents:
            return None
        
        # Find agent with lowest load
        best_agent = min(available_agents, key=lambda x: x.current_load)
        return best_agent.agent_id
    
    async def _capability_based_assignment(self, task: Task) -> Optional[str]:
        """Capability-based assignment strategy"""
        available_agents = [
            agent for agent in self.agents.values()
            if agent.can_handle_task(task)
        ]
        
        if not available_agents:
            return None
        
        # Score agents based on capability match
        scored_agents = []
        for agent in available_agents:
            score = self._calculate_capability_score(agent, task)
            scored_agents.append((score, agent))
        
        # Sort by score (descending)
        scored_agents.sort(key=lambda x: x[0], reverse=True)
        
        return scored_agents[0][1].agent_id
    
    async def _performance_based_assignment(self, task: Task) -> Optional[str]:
        """Performance-based assignment strategy"""
        available_agents = [
            agent for agent in self.agents.values()
            if agent.can_handle_task(task)
        ]
        
        if not available_agents:
            return None
        
        # Find agent with highest performance score
        best_agent = max(available_agents, key=lambda x: x.performance_score)
        return best_agent.agent_id
    
    async def _hybrid_assignment(self, task: Task) -> Optional[str]:
        """Hybrid assignment strategy combining multiple factors"""
        available_agents = [
            agent for agent in self.agents.values()
            if agent.can_handle_task(task)
        ]
        
        if not available_agents:
            return None
        
        # Calculate composite score for each agent
        scored_agents = []
        for agent in available_agents:
            # Capability score (0-1)
            capability_score = self._calculate_capability_score(agent, task)
            
            # Load score (0-1, inverted so lower load = higher score)
            load_score = 1.0 - (agent.current_load / agent.max_concurrent_tasks)
            
            # Performance score (0-1)
            performance_score = min(agent.performance_score, 1.0)
            
            # Availability score (recent activity)
            time_since_active = datetime.utcnow() - agent.last_active
            availability_score = max(0, 1.0 - (time_since_active.total_seconds() / 3600))
            
            # Success rate score
            success_score = agent.success_rate
            
            # Composite score with weights
            composite_score = (
                capability_score * 0.3 +
                load_score * 0.25 +
                performance_score * 0.2 +
                availability_score * 0.15 +
                success_score * 0.1
            )
            
            scored_agents.append((composite_score, agent))
        
        # Sort by composite score (descending)
        scored_agents.sort(key=lambda x: x[0], reverse=True)
        
        return scored_agents[0][1].agent_id
    
    def _calculate_capability_score(self, agent: AgentProfile, task: Task) -> float:
        """Calculate capability match score"""
        if not task.required_capabilities:
            return 1.0
        
        # Basic capability match
        matched_capabilities = sum(
            1 for cap in task.required_capabilities
            if cap in agent.capabilities
        )
        
        base_score = matched_capabilities / len(task.required_capabilities)
        
        # Bonus for specializations
        specialization_bonus = 0.0
        for spec in agent.specializations:
            if spec.lower() in task.title.lower() or spec.lower() in task.description.lower():
                specialization_bonus += 0.1
        
        return min(base_score + specialization_bonus, 1.0)
    
    def calculate_agent_task_fitness(self, agent_id: str, task_id: str) -> Dict[str, Any]:
        """Calculate comprehensive fitness score for agent-task pairing"""
        if agent_id not in self.agents or task_id not in self.tasks:
            return {"error": "Agent or task not found"}
        
        agent = self.agents[agent_id]
        task = self.tasks[task_id]
        
        # Check if agent can handle the task at all
        if not agent.can_handle_task(task):
            return {
                "agent_id": agent_id,
                "task_id": task_id,
                "fitness_score": 0.0,
                "can_handle": False,
                "reason": "Agent cannot handle this task"
            }
        
        # Calculate different fitness components
        fitness_components = {}
        
        # 1. Capability Match Score (30%)
        capability_score = self._calculate_detailed_capability_score(agent, task)
        fitness_components["capability_match"] = capability_score
        
        # 2. Workload Balance Score (20%)
        workload_score = self._calculate_workload_score(agent)
        fitness_components["workload_balance"] = workload_score
        
        # 3. Performance History Score (20%)
        performance_score = self._calculate_performance_score(agent, task)
        fitness_components["performance_history"] = performance_score
        
        # 4. Availability Score (15%)
        availability_score = self._calculate_availability_score(agent)
        fitness_components["availability"] = availability_score
        
        # 5. Task Affinity Score (10%)
        affinity_score = self._calculate_task_affinity_score(agent, task)
        fitness_components["task_affinity"] = affinity_score
        
        # 6. Deadline Pressure Score (5%)
        deadline_score = self._calculate_deadline_pressure_score(agent, task)
        fitness_components["deadline_pressure"] = deadline_score
        
        # Calculate weighted overall fitness score
        weights = {
            "capability_match": 0.30,
            "workload_balance": 0.20,
            "performance_history": 0.20,
            "availability": 0.15,
            "task_affinity": 0.10,
            "deadline_pressure": 0.05
        }
        
        overall_score = sum(
            fitness_components[component] * weights[component]
            for component in fitness_components
        )
        
        return {
            "agent_id": agent_id,
            "task_id": task_id,
            "fitness_score": overall_score,
            "can_handle": True,
            "components": fitness_components,
            "weights": weights,
            "recommendation": self._generate_fitness_recommendation(overall_score, fitness_components)
        }
    
    def _calculate_detailed_capability_score(self, agent: AgentProfile, task: Task) -> float:
        """Calculate detailed capability match score"""
        if not task.required_capabilities:
            return 1.0
        
        score = 0.0
        
        # Exact capability matches
        exact_matches = sum(
            1 for cap in task.required_capabilities
            if cap in agent.capabilities
        )
        
        # Base score from exact matches
        base_score = exact_matches / len(task.required_capabilities)
        score += base_score * 0.7
        
        # Bonus for having more capabilities than required
        capability_surplus = len(agent.capabilities) - len(task.required_capabilities)
        if capability_surplus > 0:
            score += min(capability_surplus * 0.05, 0.1)
        
        # Bonus for specializations
        specialization_bonus = 0.0
        for spec in agent.specializations:
            if spec.lower() in task.title.lower() or spec.lower() in task.description.lower():
                specialization_bonus += 0.1
                
            # Check if specialization matches task type
            if spec.lower() == task.task_type.value.lower():
                specialization_bonus += 0.15
        
        score += min(specialization_bonus, 0.2)
        
        # Penalty for missing critical capabilities
        missing_capabilities = [
            cap for cap in task.required_capabilities
            if cap not in agent.capabilities
        ]
        
        if missing_capabilities:
            # Check if missing capabilities are critical for task type
            critical_missing = self._check_critical_capabilities(missing_capabilities, task.task_type)
            score -= len(critical_missing) * 0.2
        
        return max(0.0, min(score, 1.0))
    
    def _check_critical_capabilities(self, missing_capabilities: List[AgentCapability], task_type: TaskType) -> List[AgentCapability]:
        """Check which missing capabilities are critical for the task type"""
        critical_map = {
            TaskType.DEVELOPMENT: [AgentCapability.CODING, AgentCapability.DESIGN],
            TaskType.TESTING: [AgentCapability.TESTING],
            TaskType.DEBUGGING: [AgentCapability.DEBUGGING, AgentCapability.CODING],
            TaskType.REVIEW: [AgentCapability.REVIEW],
            TaskType.DEPLOYMENT: [AgentCapability.DEPLOYMENT],
            TaskType.ANALYSIS: [AgentCapability.ANALYSIS],
            TaskType.RESEARCH: [AgentCapability.RESEARCH],
            TaskType.MAINTENANCE: [AgentCapability.CODING, AgentCapability.DEBUGGING]
        }
        
        critical_capabilities = critical_map.get(task_type, [])
        return [cap for cap in missing_capabilities if cap in critical_capabilities]
    
    def _calculate_workload_score(self, agent: AgentProfile) -> float:
        """Calculate workload balance score"""
        if agent.max_concurrent_tasks == 0:
            return 0.0
        
        load_percentage = agent.current_load / agent.max_concurrent_tasks
        
        # Optimal load is around 70-80%
        if load_percentage <= 0.7:
            return 1.0 - (load_percentage * 0.3)  # Prefer some load over no load
        elif load_percentage <= 0.8:
            return 1.0  # Optimal range
        else:
            return max(0.0, 1.0 - (load_percentage - 0.8) * 2.0)  # Penalty for overload
    
    def _calculate_performance_score(self, agent: AgentProfile, task: Task) -> float:
        """Calculate performance history score"""
        score = 0.0
        
        # Base score from success rate
        score += agent.success_rate * 0.6
        
        # Score from performance rating
        score += min(agent.performance_score, 1.0) * 0.4
        
        # Bonus for experience with similar tasks
        similar_task_experience = self._calculate_similar_task_experience(agent, task)
        score += similar_task_experience * 0.2
        
        # Penalty for recent failures
        recent_failure_penalty = self._calculate_recent_failure_penalty(agent)
        score -= recent_failure_penalty
        
        return max(0.0, min(score, 1.0))
    
    def _calculate_similar_task_experience(self, agent: AgentProfile, task: Task) -> float:
        """Calculate experience with similar tasks"""
        if not agent.task_history:
            return 0.0
        
        similar_tasks = 0
        total_tasks = len(agent.task_history)
        
        for past_task_id in agent.task_history:
            if past_task_id in self.tasks:
                past_task = self.tasks[past_task_id]
                if past_task.task_type == task.task_type:
                    similar_tasks += 1
                
                # Check for similar keywords
                task_keywords = set(task.title.lower().split() + task.description.lower().split())
                past_keywords = set(past_task.title.lower().split() + past_task.description.lower().split())
                
                keyword_overlap = len(task_keywords & past_keywords) / len(task_keywords | past_keywords)
                if keyword_overlap > 0.3:
                    similar_tasks += 0.5
        
        return min(similar_tasks / total_tasks, 1.0)
    
    def _calculate_recent_failure_penalty(self, agent: AgentProfile) -> float:
        """Calculate penalty for recent failures"""
        if not agent.task_history:
            return 0.0
        
        recent_tasks = agent.task_history[-10:]  # Last 10 tasks
        recent_failures = 0
        
        for task_id in recent_tasks:
            if task_id in self.tasks:
                task = self.tasks[task_id]
                if task.status == TaskStatus.FAILED:
                    recent_failures += 1
        
        failure_rate = recent_failures / len(recent_tasks)
        return failure_rate * 0.3  # Max penalty of 0.3
    
    def _calculate_availability_score(self, agent: AgentProfile) -> float:
        """Calculate availability score"""
        if not agent.availability:
            return 0.0
        
        # Score based on recent activity
        time_since_active = datetime.utcnow() - agent.last_active
        hours_inactive = time_since_active.total_seconds() / 3600
        
        if hours_inactive < 1:
            return 1.0
        elif hours_inactive < 24:
            return max(0.5, 1.0 - (hours_inactive / 24) * 0.5)
        else:
            return max(0.1, 1.0 - (hours_inactive / 168) * 0.9)  # 1 week decay
    
    def _calculate_task_affinity_score(self, agent: AgentProfile, task: Task) -> float:
        """Calculate task affinity score"""
        affinity_score = 0.0
        
        # Preference based on task type and agent specializations
        if task.task_type.value in agent.specializations:
            affinity_score += 0.5
        
        # Preference based on task priority
        if task.priority in [TaskPriority.HIGH, TaskPriority.URGENT, TaskPriority.CRITICAL]:
            # High-performing agents prefer high-priority tasks
            if agent.performance_score > 0.8:
                affinity_score += 0.3
        
        # Preference based on task complexity
        task_complexity = self._calculate_task_complexity(task)
        
        if task_complexity > 7:  # Complex task
            # Experienced agents prefer complex tasks
            if len(agent.task_history) > 20:
                affinity_score += 0.2
        elif task_complexity < 3:  # Simple task
            # Less experienced agents might prefer simpler tasks
            if len(agent.task_history) < 10:
                affinity_score += 0.1
        
        return min(affinity_score, 1.0)
    
    def _calculate_deadline_pressure_score(self, agent: AgentProfile, task: Task) -> float:
        """Calculate deadline pressure score"""
        if not task.deadline:
            return 0.5  # Neutral score for tasks without deadlines
        
        time_to_deadline = task.deadline - datetime.utcnow()
        
        # If deadline is already passed, return low score
        if time_to_deadline.total_seconds() < 0:
            return 0.1
        
        # If deadline is very tight, prefer faster agents
        if time_to_deadline.total_seconds() < 3600:  # Less than 1 hour
            return agent.performance_score * 0.8  # Favor high-performance agents
        elif time_to_deadline.total_seconds() < 86400:  # Less than 1 day
            return agent.performance_score * 0.6 + 0.4
        else:
            return 0.7  # Normal deadline pressure
    
    def _generate_fitness_recommendation(self, overall_score: float, components: Dict[str, float]) -> str:
        """Generate fitness recommendation"""
        if overall_score >= 0.8:
            return "Excellent fit - highly recommended"
        elif overall_score >= 0.6:
            return "Good fit - recommended"
        elif overall_score >= 0.4:
            return "Acceptable fit - consider if better options unavailable"
        else:
            # Identify main weaknesses
            weakest_component = min(components.items(), key=lambda x: x[1])
            return f"Poor fit - main concern: {weakest_component[0].replace('_', ' ')}"
    
    def rank_agents_for_task(self, task_id: str) -> List[Dict[str, Any]]:
        """Rank all available agents for a task"""
        if task_id not in self.tasks:
            return []
        
        task = self.tasks[task_id]
        agent_scores = []
        
        for agent_id, agent in self.agents.items():
            if not agent.can_handle_task(task):
                continue
            
            fitness_result = self.calculate_agent_task_fitness(agent_id, task_id)
            agent_scores.append({
                "agent_id": agent_id,
                "agent_name": agent.name,
                "fitness_score": fitness_result["fitness_score"],
                "components": fitness_result["components"],
                "recommendation": fitness_result["recommendation"]
            })
        
        # Sort by fitness score (descending)
        agent_scores.sort(key=lambda x: x["fitness_score"], reverse=True)
        
        return agent_scores
    
    def get_agent_workload_analysis(self, agent_id: str) -> Dict[str, Any]:
        """Get detailed workload analysis for an agent"""
        if agent_id not in self.agents:
            return {"error": "Agent not found"}
        
        agent = self.agents[agent_id]
        assigned_tasks = self.agent_assignments.get(agent_id, [])
        
        # Analyze current tasks
        task_analysis = {
            "total_tasks": len(assigned_tasks),
            "tasks_by_status": {},
            "tasks_by_priority": {},
            "tasks_by_type": {},
            "estimated_total_time": 0,
            "overdue_tasks": 0
        }
        
        now = datetime.utcnow()
        
        for task_id in assigned_tasks:
            if task_id not in self.tasks:
                continue
            
            task = self.tasks[task_id]
            
            # Count by status
            status = task.status.value
            task_analysis["tasks_by_status"][status] = task_analysis["tasks_by_status"].get(status, 0) + 1
            
            # Count by priority
            priority = task.priority.value
            task_analysis["tasks_by_priority"][priority] = task_analysis["tasks_by_priority"].get(priority, 0) + 1
            
            # Count by type
            task_type = task.task_type.value
            task_analysis["tasks_by_type"][task_type] = task_analysis["tasks_by_type"].get(task_type, 0) + 1
            
            # Add estimated time
            if task.estimated_duration:
                task_analysis["estimated_total_time"] += task.estimated_duration.total_seconds()
            
            # Check for overdue tasks
            if task.deadline and task.deadline < now and task.status != TaskStatus.COMPLETED:
                task_analysis["overdue_tasks"] += 1
        
        # Calculate workload metrics
        load_percentage = agent.get_load_percentage()
        
        return {
            "agent_id": agent_id,
            "agent_name": agent.name,
            "current_load": agent.current_load,
            "max_concurrent_tasks": agent.max_concurrent_tasks,
            "load_percentage": load_percentage,
            "availability": agent.availability,
            "performance_score": agent.performance_score,
            "success_rate": agent.success_rate,
            "task_analysis": task_analysis,
            "workload_status": self._get_workload_status(load_percentage),
            "recommendations": self._generate_workload_recommendations(agent, task_analysis)
        }
    
    def _get_workload_status(self, load_percentage: float) -> str:
        """Get workload status description"""
        if load_percentage < 30:
            return "Underutilized"
        elif load_percentage < 70:
            return "Optimal"
        elif load_percentage < 90:
            return "High"
        else:
            return "Overloaded"
    
    def _generate_workload_recommendations(self, agent: AgentProfile, task_analysis: Dict[str, Any]) -> List[str]:
        """Generate workload recommendations"""
        recommendations = []
        
        load_percentage = agent.get_load_percentage()
        
        if load_percentage < 30:
            recommendations.append("Consider assigning more tasks to this agent")
        elif load_percentage > 90:
            recommendations.append("Agent is overloaded - consider redistributing tasks")
        
        if task_analysis["overdue_tasks"] > 0:
            recommendations.append(f"Agent has {task_analysis['overdue_tasks']} overdue tasks")
        
        if agent.success_rate < 0.7:
            recommendations.append("Agent has low success rate - consider training or task reassignment")
        
        critical_tasks = task_analysis["tasks_by_priority"].get("critical", 0)
        if critical_tasks > 2:
            recommendations.append("Agent has multiple critical tasks - monitor closely")
        
        return recommendations
    
    async def dynamic_task_assignment(self, batch_size: int = 5) -> List[str]:
        """Dynamically assign multiple tasks using intelligent strategies"""
        if not self.task_queue:
            return []
        
        assigned_tasks = []
        
        # Process up to batch_size tasks
        for _ in range(min(batch_size, len(self.task_queue))):
            # Get next task with dynamic priority adjustment
            task_id = await self._get_next_dynamic_task()
            if not task_id:
                break
            
            task = self.tasks[task_id]
            
            # Apply dynamic assignment strategy
            agent_id = await self._apply_dynamic_assignment_strategy(task)
            
            if agent_id:
                await self._assign_task_to_agent(task, agent_id)
                assigned_tasks.append(task_id)
            else:
                # If no agent available, put task back in queue
                self._add_to_queue(task)
        
        return assigned_tasks
    
    async def _get_next_dynamic_task(self) -> Optional[str]:
        """Get next task with dynamic priority adjustment"""
        if not self.task_queue:
            return None
        
        # Consider multiple tasks and pick the best one dynamically
        candidate_tasks = []
        
        # Get top candidates from queue
        for _ in range(min(5, len(self.task_queue))):
            if self.task_queue:
                _, task_id = heapq.heappop(self.task_queue)
                if task_id in self.tasks:
                    candidate_tasks.append(task_id)
        
        if not candidate_tasks:
            return None
        
        # Score candidates based on current system state
        best_task_id = None
        best_score = -1
        
        for task_id in candidate_tasks:
            task = self.tasks[task_id]
            
            # Check if task can be assigned
            if not self._can_be_assigned(task):
                continue
            
            # Calculate dynamic priority score
            score = self._calculate_dynamic_priority_score(task)
            
            if score > best_score:
                best_score = score
                best_task_id = task_id
        
        # Put non-selected tasks back in queue
        for task_id in candidate_tasks:
            if task_id != best_task_id:
                self._add_to_queue(self.tasks[task_id])
        
        return best_task_id
    
    def _calculate_dynamic_priority_score(self, task: Task) -> float:
        """Calculate dynamic priority score based on current system state"""
        score = 0.0
        
        # Base priority score
        priority_values = {
            TaskPriority.LOW: 1.0,
            TaskPriority.NORMAL: 2.0,
            TaskPriority.HIGH: 3.0,
            TaskPriority.URGENT: 4.0,
            TaskPriority.CRITICAL: 5.0
        }
        
        score += priority_values.get(task.priority, 2.0)
        
        # Deadline urgency
        if task.deadline:
            time_to_deadline = task.deadline - datetime.utcnow()
            hours_to_deadline = time_to_deadline.total_seconds() / 3600
            
            if hours_to_deadline < 0:
                score += 10.0  # Overdue tasks get highest priority
            elif hours_to_deadline < 1:
                score += 5.0
            elif hours_to_deadline < 24:
                score += 2.0
            elif hours_to_deadline < 168:  # 1 week
                score += 1.0
        
        # Blocking factor - tasks that block others get higher priority
        dependents = len(self.dependents_graph.get(task.id, set()))
        score += dependents * 0.5
        
        # Agent availability factor
        available_agents = sum(
            1 for agent in self.agents.values()
            if agent.can_handle_task(task)
        )
        
        if available_agents == 0:
            score = 0.0  # Cannot assign, lowest priority
        elif available_agents == 1:
            score += 1.0  # Only one agent available, higher priority
        
        # Task age factor
        task_age = datetime.utcnow() - task.created_at
        hours_old = task_age.total_seconds() / 3600
        score += min(hours_old / 24, 2.0)  # Max 2 points for age
        
        # System load factor
        overall_load = self._calculate_system_load()
        if overall_load < 0.5:
            # System not heavily loaded, prefer complex tasks
            task_complexity = self._calculate_task_complexity(task)
            score += task_complexity * 0.1
        else:
            # System heavily loaded, prefer simple tasks
            task_complexity = self._calculate_task_complexity(task)
            score += (10 - task_complexity) * 0.05
        
        return score
    
    def _calculate_system_load(self) -> float:
        """Calculate overall system load"""
        if not self.agents:
            return 0.0
        
        total_load = sum(agent.current_load for agent in self.agents.values())
        total_capacity = sum(agent.max_concurrent_tasks for agent in self.agents.values())
        
        return total_load / total_capacity if total_capacity > 0 else 0.0
    
    async def _apply_dynamic_assignment_strategy(self, task: Task) -> Optional[str]:
        """Apply dynamic assignment strategy based on current conditions"""
        # Analyze current system state
        system_state = self._analyze_system_state()
        
        # Choose strategy based on system state
        if system_state["load_level"] == "low":
            # Low load: Use performance-based assignment
            return await self._performance_based_assignment(task)
        elif system_state["load_level"] == "high":
            # High load: Use load balancing
            return await self._load_balanced_assignment(task)
        elif system_state["deadline_pressure"] == "high":
            # High deadline pressure: Use fastest available agent
            return await self._deadline_optimized_assignment(task)
        elif system_state["complexity_level"] == "high":
            # High complexity: Use capability-based assignment
            return await self._capability_based_assignment(task)
        else:
            # Default: Use hybrid approach
            return await self._hybrid_assignment(task)
    
    def _analyze_system_state(self) -> Dict[str, str]:
        """Analyze current system state to inform assignment decisions"""
        # Calculate load level
        system_load = self._calculate_system_load()
        if system_load < 0.3:
            load_level = "low"
        elif system_load < 0.7:
            load_level = "medium"
        else:
            load_level = "high"
        
        # Calculate deadline pressure
        urgent_tasks = sum(
            1 for task in self.tasks.values()
            if task.status in [TaskStatus.PENDING, TaskStatus.ASSIGNED]
            and task.deadline
            and (task.deadline - datetime.utcnow()).total_seconds() < 86400  # 24 hours
        )
        
        total_pending = sum(
            1 for task in self.tasks.values()
            if task.status in [TaskStatus.PENDING, TaskStatus.ASSIGNED]
        )
        
        deadline_pressure_ratio = urgent_tasks / total_pending if total_pending > 0 else 0
        
        if deadline_pressure_ratio > 0.3:
            deadline_pressure = "high"
        elif deadline_pressure_ratio > 0.1:
            deadline_pressure = "medium"
        else:
            deadline_pressure = "low"
        
        # Calculate complexity level
        pending_tasks = [
            task for task in self.tasks.values()
            if task.status in [TaskStatus.PENDING, TaskStatus.ASSIGNED]
        ]
        
        if pending_tasks:
            avg_complexity = sum(self._calculate_task_complexity(task) for task in pending_tasks) / len(pending_tasks)
            if avg_complexity > 6:
                complexity_level = "high"
            elif avg_complexity > 3:
                complexity_level = "medium"
            else:
                complexity_level = "low"
        else:
            complexity_level = "low"
        
        return {
            "load_level": load_level,
            "deadline_pressure": deadline_pressure,
            "complexity_level": complexity_level,
            "total_agents": len(self.agents),
            "available_agents": sum(1 for agent in self.agents.values() if agent.availability),
            "queue_size": len(self.task_queue)
        }
    
    async def _deadline_optimized_assignment(self, task: Task) -> Optional[str]:
        """Assignment strategy optimized for deadline pressure"""
        available_agents = [
            agent for agent in self.agents.values()
            if agent.can_handle_task(task)
        ]
        
        if not available_agents:
            return None
        
        # Score agents based on speed and reliability
        scored_agents = []
        for agent in available_agents:
            score = 0.0
            
            # Performance score (speed)
            score += agent.performance_score * 0.4
            
            # Success rate (reliability)
            score += agent.success_rate * 0.3
            
            # Average completion time (if available)
            if agent.average_completion_time:
                # Lower completion time = higher score
                hours = agent.average_completion_time.total_seconds() / 3600
                score += max(0, (24 - hours) / 24) * 0.2
            
            # Current load (prefer less loaded agents)
            load_factor = 1.0 - (agent.current_load / agent.max_concurrent_tasks)
            score += load_factor * 0.1
            
            scored_agents.append((score, agent))
        
        # Sort by score (descending)
        scored_agents.sort(key=lambda x: x[0], reverse=True)
        
        return scored_agents[0][1].agent_id
    
    async def adaptive_assignment_strategy(self, task: Task) -> Optional[str]:
        """Adaptive assignment strategy that learns from past assignments"""
        # Get historical performance data for similar tasks
        similar_tasks = self._find_similar_tasks(task)
        
        if not similar_tasks:
            # No similar tasks, use default strategy
            return await self._hybrid_assignment(task)
        
        # Analyze performance of agents on similar tasks
        agent_performance = {}
        
        for similar_task in similar_tasks:
            if similar_task.assigned_agent and similar_task.status == TaskStatus.COMPLETED:
                agent_id = similar_task.assigned_agent
                if agent_id not in agent_performance:
                    agent_performance[agent_id] = {
                        "success_count": 0,
                        "total_time": timedelta(0),
                        "task_count": 0
                    }
                
                agent_performance[agent_id]["success_count"] += 1
                agent_performance[agent_id]["task_count"] += 1
                
                if similar_task.actual_duration:
                    agent_performance[agent_id]["total_time"] += similar_task.actual_duration
        
        # Score available agents based on historical performance
        available_agents = [
            agent for agent in self.agents.values()
            if agent.can_handle_task(task)
        ]
        
        if not available_agents:
            return None
        
        best_agent = None
        best_score = -1
        
        for agent in available_agents:
            score = 0.0
            
            # Historical performance on similar tasks
            if agent.agent_id in agent_performance:
                perf = agent_performance[agent.agent_id]
                success_rate = perf["success_count"] / perf["task_count"]
                avg_time = perf["total_time"] / perf["task_count"] if perf["task_count"] > 0 else timedelta(hours=1)
                
                score += success_rate * 0.5
                score += max(0, (24 - avg_time.total_seconds() / 3600) / 24) * 0.3
            
            # Current fitness score
            fitness_result = self.calculate_agent_task_fitness(agent.agent_id, task.id)
            score += fitness_result["fitness_score"] * 0.2
            
            if score > best_score:
                best_score = score
                best_agent = agent
        
        return best_agent.agent_id if best_agent else None
    
    def _find_similar_tasks(self, task: Task) -> List[Task]:
        """Find similar completed tasks for learning"""
        similar_tasks = []
        
        for past_task in self.tasks.values():
            if past_task.status != TaskStatus.COMPLETED:
                continue
            
            if past_task.id == task.id:
                continue
            
            # Check similarity criteria
            similarity_score = 0.0
            
            # Same task type
            if past_task.task_type == task.task_type:
                similarity_score += 0.4
            
            # Similar required capabilities
            if task.required_capabilities and past_task.required_capabilities:
                common_caps = set(task.required_capabilities) & set(past_task.required_capabilities)
                cap_similarity = len(common_caps) / len(set(task.required_capabilities) | set(past_task.required_capabilities))
                similarity_score += cap_similarity * 0.3
            
            # Similar keywords in title/description
            task_keywords = set(task.title.lower().split() + task.description.lower().split())
            past_keywords = set(past_task.title.lower().split() + past_task.description.lower().split())
            
            if task_keywords and past_keywords:
                keyword_similarity = len(task_keywords & past_keywords) / len(task_keywords | past_keywords)
                similarity_score += keyword_similarity * 0.2
            
            # Similar priority
            if task.priority == past_task.priority:
                similarity_score += 0.1
            
            # If similarity is high enough, include it
            if similarity_score >= 0.5:
                similar_tasks.append(past_task)
        
        return similar_tasks
    
    async def emergency_assignment(self, task_id: str) -> Optional[str]:
        """Emergency assignment for critical tasks"""
        if task_id not in self.tasks:
            return None
        
        task = self.tasks[task_id]
        
        # Mark task as critical priority
        task.priority = TaskPriority.CRITICAL
        
        # Find the best available agent immediately
        agent_rankings = self.rank_agents_for_task(task_id)
        
        if not agent_rankings:
            return None
        
        # Try to assign to the best agent
        best_agent_id = agent_rankings[0]["agent_id"]
        best_agent = self.agents[best_agent_id]
        
        # If best agent is overloaded, try to free up capacity
        if best_agent.current_load >= best_agent.max_concurrent_tasks:
            # Try to reassign some of their lower-priority tasks
            await self._free_agent_capacity(best_agent_id, 1)
        
        # Assign the task
        await self._assign_task_to_agent(task, best_agent_id)
        
        logger.info(f"Emergency assignment: Task {task_id} assigned to agent {best_agent_id}")
        return best_agent_id
    
    async def _free_agent_capacity(self, agent_id: str, slots_needed: int) -> int:
        """Free up capacity for an agent by reassigning lower-priority tasks"""
        if agent_id not in self.agents:
            return 0
        
        agent = self.agents[agent_id]
        assigned_tasks = self.agent_assignments.get(agent_id, [])
        
        # Find reassignable tasks (lower priority, not yet started)
        reassignable_tasks = []
        
        for task_id in assigned_tasks:
            if task_id not in self.tasks:
                continue
            
            task = self.tasks[task_id]
            
            if (task.status == TaskStatus.ASSIGNED and 
                task.priority in [TaskPriority.LOW, TaskPriority.NORMAL]):
                reassignable_tasks.append(task)
        
        # Sort by priority (lowest first)
        reassignable_tasks.sort(key=lambda t: t.priority.value)
        
        slots_freed = 0
        
        for task in reassignable_tasks:
            if slots_freed >= slots_needed:
                break
            
            # Try to reassign to another agent
            suitable_agents = [
                a for a in self.agents.values()
                if a.agent_id != agent_id and a.can_handle_task(task)
            ]
            
            if suitable_agents:
                # Find best alternative agent
                best_alternative = min(suitable_agents, key=lambda a: a.current_load)
                
                # Reassign task
                await self._reassign_task(task, best_alternative.agent_id)
                slots_freed += 1
        
        return slots_freed
    
    async def bulk_assignment(self, task_ids: List[str]) -> Dict[str, str]:
        """Assign multiple tasks efficiently"""
        assignments = {}
        
        # Group tasks by type and priority for efficient assignment
        task_groups = self._group_tasks_for_assignment(task_ids)
        
        for group_key, group_tasks in task_groups.items():
            # Assign tasks in each group
            for task_id in group_tasks:
                if task_id not in self.tasks:
                    continue
                
                task = self.tasks[task_id]
                
                # Check if task can be assigned
                if not self._can_be_assigned(task):
                    continue
                
                # Find best agent for this task
                agent_id = await self._find_best_agent(task)
                
                if agent_id:
                    await self._assign_task_to_agent(task, agent_id)
                    assignments[task_id] = agent_id
        
        return assignments
    
    def _group_tasks_for_assignment(self, task_ids: List[str]) -> Dict[str, List[str]]:
        """Group tasks by type and priority for efficient assignment"""
        groups = defaultdict(list)
        
        for task_id in task_ids:
            if task_id not in self.tasks:
                continue
            
            task = self.tasks[task_id]
            group_key = f"{task.task_type.value}_{task.priority.value}"
            groups[group_key].append(task_id)
        
        return groups
    
    async def schedule_future_assignment(self, task_id: str, assignment_time: datetime) -> bool:
        """Schedule a task for future assignment"""
        if task_id not in self.tasks:
            return False
        
        # This would typically use a scheduler service
        # For now, we'll simulate by setting a flag
        task = self.tasks[task_id]
        task.context["scheduled_assignment"] = assignment_time.isoformat()
        
        logger.info(f"Scheduled task {task_id} for assignment at {assignment_time}")
        return True
    
    async def _assign_task_to_agent(self, task: Task, agent_id: str):
        """Assign a task to an agent"""
        agent = self.agents[agent_id]
        
        # Update task
        task.assigned_agent = agent_id
        task.status = TaskStatus.ASSIGNED
        task.assigned_at = datetime.utcnow()
        
        # Track assignment in monitoring system
        self.monitoring_system.track_task_assigned(task, agent_id)
        
        # Update agent
        agent.current_load += 1
        self.agent_assignments[agent_id].append(task.id)
        
        # Record assignment
        assignment_record = {
            "task_id": task.id,
            "agent_id": agent_id,
            "assigned_at": datetime.utcnow().isoformat(),
            "strategy": self.current_strategy,
            "task_priority": task.priority.value,
            "agent_load": agent.current_load
        }
        
        self.assignment_history.append(assignment_record)
        
        logger.info(f"Assigned task {task.id} to agent {agent_id}")
    
    async def start_task(self, task_id: str, agent_id: str) -> bool:
        """Mark task as started"""
        if task_id not in self.tasks:
            return False
        
        task = self.tasks[task_id]
        
        if task.assigned_agent != agent_id:
            return False
        
        task.status = TaskStatus.IN_PROGRESS
        task.started_at = datetime.utcnow()
        
        # Track task start in monitoring system
        self.monitoring_system.track_task_started(task, agent_id)
        
        logger.info(f"Started task {task_id} by agent {agent_id}")
        return True
    
    async def complete_task(self, task_id: str, agent_id: str, result: Dict[str, Any] = None) -> str:
        """Mark task as completed and submit for verification"""
        if task_id not in self.tasks:
            raise ValueError(f"Task {task_id} not found")
        
        task = self.tasks[task_id]
        
        if task.assigned_agent != agent_id:
            raise ValueError(f"Task {task_id} is not assigned to agent {agent_id}")
        
        # Prepare completion data
        completion_data = {
            "result": result or {},
            "status": "completed",
            "completed_by": agent_id,
            "completed_at": datetime.utcnow().isoformat(),
            "task_id": task_id
        }
        
        # Add additional completion metadata
        if task.started_at:
            completion_data["actual_duration"] = (datetime.utcnow() - task.started_at).total_seconds()
        
        if task.estimated_duration:
            completion_data["estimated_duration"] = task.estimated_duration.total_seconds()
        
        # Update task timing
        task.completed_at = datetime.utcnow()
        task.progress = 1.0
        
        if task.started_at:
            task.actual_duration = task.completed_at - task.started_at
        
        # Update agent
        agent = self.agents[agent_id]
        agent.current_load -= 1
        
        if task_id in self.agent_assignments[agent_id]:
            self.agent_assignments[agent_id].remove(task_id)
        
        # Track completion in monitoring system
        self.monitoring_system.track_task_completed(task, agent_id, True)
        
        # Update performance metrics
        self.performance_metrics["completed_tasks"] += 1
        self._update_agent_performance(agent, task, True)
        
        # Submit for verification
        verification_id = await self.verification_system.submit_for_verification(task_id, completion_data)
        
        logger.info(f"Task {task_id} completed by agent {agent_id} and submitted for verification (ID: {verification_id})")
        return verification_id
    
    async def fail_task(self, task_id: str, agent_id: str, reason: str = "") -> bool:
        """Mark task as failed"""
        if task_id not in self.tasks:
            return False
        
        task = self.tasks[task_id]
        
        if task.assigned_agent != agent_id:
            return False
        
        # Update task
        task.status = TaskStatus.FAILED
        task.retry_count += 1
        
        # Update agent
        agent = self.agents[agent_id]
        agent.current_load -= 1
        
        if task_id in self.agent_assignments[agent_id]:
            self.agent_assignments[agent_id].remove(task_id)
        
        # Track failure in monitoring system
        self.monitoring_system.track_task_completed(task, agent_id, False)
        
        # Update performance metrics
        self.performance_metrics["failed_tasks"] += 1
        self._update_agent_performance(agent, task, False)
        
        # Try automatic reassignment first
        reassignment_attempted = await self.reassignment_system.handle_task_failure(task_id, agent_id, reason)
        
        if not reassignment_attempted:
            # Fallback to retry logic if reassignment didn't happen
            if task.retry_count < task.max_retries:
                task.assigned_agent = None
                task.status = TaskStatus.PENDING
                task.assigned_at = None
                self._add_to_queue(task)
                logger.info(f"Retrying task {task_id} (attempt {task.retry_count + 1})")
            else:
                logger.error(f"Task {task_id} failed permanently after {task.retry_count} attempts")
        else:
            logger.info(f"Task {task_id} failure triggered automatic reassignment")
        
        return True
    
    def _update_agent_performance(self, agent: AgentProfile, task: Task, success: bool):
        """Update agent performance metrics"""
        # Update success rate
        total_tasks = len(agent.task_history) + 1
        current_successes = agent.success_rate * len(agent.task_history)
        
        if success:
            current_successes += 1
        
        agent.success_rate = current_successes / total_tasks
        
        # Update average completion time
        if success and task.actual_duration:
            if agent.average_completion_time:
                # Running average
                agent.average_completion_time = (
                    agent.average_completion_time * len(agent.task_history) + task.actual_duration
                ) / total_tasks
            else:
                agent.average_completion_time = task.actual_duration
        
        # Update performance score based on success rate and completion time
        agent.performance_score = agent.success_rate * 0.7 + 0.3  # Base score
        
        # Add to task history
        agent.task_history.append(task.id)
        
        # Keep only recent history
        if len(agent.task_history) > 100:
            agent.task_history = agent.task_history[-100:]
    
    async def _check_dependent_tasks(self, completed_task_id: str):
        """Check if any dependent tasks can now be assigned"""
        dependent_tasks = self.dependents_graph.get(completed_task_id, set())
        
        for dependent_task_id in dependent_tasks:
            if dependent_task_id in self.tasks:
                task = self.tasks[dependent_task_id]
                if task.status == TaskStatus.PENDING and self._can_be_assigned(task):
                    self._add_to_queue(task)
    
    async def _monitor_loop(self):
        """Background monitoring loop"""
        while self.running:
            try:
                await self._monitor_tasks()
                await asyncio.sleep(30)  # Check every 30 seconds
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Monitor loop error: {e}")
                await asyncio.sleep(5)
    
    async def _monitor_tasks(self):
        """Monitor task progress and handle timeouts"""
        now = datetime.utcnow()
        
        for task in self.tasks.values():
            # Check for deadline violations
            if task.deadline and task.deadline < now and task.status not in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                logger.warning(f"Task {task.id} missed deadline")
                # Could trigger escalation or reassignment
            
            # Check for stalled tasks
            if task.status == TaskStatus.IN_PROGRESS and task.started_at:
                time_running = now - task.started_at
                if task.estimated_duration and time_running > task.estimated_duration * 2:
                    logger.warning(f"Task {task.id} is taking longer than expected")
                    # Could trigger check-in or reassignment
    
    async def _rebalance_loop(self):
        """Background rebalancing loop"""
        while self.running:
            try:
                await self._rebalance_tasks()
                await asyncio.sleep(self.rebalance_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Rebalance loop error: {e}")
                await asyncio.sleep(60)
    
    async def _rebalance_tasks(self):
        """Rebalance task assignments across agents"""
        # Find overloaded and underloaded agents
        overloaded_agents = [
            agent for agent in self.agents.values()
            if agent.current_load > agent.max_concurrent_tasks * 0.8
        ]
        
        underloaded_agents = [
            agent for agent in self.agents.values()
            if agent.current_load < agent.max_concurrent_tasks * 0.5
        ]
        
        if not overloaded_agents or not underloaded_agents:
            return
        
        # Try to move tasks from overloaded to underloaded agents
        for overloaded_agent in overloaded_agents:
            assigned_tasks = self.agent_assignments.get(overloaded_agent.agent_id, [])
            
            for task_id in assigned_tasks[:]:  # Copy list to avoid modification during iteration
                if task_id not in self.tasks:
                    continue
                
                task = self.tasks[task_id]
                
                # Only reassign pending tasks
                if task.status != TaskStatus.ASSIGNED:
                    continue
                
                # Find suitable underloaded agent
                suitable_agents = [
                    agent for agent in underloaded_agents
                    if agent.can_handle_task(task)
                ]
                
                if suitable_agents:
                    # Choose best agent
                    best_agent = max(suitable_agents, key=lambda x: x.performance_score)
                    
                    # Reassign task
                    await self._reassign_task(task, best_agent.agent_id)
                    
                    logger.info(f"Rebalanced task {task_id} from {overloaded_agent.agent_id} to {best_agent.agent_id}")
                    break
    
    async def _reassign_task(self, task: Task, new_agent_id: str):
        """Reassign a task to a different agent"""
        # Remove from old agent
        if task.assigned_agent:
            old_agent = self.agents[task.assigned_agent]
            old_agent.current_load -= 1
            
            if task.id in self.agent_assignments[task.assigned_agent]:
                self.agent_assignments[task.assigned_agent].remove(task.id)
        
        # Assign to new agent
        await self._assign_task_to_agent(task, new_agent_id)
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get task status"""
        if task_id not in self.tasks:
            return None
        
        task = self.tasks[task_id]
        return task.to_dict()
    
    def get_agent_status(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Get agent status"""
        if agent_id not in self.agents:
            return None
        
        agent = self.agents[agent_id]
        assigned_tasks = self.agent_assignments.get(agent_id, [])
        
        return {
            **agent.to_dict(),
            "assigned_tasks": assigned_tasks,
            "load_percentage": agent.get_load_percentage()
        }
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status"""
        total_agents = len(self.agents)
        available_agents = sum(1 for agent in self.agents.values() if agent.availability)
        
        task_counts = {}
        for status in TaskStatus:
            task_counts[status.value] = sum(1 for task in self.tasks.values() if task.status == status)
        
        return {
            "total_agents": total_agents,
            "available_agents": available_agents,
            "queue_size": len(self.task_queue),
            "task_counts": task_counts,
            "performance_metrics": self.performance_metrics,
            "assignment_strategy": self.current_strategy,
            "priority_queue_stats": self.priority_queue_manager.get_queue_statistics()
        }
    
    # Priority Queue Management Methods
    
    def get_priority_queue_status(self) -> Dict[str, Any]:
        """Get detailed priority queue status"""
        return self.priority_queue_manager.get_queue_statistics()
    
    def rebalance_priority_queues(self) -> Dict[str, int]:
        """Rebalance priority queues for optimal performance"""
        return self.priority_queue_manager.rebalance_queues()
    
    def promote_aged_tasks(self) -> int:
        """Promote tasks that have been waiting too long"""
        return self.priority_queue_manager.promote_aged_tasks()
    
    def get_task_queue_position(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get position of task in priority queue"""
        return self.priority_queue_manager.get_task_queue_position(task_id)
    
    def update_task_priority_in_queue(self, task_id: str, new_priority: TaskPriority) -> bool:
        """Update task priority in queue"""
        return self.priority_queue_manager.update_task_priority(task_id, new_priority)
    
    def clear_priority_queue(self, queue_name: str) -> int:
        """Clear specific priority queue"""
        return self.priority_queue_manager.clear_queue(queue_name)
    
    def add_task_to_specific_queue(self, task_id: str, queue_name: str) -> bool:
        """Add task to specific priority queue"""
        if task_id not in self.tasks:
            return False
        
        task = self.tasks[task_id]
        return self.priority_queue_manager.add_task_to_queue(task, queue_name)
    
    async def process_priority_queue_batch(self, batch_size: int = 10) -> List[str]:
        """Process multiple tasks from priority queues in batch"""
        assigned_tasks = []
        
        for _ in range(batch_size):
            task_id = await self.assign_next_task()
            if task_id:
                assigned_tasks.append(task_id)
            else:
                break  # No more tasks available
        
        return assigned_tasks
    
    async def emergency_queue_processing(self) -> Dict[str, Any]:
        """Process emergency/critical tasks immediately"""
        results = {
            "processed_urgent": 0,
            "processed_critical": 0,
            "failed_assignments": []
        }
        
        # Process all urgent and critical tasks first
        while True:
            task = self.priority_queue_manager.get_next_task(preferred_queue="urgent")
            if not task:
                break
            
            if task.priority == TaskPriority.URGENT:
                results["processed_urgent"] += 1
            elif task.priority == TaskPriority.CRITICAL:
                results["processed_critical"] += 1
            
            # Try emergency assignment
            agent_id = await self.emergency_assignment(task.id)
            if not agent_id:
                results["failed_assignments"].append(task.id)
        
        return results
    
    def optimize_queue_performance(self) -> Dict[str, Any]:
        """Optimize queue performance based on metrics"""
        stats = self.priority_queue_manager.get_queue_statistics()
        optimizations = {
            "rebalanced": False,
            "promoted_tasks": 0,
            "recommendations": []
        }
        
        # Check for queue health issues
        queue_health = stats["queue_health"]
        
        for queue_name, health in queue_health.items():
            if health == "critical":
                optimizations["recommendations"].append(f"Queue {queue_name} is critically full - consider expanding capacity")
            elif health == "high":
                optimizations["recommendations"].append(f"Queue {queue_name} has high load - monitor closely")
        
        # Auto-rebalance if needed
        if any(health in ["critical", "high"] for health in queue_health.values()):
            rebalance_stats = self.rebalance_priority_queues()
            optimizations["rebalanced"] = True
            optimizations["rebalance_stats"] = rebalance_stats
        
        # Promote aged tasks
        promoted_count = self.promote_aged_tasks()
        optimizations["promoted_tasks"] = promoted_count
        
        if promoted_count > 0:
            optimizations["recommendations"].append(f"Promoted {promoted_count} aged tasks to prevent starvation")
        
        # Check fairness
        fairness_stats = stats["fairness_stats"]
        process_counts = fairness_stats["queue_process_count"]
        
        if process_counts:
            max_processed = max(process_counts.values())
            min_processed = min(process_counts.values())
            
            if max_processed > min_processed * 3:  # Significant imbalance
                optimizations["recommendations"].append("Queue processing is imbalanced - consider fairness adjustments")
        
        return optimizations
    
    # Task Monitoring and Tracking Methods
    
    def get_task_monitoring_metrics(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed monitoring metrics for a task"""
        return self.monitoring_system.get_task_metrics(task_id)
    
    def get_agent_monitoring_metrics(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed monitoring metrics for an agent"""
        return self.monitoring_system.get_agent_metrics(agent_id)
    
    def get_system_monitoring_metrics(self) -> Dict[str, Any]:
        """Get overall system monitoring metrics"""
        return self.monitoring_system.get_system_metrics()
    
    def get_recent_alerts(self, severity: str = None, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent monitoring alerts"""
        return self.monitoring_system.get_recent_alerts(severity, limit)
    
    def get_performance_report(self, hours: int = 24) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        return self.monitoring_system.get_performance_report(hours)
    
    def track_task_progress(self, task_id: str, progress: float, message: str = "") -> bool:
        """Track progress update for a task"""
        if task_id not in self.tasks:
            return False
        
        # Update task progress
        task = self.tasks[task_id]
        task.progress = progress
        
        # Track in monitoring system
        self.monitoring_system.track_task_progress(task_id, progress, message)
        
        return True
    
    async def reassign_task(self, task_id: str, new_agent_id: str, reason: str = "") -> bool:
        """Reassign a task to a different agent with monitoring"""
        if task_id not in self.tasks or new_agent_id not in self.agents:
            return False
        
        task = self.tasks[task_id]
        old_agent_id = task.assigned_agent
        
        if not old_agent_id:
            return False
        
        # Track reassignment in monitoring system
        self.monitoring_system.track_task_reassigned(task_id, old_agent_id, new_agent_id, reason)
        
        # Perform reassignment
        await self._reassign_task(task, new_agent_id)
        
        return True
    
    def set_monitoring_alert_threshold(self, threshold_name: str, value: float) -> bool:
        """Set monitoring alert threshold"""
        if threshold_name in self.monitoring_system.alert_thresholds:
            self.monitoring_system.alert_thresholds[threshold_name] = value
            logger.info(f"Updated alert threshold {threshold_name} to {value}")
            return True
        return False
    
    def get_monitoring_alert_thresholds(self) -> Dict[str, float]:
        """Get current monitoring alert thresholds"""
        return self.monitoring_system.alert_thresholds.copy()
    
    def enable_monitoring(self) -> bool:
        """Enable task monitoring"""
        self.monitoring_system.monitoring_enabled = True
        logger.info("Task monitoring enabled")
        return True
    
    def disable_monitoring(self) -> bool:
        """Disable task monitoring"""
        self.monitoring_system.monitoring_enabled = False
        logger.info("Task monitoring disabled")
        return True
    
    async def run_health_check(self) -> Dict[str, Any]:
        """Run immediate health check on all tasks and agents"""
        await self.monitoring_system._perform_health_checks()
        
        health_report = {
            "timestamp": datetime.utcnow().isoformat(),
            "task_health": {},
            "agent_health": {},
            "system_health": "healthy"
        }
        
        # Collect task health
        unhealthy_tasks = 0
        for task_id, metrics in self.monitoring_system.task_metrics.items():
            health_status = metrics.get("health_status", "unknown")
            health_report["task_health"][task_id] = health_status
            if health_status != "healthy":
                unhealthy_tasks += 1
        
        # Collect agent health
        unhealthy_agents = 0
        for agent_id, metrics in self.monitoring_system.agent_metrics.items():
            health_status = metrics.get("health_status", "unknown")
            health_report["agent_health"][agent_id] = health_status
            if health_status != "healthy":
                unhealthy_agents += 1
        
        # Determine overall system health
        if unhealthy_tasks > len(self.monitoring_system.task_metrics) * 0.3:
            health_report["system_health"] = "critical"
        elif unhealthy_tasks > len(self.monitoring_system.task_metrics) * 0.1:
            health_report["system_health"] = "warning"
        elif unhealthy_agents > 0:
            health_report["system_health"] = "warning"
        
        health_report["summary"] = {
            "total_tasks": len(self.monitoring_system.task_metrics),
            "unhealthy_tasks": unhealthy_tasks,
            "total_agents": len(self.monitoring_system.agent_metrics),
            "unhealthy_agents": unhealthy_agents
        }
        
        return health_report
    
    def get_task_timeline(self, task_id: str) -> Optional[List[Dict[str, Any]]]:
        """Get timeline of events for a task"""
        metrics = self.monitoring_system.get_task_metrics(task_id)
        if not metrics:
            return None
        
        timeline = []
        
        # Add creation event
        if "created_at" in metrics:
            timeline.append({
                "timestamp": metrics["created_at"],
                "event": "created",
                "description": "Task created"
            })
        
        # Add status history
        if "status_history" in metrics:
            for status, timestamp in metrics["status_history"]:
                timeline.append({
                    "timestamp": timestamp,
                    "event": "status_change",
                    "description": f"Status changed to {status}"
                })
        
        # Add assignment events
        if "assigned_at" in metrics:
            timeline.append({
                "timestamp": metrics["assigned_at"],
                "event": "assigned",
                "description": f"Assigned to agent {metrics.get('assigned_agent', 'unknown')}"
            })
        
        # Add progress updates
        if "progress_updates" in metrics:
            for update in metrics["progress_updates"]:
                timeline.append({
                    "timestamp": update["timestamp"],
                    "event": "progress_update",
                    "description": f"Progress: {update['progress']:.1%}",
                    "message": update.get("message", "")
                })
        
        # Add reassignment events
        if "reassignment_history" in metrics:
            for reassignment in metrics["reassignment_history"]:
                timeline.append({
                    "timestamp": reassignment["timestamp"],
                    "event": "reassigned",
                    "description": f"Reassigned from {reassignment['from_agent']} to {reassignment['to_agent']}",
                    "reason": reassignment.get("reason", "")
                })
        
        # Add completion event
        if "completed_at" in metrics:
            success = metrics.get("success", False)
            timeline.append({
                "timestamp": metrics["completed_at"],
                "event": "completed",
                "description": f"Task {'completed successfully' if success else 'failed'}"
            })
        
        # Sort by timestamp
        timeline.sort(key=lambda x: x["timestamp"])
        
        return timeline
    
    def get_agent_performance_trend(self, agent_id: str, days: int = 7) -> Dict[str, Any]:
        """Get performance trend for an agent"""
        metrics = self.monitoring_system.get_agent_metrics(agent_id)
        if not metrics:
            return {"error": "Agent not found"}
        
        # This would typically analyze historical data
        # For now, return current metrics with trend indicators
        return {
            "agent_id": agent_id,
            "current_metrics": metrics,
            "trend_period": f"Last {days} days",
            "trends": {
                "success_rate": "stable",  # Would calculate from historical data
                "completion_time": "improving",  # Would calculate from historical data
                "task_load": "increasing"  # Would calculate from historical data
            }
        }
    
    # Automatic Reassignment Methods
    
    def get_reassignment_statistics(self) -> Dict[str, Any]:
        \"\"\"Get automatic reassignment statistics\"\"\"
        return self.reassignment_system.get_reassignment_statistics()
    
    def get_task_reassignment_history(self, task_id: str) -> List[Dict[str, Any]]:
        \"\"\"Get reassignment history for a task\"\"\"
        return self.reassignment_system.get_task_reassignment_history(task_id)
    
    def update_reassignment_config(self, config_updates: Dict[str, Any]) -> bool:
        \"\"\"Update automatic reassignment configuration\"\"\"
        return self.reassignment_system.update_config(config_updates)
    
    def get_reassignment_config(self) -> Dict[str, Any]:
        \"\"\"Get current reassignment configuration\"\"\"
        return self.reassignment_system.config.copy()
    
    def blacklist_agent(self, agent_id: str, duration_seconds: int = None, reason: str = \"manual\") -> bool:
        \"\"\"Blacklist an agent from receiving new assignments\"\"\"
        success = self.reassignment_system.blacklist_agent(agent_id, duration_seconds)
        
        if success:
            # Optionally reassign current tasks
            asyncio.create_task(self.reassignment_system.handle_agent_unavailable(agent_id, reason))
        
        return success
    
    def remove_agent_blacklist(self, agent_id: str) -> bool:
        \"\"\"Remove agent from blacklist\"\"\"
        return self.reassignment_system.remove_agent_blacklist(agent_id)
    
    def get_blacklisted_agents(self) -> List[Dict[str, Any]]:
        \"\"\"Get list of currently blacklisted agents\"\"\"
        stats = self.reassignment_system.get_reassignment_statistics()
        return stats[\"active_blacklisted_agents\"]
    
    async def manual_task_reassignment(self, task_id: str, target_agent_id: str, reason: str = \"manual\") -> bool:
        \"\"\"Manually reassign a task to a specific agent\"\"\"
        return await self.reassignment_system.manual_reassignment(task_id, target_agent_id, reason)
    
    async def trigger_load_balancing_reassignment(self) -> Dict[str, Any]:
        \"\"\"Manually trigger load balancing reassignment\"\"\"
        await self.reassignment_system._check_load_balancing_reassignment()
        return {\"triggered\": True, \"timestamp\": datetime.utcnow().isoformat()}
    
    async def handle_agent_performance_issues(self, agent_id: str) -> Dict[str, Any]:
        \"\"\"Handle agent with performance issues\"\"\"
        reassigned_tasks = await self.reassignment_system.handle_performance_degradation(agent_id)
        
        return {
            \"agent_id\": agent_id,
            \"reassigned_tasks\": reassigned_tasks,
            \"reassignment_count\": len(reassigned_tasks),
            \"timestamp\": datetime.utcnow().isoformat()
        }
    
    async def emergency_task_evacuation(self, agent_id: str, reason: str = \"emergency\") -> Dict[str, Any]:
        \"\"\"Emergency evacuation of all tasks from an agent\"\"\"
        reassigned_tasks = await self.reassignment_system.handle_agent_unavailable(agent_id, reason)
        
        # Also blacklist the agent temporarily
        self.reassignment_system.blacklist_agent(agent_id, 3600)  # 1 hour
        
        return {
            \"agent_id\": agent_id,
            \"evacuated_tasks\": reassigned_tasks,
            \"evacuation_count\": len(reassigned_tasks),
            \"reason\": reason,
            \"agent_blacklisted\": True,
            \"timestamp\": datetime.utcnow().isoformat()
        }
    
    def enable_automatic_reassignment(self) -> bool:
        \"\"\"Enable automatic reassignment system\"\"\"
        self.reassignment_system.config[\"enable_automatic_reassignment\"] = True
        logger.info(\"Automatic reassignment enabled\")
        return True
    
    def disable_automatic_reassignment(self) -> bool:
        \"\"\"Disable automatic reassignment system\"\"\"
        self.reassignment_system.config[\"enable_automatic_reassignment\"] = False
        logger.info(\"Automatic reassignment disabled\")
        return True
    
    def enable_proactive_reassignment(self) -> bool:
        \"\"\"Enable proactive reassignment monitoring\"\"\"
        self.reassignment_system.config[\"enable_proactive_reassignment\"] = True
        logger.info(\"Proactive reassignment enabled\")
        return True
    
    def disable_proactive_reassignment(self) -> bool:
        \"\"\"Disable proactive reassignment monitoring\"\"\"
        self.reassignment_system.config[\"enable_proactive_reassignment\"] = False
        logger.info(\"Proactive reassignment disabled\")
        return True
    
    async def run_reassignment_health_check(self) -> Dict[str, Any]:
        \"\"\"Run health check for reassignment system\"\"\"
        stats = self.get_reassignment_statistics()
        
        health_report = {
            \"timestamp\": datetime.utcnow().isoformat(),
            \"system_health\": \"healthy\",
            \"issues\": [],
            \"recommendations\": []
        }
        
        # Check reassignment success rate
        total_reassignments = stats[\"stats\"][\"total_reassignments\"]
        failed_reassignments = stats[\"stats\"][\"failed_reassignments\"]
        
        if total_reassignments > 0:
            failure_rate = failed_reassignments / total_reassignments
            if failure_rate > 0.2:  # More than 20% failure rate
                health_report[\"system_health\"] = \"warning\"\n                health_report[\"issues\"].append(f\"High reassignment failure rate: {failure_rate:.1%}\")
                health_report[\"recommendations\"].append(\"Review agent capabilities and task requirements\")
        
        # Check for frequently reassigned tasks
        task_counts = stats[\"task_reassignment_counts\"]
        frequently_reassigned = [task_id for task_id, count in task_counts.items() if count > 2]
        
        if frequently_reassigned:
            health_report[\"system_health\"] = \"warning\"
            health_report[\"issues\"].append(f\"{len(frequently_reassigned)} tasks have been reassigned multiple times\")
            health_report[\"recommendations\"].append(\"Review task specifications and agent matching criteria\")
        
        # Check for blacklisted agents
        blacklisted_agents = stats[\"active_blacklisted_agents\"]
        if len(blacklisted_agents) > len(self.agents) * 0.3:  # More than 30% of agents blacklisted
            health_report[\"system_health\"] = \"critical\"
            health_report[\"issues\"].append(f\"{len(blacklisted_agents)} agents are currently blacklisted\")
            health_report[\"recommendations\"].append(\"Investigate agent reliability issues\")
        
        # Check average reassignment time
        avg_time = stats[\"stats\"][\"avg_reassignment_time\"]
        if avg_time > 60:  # More than 1 minute average
            health_report[\"system_health\"] = \"warning\"
            health_report[\"issues\"].append(f\"High average reassignment time: {avg_time:.1f} seconds\")
            health_report[\"recommendations\"].append(\"Optimize agent selection algorithm\")
        
        return health_report
    
    def get_reassignment_insights(self) -> Dict[str, Any]:
        \"\"\"Get insights about reassignment patterns\"\"\"
        stats = self.get_reassignment_statistics()
        
        insights = {
            \"timestamp\": datetime.utcnow().isoformat(),
            \"patterns\": {},
            \"trends\": {},
            \"suggestions\": []
        }
        
        # Analyze reassignment reasons
        reason_counts = stats[\"stats\"][\"reassignment_reasons\"]
        total_reassignments = sum(reason_counts.values())
        
        if total_reassignments > 0:
            insights[\"patterns\"][\"top_reassignment_reasons\"] = [\n                {\"reason\": reason, \"count\": count, \"percentage\": count / total_reassignments * 100}\n                for reason, count in sorted(reason_counts.items(), key=lambda x: x[1], reverse=True)\n            ]
            
            # Check for problematic patterns
            if reason_counts.get(\"task_failure\", 0) > total_reassignments * 0.4:\n                insights[\"suggestions\"].append(\"High task failure rate - consider reviewing task complexity or agent training\")
            
            if reason_counts.get(\"agent_unavailable\", 0) > total_reassignments * 0.3:\n                insights[\"suggestions\"].append(\"Frequent agent unavailability - consider agent reliability monitoring\")
            
            if reason_counts.get(\"performance_degradation\", 0) > total_reassignments * 0.2:\n                insights[\"suggestions\"].append(\"Performance issues detected - consider agent performance optimization\")
        
        # Analyze task reassignment frequency
        task_counts = stats[\"task_reassignment_counts\"]
        if task_counts:
            avg_reassignments = sum(task_counts.values()) / len(task_counts)
            insights[\"patterns\"][\"average_reassignments_per_task\"] = avg_reassignments
            
            if avg_reassignments > 1.5:\n                insights[\"suggestions\"].append(\"Tasks frequently reassigned - review task-agent matching algorithm\")
        
        # Analyze recent trends
        recent_reassignments = stats[\"recent_reassignments\"]\n        if len(recent_reassignments) >= 5:\n            recent_reasons = [r[\"reason\"] for r in recent_reassignments[-5:]]\n            most_recent_reason = max(set(recent_reasons), key=recent_reasons.count)\n            insights[\"trends\"][\"recent_dominant_reason\"] = most_recent_reason
            
            if most_recent_reason == \"deadline_pressure\":\n                insights[\"suggestions\"].append(\"Recent deadline pressure - consider workload distribution optimization\")
        
        return insights
    
    # Task Completion Verification Methods
    
    def register_reviewer(self, reviewer_id: str, reviewer_info: Dict[str, Any]) -> bool:
        \"\"\"Register a task reviewer\"\"\"
        try:
            self.verification_system.register_reviewer(reviewer_id, reviewer_info)
            logger.info(f\"Registered reviewer {reviewer_id}\")
            return True
        except Exception as e:
            logger.error(f\"Failed to register reviewer {reviewer_id}: {e}\")
            return False
    
    async def submit_peer_review(self, task_id: str, reviewer_id: str, review_data: Dict[str, Any]) -> bool:
        \"\"\"Submit a peer review for a task\"\"\"
        return await self.verification_system.submit_peer_review(task_id, reviewer_id, review_data)
    
    def get_verification_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        \"\"\"Get verification status for a task\"\"\"
        return self.verification_system.get_verification_status(task_id)
    
    def get_verification_metrics(self) -> Dict[str, Any]:
        \"\"\"Get verification system metrics\"\"\"
        return self.verification_system.get_verification_metrics()
    
    def get_reviewer_performance(self, reviewer_id: str) -> Optional[Dict[str, Any]]:
        \"\"\"Get performance metrics for a reviewer\"\"\"
        return self.verification_system.get_reviewer_performance(reviewer_id)
    
    def update_verification_config(self, config_updates: Dict[str, Any]) -> bool:
        \"\"\"Update verification system configuration\"\"\"
        return self.verification_system.update_verification_config(config_updates)
    
    def get_verification_config(self) -> Dict[str, Any]:
        \"\"\"Get current verification configuration\"\"\"
        return self.verification_system.config.copy()
    
    def enable_verification(self) -> bool:
        \"\"\"Enable task verification system\"\"\"
        self.verification_system.config[\"enable_verification\"] = True
        logger.info(\"Task verification enabled\")
        return True
    
    def disable_verification(self) -> bool:
        \"\"\"Disable task verification system\"\"\"
        self.verification_system.config[\"enable_verification\"] = False
        logger.info(\"Task verification disabled\")
        return True
    
    def enable_peer_review(self) -> bool:
        \"\"\"Enable mandatory peer review\"\"\"
        self.verification_system.config[\"require_peer_review\"] = True
        logger.info(\"Mandatory peer review enabled\")
        return True
    
    def disable_peer_review(self) -> bool:
        \"\"\"Disable mandatory peer review\"\"\"
        self.verification_system.config[\"require_peer_review\"] = False
        logger.info(\"Mandatory peer review disabled\")
        return True
    
    def set_verification_threshold(self, threshold: float) -> bool:
        \"\"\"Set quality score threshold for verification\"\"\"
        if 0.0 <= threshold <= 1.0:
            self.verification_system.config[\"quality_score_threshold\"] = threshold
            logger.info(f\"Verification quality threshold set to {threshold}\")
            return True
        return False
    
    def set_auto_approval_threshold(self, threshold: float) -> bool:
        \"\"\"Set auto-approval threshold\"\"\"
        if 0.0 <= threshold <= 1.0:
            self.verification_system.config[\"auto_approve_threshold\"] = threshold
            logger.info(f\"Auto-approval threshold set to {threshold}\")
            return True
        return False
    
    async def manual_task_approval(self, task_id: str, approver_id: str, reason: str = \"manual approval\") -> bool:
        \"\"\"Manually approve a task verification\"\"\"
        verification_data = self.verification_system.get_verification_status(task_id)
        if not verification_data or verification_data[\"status\"] != \"pending\":
            return False
        
        # Update verification data
        verification_data[\"status\"] = \"approved\"
        verification_data[\"approved_at\"] = datetime.utcnow()
        verification_data[\"approval_reason\"] = f\"Manual approval by {approver_id}: {reason}\"
        verification_data[\"approved_by\"] = approver_id
        
        # Complete the approval process
        await self.verification_system._approve_verification(verification_data, verification_data[\"approval_reason\"])
        
        logger.info(f\"Task {task_id} manually approved by {approver_id}\")
        return True
    
    async def manual_task_rejection(self, task_id: str, rejector_id: str, reason: str = \"manual rejection\") -> bool:
        \"\"\"Manually reject a task verification\"\"\"
        verification_data = self.verification_system.get_verification_status(task_id)
        if not verification_data or verification_data[\"status\"] != \"pending\":
            return False
        
        # Update verification data
        verification_data[\"status\"] = \"rejected\"
        verification_data[\"rejected_at\"] = datetime.utcnow()
        verification_data[\"rejection_reason\"] = f\"Manual rejection by {rejector_id}: {reason}\"
        verification_data[\"rejected_by\"] = rejector_id
        
        # Complete the rejection process
        await self.verification_system._reject_verification(verification_data, verification_data[\"rejection_reason\"])
        
        logger.info(f\"Task {task_id} manually rejected by {rejector_id}\")
        return True
    
    def get_pending_verifications(self) -> List[Dict[str, Any]]:
        \"\"\"Get list of tasks pending verification\"\"\"
        return [\n            {\n                \"task_id\": task_id,\n                \"verification_id\": data[\"verification_id\"],\n                \"submitted_at\": data[\"submitted_at\"].isoformat(),\n                \"status\": data[\"status\"],\n                \"quality_score\": data[\"quality_score\"],\n                \"estimated_completion\": data[\"estimated_completion\"].isoformat(),\n                \"reviewer_assignments\": len(data[\"reviewer_assignments\"])\n            }\n            for task_id, data in self.verification_system.pending_verifications.items()\n        ]
    
    def get_verification_queue_status(self) -> Dict[str, Any]:
        \"\"\"Get verification queue status\"\"\"
        pending = self.verification_system.pending_verifications
        
        status_counts = defaultdict(int)
        priority_counts = defaultdict(int)
        
        for verification_data in pending.values():
            status_counts[verification_data[\"status\"]] += 1
            \n            task_id = verification_data[\"task_id\"]\n            if task_id in self.tasks:\n                priority_counts[self.tasks[task_id].priority.value] += 1
        
        return {\n            \"total_pending\": len(pending),\n            \"status_breakdown\": dict(status_counts),\n            \"priority_breakdown\": dict(priority_counts),\n            \"queue_size\": len(self.verification_system.verification_queue),\n            \"active_reviewers\": len([r for r in self.verification_system.reviewers.values() if r[\"current_reviews\"] > 0])\n        }
    
    async def run_verification_health_check(self) -> Dict[str, Any]:
        \"\"\"Run health check for verification system\"\"\"
        metrics = self.get_verification_metrics()
        \n        health_report = {\n            \"timestamp\": datetime.utcnow().isoformat(),\n            \"system_health\": \"healthy\",\n            \"issues\": [],\n            \"recommendations\": []\n        }
        \n        # Check verification backlog\n        pending_count = metrics[\"pending_verifications\"]\n        if pending_count > 50:\n            health_report[\"system_health\"] = \"warning\"\n            health_report[\"issues\"].append(f\"High verification backlog: {pending_count} pending verifications\")\n            health_report[\"recommendations\"].append(\"Consider adding more reviewers or enabling auto-approval\")\n        \n        # Check reviewer availability\n        total_reviewers = metrics[\"total_reviewers\"]\n        active_reviewers = metrics[\"active_reviewers\"]\n        \n        if total_reviewers == 0:\n            health_report[\"system_health\"] = \"critical\"\n            health_report[\"issues\"].append(\"No reviewers registered\")\n            health_report[\"recommendations\"].append(\"Register reviewers for peer review process\")\n        elif active_reviewers / total_reviewers > 0.8:\n            health_report[\"system_health\"] = \"warning\"\n            health_report[\"issues\"].append(\"Most reviewers are overloaded\")\n            health_report[\"recommendations\"].append(\"Consider adding more reviewers or balancing workload\")\n        \n        # Check approval/rejection rates\n        quality_metrics = metrics[\"quality_metrics\"]\n        total_verifications = quality_metrics[\"total_verifications\"]\n        \n        if total_verifications > 10:\n            rejection_rate = quality_metrics[\"rejected_tasks\"] / total_verifications\n            if rejection_rate > 0.3:\n                health_report[\"system_health\"] = \"warning\"\n                health_report[\"issues\"].append(f\"High rejection rate: {rejection_rate:.1%}\")\n                health_report[\"recommendations\"].append(\"Review task quality standards and agent training\")\n        \n        # Check verification times\n        avg_time = quality_metrics[\"avg_verification_time\"]\n        if avg_time > 3600:  # More than 1 hour\n            health_report[\"system_health\"] = \"warning\"\n            health_report[\"issues\"].append(f\"High average verification time: {avg_time/60:.1f} minutes\")\n            health_report[\"recommendations\"].append(\"Optimize verification process or increase reviewer capacity\")\n        \n        return health_report
    \n    def get_verification_insights(self) -> Dict[str, Any]:
        \"\"\"Get insights about verification patterns\"\"\"
        metrics = self.get_verification_metrics()
        quality_metrics = metrics[\"quality_metrics\"]\n        \n        insights = {\n            \"timestamp\": datetime.utcnow().isoformat(),\n            \"patterns\": {},\n            \"trends\": {},\n            \"suggestions\": []\n        }
        \n        # Quality score distribution analysis\n        score_distribution = quality_metrics[\"quality_score_distribution\"]\n        if score_distribution:\n            avg_quality_score = sum(score * count for score, count in score_distribution.items()) / sum(score_distribution.values())\n            insights[\"patterns\"][\"average_quality_score\"] = avg_quality_score\n            \n            low_quality_tasks = sum(count for score, count in score_distribution.items() if score < 0.7)\n            total_tasks = sum(score_distribution.values())\n            \n            if low_quality_tasks / total_tasks > 0.3:\n                insights[\"suggestions\"].append(\"High proportion of low-quality task completions - consider additional training\")\n        \n        # Rejection reasons analysis\n        rejection_reasons = quality_metrics[\"common_rejection_reasons\"]\n        if rejection_reasons:\n            top_rejection_reason = max(rejection_reasons.items(), key=lambda x: x[1])\n            insights[\"patterns\"][\"top_rejection_reason\"] = {\n                \"reason\": top_rejection_reason[0],\n                \"count\": top_rejection_reason[1]\n            }\n            \n            if \"validation\" in top_rejection_reason[0].lower():\n                insights[\"suggestions\"].append(\"Validation issues are common - review output requirements and standards\")\n        \n        # Approval trends\n        total_verifications = quality_metrics[\"total_verifications\"]\n        if total_verifications > 0:\n            approval_rate = quality_metrics[\"approved_tasks\"] / total_verifications\n            insights[\"patterns\"][\"approval_rate\"] = approval_rate\n            \n            if approval_rate < 0.6:\n                insights[\"suggestions\"].append(\"Low approval rate - consider reviewing verification criteria\")\n            elif approval_rate > 0.95:\n                insights[\"suggestions\"].append(\"Very high approval rate - verification criteria might be too lenient\")\n        \n        return insights"

class TaskPriorityQueueManager:
    """
    Advanced task priority queue management system
    Handles multiple priority queues with intelligent scheduling
    """
    
    def __init__(self):
        # Multiple priority queues for different scenarios
        self.queues = {
            "urgent": [],  # Critical and urgent tasks
            "high": [],   # High priority tasks
            "normal": [], # Normal priority tasks
            "low": [],    # Low priority tasks
            "scheduled": []  # Scheduled tasks
        }
        
        # Queue statistics
        self.queue_stats = {
            "urgent": {"processed": 0, "avg_wait_time": 0.0, "max_wait_time": 0.0},
            "high": {"processed": 0, "avg_wait_time": 0.0, "max_wait_time": 0.0},
            "normal": {"processed": 0, "avg_wait_time": 0.0, "max_wait_time": 0.0},
            "low": {"processed": 0, "avg_wait_time": 0.0, "max_wait_time": 0.0},
            "scheduled": {"processed": 0, "avg_wait_time": 0.0, "max_wait_time": 0.0}
        }
        
        # Queue configuration
        self.queue_config = {
            "max_size": 1000,
            "aging_threshold": 3600,  # 1 hour
            "promotion_factor": 1.5,
            "batch_size": 10,
            "starvation_threshold": 300  # 5 minutes
        }
        
        # Fairness tracking
        self.fairness_tracker = {
            "last_processed_queue": None,
            "queue_process_count": defaultdict(int),
            "starvation_timers": defaultdict(float)
        }
        
        # Task metadata for queue management
        self.task_metadata = {}  # task_id -> metadata
        
        logger.info("TaskPriorityQueueManager initialized")
    
    def add_task_to_queue(self, task: Task, queue_name: str = None) -> bool:
        """Add a task to the appropriate priority queue"""
        if queue_name is None:
            queue_name = self._determine_queue_for_task(task)
        
        if queue_name not in self.queues:
            logger.error(f"Invalid queue name: {queue_name}")
            return False
        
        if len(self.queues[queue_name]) >= self.queue_config["max_size"]:
            logger.warning(f"Queue {queue_name} is full, considering overflow handling")
            return self._handle_queue_overflow(task, queue_name)
        
        # Calculate priority score
        priority_score = self._calculate_priority_score(task)
        
        # Add to queue with priority
        queue_entry = {
            "task": task,
            "priority_score": priority_score,
            "added_at": datetime.utcnow(),
            "queue_name": queue_name,
            "promoted": False
        }
        
        # Insert maintaining priority order
        heapq.heappush(self.queues[queue_name], (-priority_score, task.id, queue_entry))
        
        # Store metadata
        self.task_metadata[task.id] = queue_entry
        
        logger.debug(f"Added task {task.id} to {queue_name} queue with priority {priority_score}")
        return True
    
    def _determine_queue_for_task(self, task: Task) -> str:
        """Determine appropriate queue for a task"""
        # Check if task is scheduled for future
        if "scheduled_assignment" in task.context:
            return "scheduled"
        
        # Priority-based queue assignment
        if task.priority in [TaskPriority.CRITICAL, TaskPriority.URGENT]:
            return "urgent"
        elif task.priority == TaskPriority.HIGH:
            return "high"
        elif task.priority == TaskPriority.NORMAL:
            return "normal"
        else:
            return "low"
    
    def _calculate_priority_score(self, task: Task) -> float:
        """Calculate detailed priority score for queue ordering"""
        score = 0.0
        
        # Base priority score
        priority_values = {
            TaskPriority.LOW: 1.0,
            TaskPriority.NORMAL: 2.0,
            TaskPriority.HIGH: 3.0,
            TaskPriority.URGENT: 4.0,
            TaskPriority.CRITICAL: 5.0
        }
        
        score += priority_values.get(task.priority, 2.0) * 10
        
        # Deadline urgency
        if task.deadline:
            time_to_deadline = task.deadline - datetime.utcnow()
            hours_remaining = time_to_deadline.total_seconds() / 3600
            
            if hours_remaining < 0:
                score += 100  # Overdue
            elif hours_remaining < 1:
                score += 50
            elif hours_remaining < 6:
                score += 20
            elif hours_remaining < 24:
                score += 10
            elif hours_remaining < 168:  # 1 week
                score += 5
        
        # Dependency blocking factor
        # Tasks that block many others get higher priority
        blocking_factor = task.context.get("blocking_count", 0)
        score += blocking_factor * 2
        
        # Task age factor (prevent starvation)
        task_age = datetime.utcnow() - task.created_at
        hours_old = task_age.total_seconds() / 3600
        score += min(hours_old / 24, 5.0)  # Max 5 points for age
        
        # Retry penalty (lower priority for retried tasks)
        if task.retry_count > 0:
            score -= task.retry_count * 2
        
        # Estimated effort factor
        if task.estimated_duration:
            hours = task.estimated_duration.total_seconds() / 3600
            # Slightly favor shorter tasks for better throughput
            score += max(0, (24 - hours) / 24) * 2
        
        return max(score, 0.1)  # Minimum score
    
    def _handle_queue_overflow(self, task: Task, queue_name: str) -> bool:
        """Handle queue overflow situations"""
        # Try to promote lower priority tasks to make room
        if queue_name in ["normal", "low"]:
            # Remove oldest low priority task
            if self.queues[queue_name]:
                removed_entry = heapq.heappop(self.queues[queue_name])
                removed_task_id = removed_entry[1]
                
                # Try to put in lower priority queue
                lower_queue = "low" if queue_name == "normal" else None
                if lower_queue and len(self.queues[lower_queue]) < self.queue_config["max_size"]:
                    removed_task = removed_entry[2]["task"]
                    self.add_task_to_queue(removed_task, lower_queue)
                else:
                    # Log dropped task
                    logger.warning(f"Dropped task {removed_task_id} due to queue overflow")
                    if removed_task_id in self.task_metadata:
                        del self.task_metadata[removed_task_id]
                
                # Now add the new task
                return self.add_task_to_queue(task, queue_name)
        
        # For urgent/high priority queues, expand capacity temporarily
        if queue_name in ["urgent", "high"]:
            logger.warning(f"Expanding {queue_name} queue capacity due to overflow")
            # Just add the task anyway for critical situations
            priority_score = self._calculate_priority_score(task)
            queue_entry = {
                "task": task,
                "priority_score": priority_score,
                "added_at": datetime.utcnow(),
                "queue_name": queue_name,
                "promoted": False
            }
            heapq.heappush(self.queues[queue_name], (-priority_score, task.id, queue_entry))
            self.task_metadata[task.id] = queue_entry
            return True
        
        return False
    
    def get_next_task(self, preferred_queue: str = None) -> Optional[Task]:
        """Get the next task considering fairness and starvation prevention"""
        # Check scheduled tasks first
        scheduled_task = self._check_scheduled_tasks()
        if scheduled_task:
            return scheduled_task
        
        # Apply fairness algorithm
        if preferred_queue:
            task = self._get_task_from_queue(preferred_queue)
            if task:
                return task
        
        # Check queues in order of priority with fairness consideration
        queue_order = self._determine_queue_order()
        
        for queue_name in queue_order:
            task = self._get_task_from_queue(queue_name)
            if task:
                return task
        
        return None
    
    def _check_scheduled_tasks(self) -> Optional[Task]:
        """Check if any scheduled tasks are ready"""
        now = datetime.utcnow()
        ready_tasks = []
        
        for entry in self.queues["scheduled"]:
            task = entry[2]["task"]
            if "scheduled_assignment" in task.context:
                scheduled_time = datetime.fromisoformat(task.context["scheduled_assignment"])
                if scheduled_time <= now:
                    ready_tasks.append(entry)
        
        if ready_tasks:
            # Get the earliest scheduled task
            earliest_entry = min(ready_tasks, key=lambda x: x[2]["task"].context["scheduled_assignment"])
            
            # Remove from scheduled queue
            self.queues["scheduled"].remove(earliest_entry)
            heapq.heapify(self.queues["scheduled"])
            
            task = earliest_entry[2]["task"]
            
            # Remove from metadata
            if task.id in self.task_metadata:
                del self.task_metadata[task.id]
            
            # Update statistics
            self._update_queue_statistics("scheduled", earliest_entry[2])
            
            return task
        
        return None
    
    def _determine_queue_order(self) -> List[str]:
        """Determine queue processing order considering fairness"""
        now = datetime.utcnow().timestamp()
        
        # Check for starvation
        starving_queues = []
        for queue_name in ["low", "normal", "high", "urgent"]:
            if self.queues[queue_name]:  # Queue has tasks
                last_processed = self.fairness_tracker["starvation_timers"].get(queue_name, now)
                if now - last_processed > self.queue_config["starvation_threshold"]:
                    starving_queues.append(queue_name)
        
        # Prioritize starving queues
        if starving_queues:
            # Sort by starvation time (longest starving first)
            starving_queues.sort(key=lambda q: self.fairness_tracker["starvation_timers"].get(q, now))
            return starving_queues + [q for q in ["urgent", "high", "normal", "low"] if q not in starving_queues]
        
        # Normal priority order with round-robin fairness
        base_order = ["urgent", "high", "normal", "low"]
        
        # Apply round-robin for same priority levels
        last_processed = self.fairness_tracker["last_processed_queue"]
        if last_processed and last_processed in base_order:
            last_index = base_order.index(last_processed)
            # Rotate order to start after last processed
            base_order = base_order[last_index + 1:] + base_order[:last_index + 1]
        
        return base_order
    
    def _get_task_from_queue(self, queue_name: str) -> Optional[Task]:
        """Get task from specific queue"""
        if not self.queues[queue_name]:
            return None
        
        # Get highest priority task
        entry = heapq.heappop(self.queues[queue_name])
        task = entry[2]["task"]
        
        # Update fairness tracking
        self.fairness_tracker["last_processed_queue"] = queue_name
        self.fairness_tracker["queue_process_count"][queue_name] += 1
        self.fairness_tracker["starvation_timers"][queue_name] = datetime.utcnow().timestamp()
        
        # Remove from metadata
        if task.id in self.task_metadata:
            del self.task_metadata[task.id]
        
        # Update statistics
        self._update_queue_statistics(queue_name, entry[2])
        
        return task
    
    def _update_queue_statistics(self, queue_name: str, queue_entry: Dict[str, Any]):
        """Update queue statistics"""
        wait_time = (datetime.utcnow() - queue_entry["added_at"]).total_seconds()
        
        stats = self.queue_stats[queue_name]
        stats["processed"] += 1
        
        # Update average wait time
        if stats["processed"] == 1:
            stats["avg_wait_time"] = wait_time
        else:
            stats["avg_wait_time"] = (
                stats["avg_wait_time"] * (stats["processed"] - 1) + wait_time
            ) / stats["processed"]
        
        # Update max wait time
        stats["max_wait_time"] = max(stats["max_wait_time"], wait_time)
    
    def promote_aged_tasks(self) -> int:
        """Promote tasks that have been waiting too long"""
        now = datetime.utcnow()
        promoted_count = 0
        aging_threshold = self.queue_config["aging_threshold"]
        
        # Check lower priority queues for aged tasks
        for queue_name in ["low", "normal"]:
            target_queue = "normal" if queue_name == "low" else "high"
            
            aged_tasks = []
            remaining_tasks = []
            
            # Separate aged tasks from queue
            for entry in self.queues[queue_name]:
                task_entry = entry[2]
                age = (now - task_entry["added_at"]).total_seconds()
                
                if age > aging_threshold and not task_entry["promoted"]:
                    aged_tasks.append(entry)
                else:
                    remaining_tasks.append(entry)
            
            # Update queue with remaining tasks
            self.queues[queue_name] = remaining_tasks
            heapq.heapify(self.queues[queue_name])
            
            # Promote aged tasks
            for aged_entry in aged_tasks:
                task = aged_entry[2]["task"]
                task_entry = aged_entry[2]
                task_entry["promoted"] = True
                
                # Increase priority score
                new_priority = task_entry["priority_score"] * self.queue_config["promotion_factor"]
                
                # Add to higher priority queue
                promoted_entry = {
                    "task": task,
                    "priority_score": new_priority,
                    "added_at": task_entry["added_at"],
                    "queue_name": target_queue,
                    "promoted": True
                }
                
                heapq.heappush(self.queues[target_queue], (-new_priority, task.id, promoted_entry))
                self.task_metadata[task.id] = promoted_entry
                
                promoted_count += 1
                logger.info(f"Promoted task {task.id} from {queue_name} to {target_queue}")
        
        return promoted_count
    
    def rebalance_queues(self) -> Dict[str, int]:
        """Rebalance queues based on current load and priorities"""
        rebalance_stats = {"moved": 0, "promoted": 0, "demoted": 0}
        
        # Promote aged tasks
        rebalance_stats["promoted"] = self.promote_aged_tasks()
        
        # Check for queue imbalances
        total_tasks = sum(len(queue) for queue in self.queues.values())
        if total_tasks == 0:
            return rebalance_stats
        
        # Calculate ideal distribution
        ideal_distribution = {
            "urgent": 0.1,
            "high": 0.2,
            "normal": 0.5,
            "low": 0.2
        }
        
        # Current distribution
        current_distribution = {
            queue_name: len(queue) / total_tasks
            for queue_name, queue in self.queues.items()
            if queue_name != "scheduled"
        }
        
        # Identify imbalances and rebalance
        for queue_name, ideal_ratio in ideal_distribution.items():
            current_ratio = current_distribution.get(queue_name, 0)
            
            if current_ratio > ideal_ratio * 1.5:  # Queue is overloaded
                # Move some tasks to appropriate queues
                excess_tasks = int((current_ratio - ideal_ratio) * total_tasks)
                moved = self._move_excess_tasks(queue_name, excess_tasks)
                rebalance_stats["moved"] += moved
        
        return rebalance_stats
    
    def _move_excess_tasks(self, overloaded_queue: str, excess_count: int) -> int:
        """Move excess tasks from overloaded queue"""
        if not self.queues[overloaded_queue] or excess_count <= 0:
            return 0
        
        moved_count = 0
        tasks_to_move = []
        
        # Get lowest priority tasks from overloaded queue
        queue_list = list(self.queues[overloaded_queue])
        queue_list.sort(key=lambda x: x[0])  # Sort by priority (ascending)
        
        for entry in queue_list[:excess_count]:
            tasks_to_move.append(entry)
        
        # Remove tasks from overloaded queue
        for entry in tasks_to_move:
            self.queues[overloaded_queue].remove(entry)
            moved_count += 1
        
        heapq.heapify(self.queues[overloaded_queue])
        
        # Redistribute tasks
        for entry in tasks_to_move:
            task = entry[2]["task"]
            # Re-add to appropriate queue
            self.add_task_to_queue(task)
        
        return moved_count
    
    def get_queue_statistics(self) -> Dict[str, Any]:
        """Get comprehensive queue statistics"""
        stats = {
            "queue_sizes": {name: len(queue) for name, queue in self.queues.items()},
            "queue_stats": self.queue_stats.copy(),
            "fairness_stats": {
                "last_processed_queue": self.fairness_tracker["last_processed_queue"],
                "queue_process_count": dict(self.fairness_tracker["queue_process_count"]),
                "starvation_timers": dict(self.fairness_tracker["starvation_timers"])
            },
            "total_tasks": sum(len(queue) for queue in self.queues.values()),
            "queue_health": self._assess_queue_health()
        }
        
        return stats
    
    def _assess_queue_health(self) -> Dict[str, str]:
        """Assess health of each queue"""
        health = {}
        
        for queue_name, queue in self.queues.items():
            queue_size = len(queue)
            max_size = self.queue_config["max_size"]
            
            if queue_size == 0:
                health[queue_name] = "empty"
            elif queue_size < max_size * 0.3:
                health[queue_name] = "healthy"
            elif queue_size < max_size * 0.7:
                health[queue_name] = "moderate"
            elif queue_size < max_size * 0.9:
                health[queue_name] = "high"
            else:
                health[queue_name] = "critical"
        
        return health
    
    def clear_queue(self, queue_name: str) -> int:
        """Clear a specific queue"""
        if queue_name not in self.queues:
            return 0
        
        cleared_count = len(self.queues[queue_name])
        
        # Clear metadata for tasks in queue
        for entry in self.queues[queue_name]:
            task_id = entry[1]
            if task_id in self.task_metadata:
                del self.task_metadata[task_id]
        
        # Clear queue
        self.queues[queue_name] = []
        
        logger.info(f"Cleared {cleared_count} tasks from {queue_name} queue")
        return cleared_count
    
    def get_task_queue_position(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get position of task in its queue"""
        if task_id not in self.task_metadata:
            return None
        
        metadata = self.task_metadata[task_id]
        queue_name = metadata["queue_name"]
        queue = self.queues[queue_name]
        
        # Find position in queue
        position = None
        for i, entry in enumerate(queue):
            if entry[1] == task_id:
                position = i + 1
                break
        
        return {
            "task_id": task_id,
            "queue_name": queue_name,
            "position": position,
            "queue_size": len(queue),
            "priority_score": metadata["priority_score"],
            "added_at": metadata["added_at"].isoformat(),
            "promoted": metadata["promoted"]
        }
    
    def remove_task(self, task_id: str) -> bool:
        """Remove a task from its queue"""
        if task_id not in self.task_metadata:
            return False
        
        metadata = self.task_metadata[task_id]
        queue_name = metadata["queue_name"]
        
        # Find and remove task from queue
        queue = self.queues[queue_name]
        for entry in queue:
            if entry[1] == task_id:
                queue.remove(entry)
                heapq.heapify(queue)
                break
        
        # Remove metadata
        del self.task_metadata[task_id]
        
        logger.debug(f"Removed task {task_id} from {queue_name} queue")
        return True
    
    def update_task_priority(self, task_id: str, new_priority: TaskPriority) -> bool:
        """Update priority of task in queue"""
        if task_id not in self.task_metadata:
            return False
        
        metadata = self.task_metadata[task_id]
        task = metadata["task"]
        
        # Update task priority
        task.priority = new_priority
        
        # Remove from current queue
        self.remove_task(task_id)
        
        # Re-add with new priority
        self.add_task_to_queue(task)
        
        logger.info(f"Updated priority of task {task_id} to {new_priority}")
        return True

class TaskMonitoringSystem:
    """
    Comprehensive task monitoring and tracking system
    Monitors task progress, performance, and health
    """
    
    def __init__(self):
        # Monitoring data
        self.task_metrics: Dict[str, Dict[str, Any]] = {}  # task_id -> metrics
        self.agent_metrics: Dict[str, Dict[str, Any]] = {}  # agent_id -> metrics
        self.monitoring_history: List[Dict[str, Any]] = []
        
        # Alert system
        self.alerts: List[Dict[str, Any]] = []
        self.alert_rules: Dict[str, Callable] = {}
        self.alert_thresholds = {
            "task_overdue_hours": 24,
            "task_stalled_hours": 4,
            "agent_overload_percentage": 90,
            "failure_rate_threshold": 0.3,
            "response_time_threshold": 3600  # 1 hour
        }
        
        # Performance tracking
        self.performance_snapshots: List[Dict[str, Any]] = []
        self.trend_data: Dict[str, List[float]] = defaultdict(list)
        
        # Health check configuration
        self.health_check_interval = 300  # 5 minutes
        self.monitoring_enabled = True
        
        # Background monitoring
        self.monitoring_task: Optional[asyncio.Task] = None
        self.running = False
        
        # Register default alert rules
        self._register_default_alert_rules()
        
        logger.info("TaskMonitoringSystem initialized")
    
    def _register_default_alert_rules(self):
        """Register default alert rules"""
        self.alert_rules = {
            "task_overdue": self._check_overdue_tasks,
            "task_stalled": self._check_stalled_tasks,
            "agent_overload": self._check_agent_overload,
            "high_failure_rate": self._check_failure_rate,
            "slow_response_time": self._check_response_time,
            "dependency_blocking": self._check_dependency_blocking,
            "queue_overflow": self._check_queue_overflow
        }
    
    async def start_monitoring(self):
        """Start background monitoring"""
        if self.running:
            return
        
        self.running = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("Task monitoring system started")
    
    async def stop_monitoring(self):
        """Stop background monitoring"""
        if not self.running:
            return
        
        self.running = False
        
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Task monitoring system stopped")
    
    def track_task_created(self, task: Task):
        """Track task creation"""
        self.task_metrics[task.id] = {
            "created_at": task.created_at,
            "status_history": [(task.status.value, datetime.utcnow())],
            "assignment_attempts": 0,
            "reassignment_count": 0,
            "total_wait_time": 0.0,
            "total_execution_time": 0.0,
            "progress_updates": [],
            "alerts_triggered": [],
            "performance_score": 0.0,
            "health_status": "healthy"
        }
        
        logger.debug(f"Started tracking task {task.id}")
    
    def track_task_assigned(self, task: Task, agent_id: str):
        """Track task assignment"""
        if task.id not in self.task_metrics:
            self.track_task_created(task)
        
        metrics = self.task_metrics[task.id]
        metrics["assigned_at"] = datetime.utcnow()
        metrics["assigned_agent"] = agent_id
        metrics["assignment_attempts"] += 1
        metrics["status_history"].append((TaskStatus.ASSIGNED.value, datetime.utcnow()))
        
        # Calculate wait time
        wait_time = (datetime.utcnow() - task.created_at).total_seconds()
        metrics["total_wait_time"] = wait_time
        
        # Track agent metrics
        if agent_id not in self.agent_metrics:
            self.agent_metrics[agent_id] = {
                "tasks_assigned": 0,
                "tasks_completed": 0,
                "tasks_failed": 0,
                "total_execution_time": 0.0,
                "average_completion_time": 0.0,
                "success_rate": 1.0,
                "current_load": 0,
                "performance_trend": [],
                "last_activity": datetime.utcnow()
            }
        
        self.agent_metrics[agent_id]["tasks_assigned"] += 1
        self.agent_metrics[agent_id]["last_activity"] = datetime.utcnow()
        
        logger.debug(f"Tracked assignment of task {task.id} to agent {agent_id}")
    
    def track_task_started(self, task: Task, agent_id: str):
        """Track task start"""
        if task.id not in self.task_metrics:
            self.track_task_created(task)
        
        metrics = self.task_metrics[task.id]
        metrics["started_at"] = datetime.utcnow()
        metrics["status_history"].append((TaskStatus.IN_PROGRESS.value, datetime.utcnow()))
        
        # Update agent metrics
        if agent_id in self.agent_metrics:
            self.agent_metrics[agent_id]["current_load"] += 1
            self.agent_metrics[agent_id]["last_activity"] = datetime.utcnow()
        
        logger.debug(f"Tracked start of task {task.id} by agent {agent_id}")
    
    def track_task_progress(self, task_id: str, progress: float, message: str = ""):
        """Track task progress update"""
        if task_id not in self.task_metrics:
            return
        
        metrics = self.task_metrics[task_id]
        progress_update = {
            "timestamp": datetime.utcnow(),
            "progress": progress,
            "message": message
        }
        
        metrics["progress_updates"].append(progress_update)
        metrics["current_progress"] = progress
        
        # Calculate progress velocity
        if len(metrics["progress_updates"]) > 1:
            last_update = metrics["progress_updates"][-2]
            time_diff = (progress_update["timestamp"] - last_update["timestamp"]).total_seconds()
            progress_diff = progress - last_update["progress"]
            
            if time_diff > 0:
                velocity = progress_diff / (time_diff / 3600)  # Progress per hour
                metrics["progress_velocity"] = velocity
        
        logger.debug(f"Tracked progress update for task {task_id}: {progress:.1%}")
    
    def track_task_completed(self, task: Task, agent_id: str, success: bool):
        """Track task completion"""
        if task.id not in self.task_metrics:
            self.track_task_created(task)
        
        metrics = self.task_metrics[task.id]
        completion_time = datetime.utcnow()
        metrics["completed_at"] = completion_time
        metrics["success"] = success
        
        status = TaskStatus.COMPLETED if success else TaskStatus.FAILED
        metrics["status_history"].append((status.value, completion_time))
        
        # Calculate execution time
        if "started_at" in metrics:
            execution_time = (completion_time - metrics["started_at"]).total_seconds()
            metrics["total_execution_time"] = execution_time
        
        # Calculate total task time
        total_time = (completion_time - task.created_at).total_seconds()
        metrics["total_task_time"] = total_time
        
        # Calculate performance score
        metrics["performance_score"] = self._calculate_task_performance_score(task, metrics)
        
        # Update agent metrics
        if agent_id in self.agent_metrics:
            agent_metrics = self.agent_metrics[agent_id]
            agent_metrics["current_load"] = max(0, agent_metrics["current_load"] - 1)
            agent_metrics["last_activity"] = completion_time
            
            if success:
                agent_metrics["tasks_completed"] += 1
            else:
                agent_metrics["tasks_failed"] += 1
            
            # Update success rate
            total_tasks = agent_metrics["tasks_completed"] + agent_metrics["tasks_failed"]
            if total_tasks > 0:
                agent_metrics["success_rate"] = agent_metrics["tasks_completed"] / total_tasks
            
            # Update average completion time
            if success and "total_execution_time" in metrics:
                if agent_metrics["tasks_completed"] == 1:
                    agent_metrics["average_completion_time"] = metrics["total_execution_time"]
                else:
                    # Running average
                    current_avg = agent_metrics["average_completion_time"]
                    new_avg = (
                        current_avg * (agent_metrics["tasks_completed"] - 1) + 
                        metrics["total_execution_time"]
                    ) / agent_metrics["tasks_completed"]
                    agent_metrics["average_completion_time"] = new_avg
        
        logger.info(f"Tracked completion of task {task.id}: {'success' if success else 'failure'}")
    
    def track_task_reassigned(self, task_id: str, old_agent_id: str, new_agent_id: str, reason: str = ""):
        """Track task reassignment"""
        if task_id not in self.task_metrics:
            return
        
        metrics = self.task_metrics[task_id]
        metrics["reassignment_count"] += 1
        metrics["reassignment_history"] = metrics.get("reassignment_history", [])
        metrics["reassignment_history"].append({
            "timestamp": datetime.utcnow(),
            "from_agent": old_agent_id,
            "to_agent": new_agent_id,
            "reason": reason
        })
        
        # Update agent metrics
        if old_agent_id in self.agent_metrics:
            self.agent_metrics[old_agent_id]["current_load"] = max(0, self.agent_metrics[old_agent_id]["current_load"] - 1)
        
        if new_agent_id in self.agent_metrics:
            self.agent_metrics[new_agent_id]["current_load"] += 1
            self.agent_metrics[new_agent_id]["tasks_assigned"] += 1
        
        logger.info(f"Tracked reassignment of task {task_id} from {old_agent_id} to {new_agent_id}")
    
    def _calculate_task_performance_score(self, task: Task, metrics: Dict[str, Any]) -> float:
        """Calculate performance score for a task"""
        score = 0.0
        
        # Success factor (50%)
        if metrics.get("success", False):
            score += 0.5
        
        # Time efficiency factor (30%)
        if task.estimated_duration and "total_execution_time" in metrics:
            estimated_seconds = task.estimated_duration.total_seconds()
            actual_seconds = metrics["total_execution_time"]
            
            if actual_seconds <= estimated_seconds:
                efficiency = 1.0
            else:
                efficiency = max(0, estimated_seconds / actual_seconds)
            
            score += efficiency * 0.3
        
        # Assignment efficiency factor (10%)
        assignment_attempts = metrics.get("assignment_attempts", 1)
        assignment_efficiency = 1.0 / assignment_attempts
        score += assignment_efficiency * 0.1
        
        # Progress consistency factor (10%)
        progress_updates = metrics.get("progress_updates", [])
        if len(progress_updates) > 1:
            # Check for consistent progress
            progress_values = [update["progress"] for update in progress_updates]
            consistency = 1.0 - (max(progress_values) - min(progress_values)) / len(progress_values)
            score += consistency * 0.1
        
        return min(score, 1.0)
    
    async def _monitoring_loop(self):
        """Background monitoring loop"""
        while self.running:
            try:
                await self._perform_health_checks()
                await self._update_performance_metrics()
                await self._check_alerts()
                await self._cleanup_old_data()
                
                await asyncio.sleep(self.health_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Monitoring loop error: {e}")
                await asyncio.sleep(60)  # Wait before retrying
    
    async def _perform_health_checks(self):
        """Perform health checks on tasks and agents"""
        now = datetime.utcnow()
        
        for task_id, metrics in self.task_metrics.items():
            # Check task health
            health_status = "healthy"
            
            # Check for stalled tasks
            if "started_at" in metrics:
                running_time = (now - metrics["started_at"]).total_seconds() / 3600
                if running_time > self.alert_thresholds["task_stalled_hours"]:
                    health_status = "stalled"
            
            # Check for overdue tasks
            if "created_at" in metrics and not metrics.get("completed_at"):
                age = (now - metrics["created_at"]).total_seconds() / 3600
                if age > self.alert_thresholds["task_overdue_hours"]:
                    health_status = "overdue"
            
            # Check progress velocity
            if "progress_velocity" in metrics and metrics["progress_velocity"] < 0.01:  # Very slow progress
                health_status = "slow_progress"
            
            metrics["health_status"] = health_status
        
        # Check agent health
        for agent_id, metrics in self.agent_metrics.items():
            health_status = "healthy"
            
            # Check failure rate
            total_tasks = metrics["tasks_completed"] + metrics["tasks_failed"]
            if total_tasks > 5:  # Only check if agent has handled enough tasks
                failure_rate = metrics["tasks_failed"] / total_tasks
                if failure_rate > self.alert_thresholds["failure_rate_threshold"]:
                    health_status = "high_failure_rate"
            
            # Check response time
            last_activity = metrics.get("last_activity")
            if last_activity:
                inactive_time = (now - last_activity).total_seconds()
                if inactive_time > self.alert_thresholds["response_time_threshold"]:
                    health_status = "unresponsive"
            
            metrics["health_status"] = health_status
    
    async def _update_performance_metrics(self):
        """Update system performance metrics"""
        snapshot = {
            "timestamp": datetime.utcnow(),
            "total_tasks": len(self.task_metrics),
            "active_tasks": sum(1 for m in self.task_metrics.values() if not m.get("completed_at")),
            "completed_tasks": sum(1 for m in self.task_metrics.values() if m.get("success", False)),
            "failed_tasks": sum(1 for m in self.task_metrics.values() if m.get("success") is False),
            "total_agents": len(self.agent_metrics),
            "active_agents": sum(1 for m in self.agent_metrics.values() if m["current_load"] > 0),
            "average_completion_time": self._calculate_average_completion_time(),
            "system_throughput": self._calculate_system_throughput(),
            "overall_success_rate": self._calculate_overall_success_rate()
        }
        
        self.performance_snapshots.append(snapshot)
        
        # Keep only recent snapshots (last 24 hours)
        cutoff_time = datetime.utcnow() - timedelta(hours=24)
        self.performance_snapshots = [
            s for s in self.performance_snapshots 
            if s["timestamp"] > cutoff_time
        ]
        
        # Update trend data
        for key in ["system_throughput", "overall_success_rate", "average_completion_time"]:
            self.trend_data[key].append(snapshot[key])
            if len(self.trend_data[key]) > 100:  # Keep last 100 data points
                self.trend_data[key].pop(0)
    
    def _calculate_average_completion_time(self) -> float:
        """Calculate average task completion time"""
        completion_times = [
            m["total_execution_time"] for m in self.task_metrics.values() 
            if "total_execution_time" in m
        ]
        
        return sum(completion_times) / len(completion_times) if completion_times else 0.0
    
    def _calculate_system_throughput(self) -> float:
        """Calculate system throughput (tasks per hour)"""
        now = datetime.utcnow()
        hour_ago = now - timedelta(hours=1)
        
        recent_completions = sum(
            1 for m in self.task_metrics.values()
            if m.get("completed_at") and m["completed_at"] > hour_ago
        )
        
        return recent_completions
    
    def _calculate_overall_success_rate(self) -> float:
        """Calculate overall system success rate"""
        completed_tasks = [m for m in self.task_metrics.values() if "success" in m]
        
        if not completed_tasks:
            return 1.0
        
        successful_tasks = sum(1 for m in completed_tasks if m["success"])
        return successful_tasks / len(completed_tasks)
    
    async def _check_alerts(self):
        """Check all alert rules"""
        for rule_name, rule_func in self.alert_rules.items():
            try:
                alerts = await rule_func() if asyncio.iscoroutinefunction(rule_func) else rule_func()
                if alerts:
                    for alert in alerts:
                        alert["rule_name"] = rule_name
                        alert["timestamp"] = datetime.utcnow()
                        self.alerts.append(alert)
            except Exception as e:
                logger.error(f"Alert rule {rule_name} failed: {e}")
        
        # Clean up old alerts (keep last 1000)
        if len(self.alerts) > 1000:
            self.alerts = self.alerts[-1000:]
    
    def _check_overdue_tasks(self) -> List[Dict[str, Any]]:
        """Check for overdue tasks"""
        alerts = []
        now = datetime.utcnow()
        threshold_hours = self.alert_thresholds["task_overdue_hours"]
        
        for task_id, metrics in self.task_metrics.items():
            if metrics.get("completed_at"):
                continue
            
            age = (now - metrics["created_at"]).total_seconds() / 3600
            if age > threshold_hours:
                alerts.append({
                    "severity": "high",
                    "type": "task_overdue",
                    "task_id": task_id,
                    "message": f"Task {task_id} is overdue by {age - threshold_hours:.1f} hours",
                    "age_hours": age
                })
        
        return alerts
    
    def _check_stalled_tasks(self) -> List[Dict[str, Any]]:
        """Check for stalled tasks"""
        alerts = []
        now = datetime.utcnow()
        threshold_hours = self.alert_thresholds["task_stalled_hours"]
        
        for task_id, metrics in self.task_metrics.items():
            if not metrics.get("started_at") or metrics.get("completed_at"):
                continue
            
            running_time = (now - metrics["started_at"]).total_seconds() / 3600
            if running_time > threshold_hours:
                # Check if there has been recent progress
                recent_progress = False
                if metrics.get("progress_updates"):
                    last_update = metrics["progress_updates"][-1]["timestamp"]
                    if (now - last_update).total_seconds() < 3600:  # Progress in last hour
                        recent_progress = True
                
                if not recent_progress:
                    alerts.append({
                        "severity": "medium",
                        "type": "task_stalled",
                        "task_id": task_id,
                        "message": f"Task {task_id} appears stalled (running for {running_time:.1f} hours)",
                        "running_hours": running_time
                    })
        
        return alerts
    
    def _check_agent_overload(self) -> List[Dict[str, Any]]:
        """Check for agent overload"""
        alerts = []
        threshold = self.alert_thresholds["agent_overload_percentage"]
        
        for agent_id, metrics in self.agent_metrics.items():
            # This would need access to agent max capacity
            # For now, we'll use a simple heuristic
            current_load = metrics["current_load"]
            if current_load > 5:  # Assuming max 5 concurrent tasks per agent
                load_percentage = (current_load / 5) * 100
                if load_percentage > threshold:
                    alerts.append({
                        "severity": "high",
                        "type": "agent_overload",
                        "agent_id": agent_id,
                        "message": f"Agent {agent_id} is overloaded ({load_percentage:.1f}%)",
                        "load_percentage": load_percentage
                    })
        
        return alerts
    
    def _check_failure_rate(self) -> List[Dict[str, Any]]:
        """Check for high failure rates"""
        alerts = []
        threshold = self.alert_thresholds["failure_rate_threshold"]
        
        for agent_id, metrics in self.agent_metrics.items():
            if metrics["success_rate"] < (1 - threshold):
                failure_rate = 1 - metrics["success_rate"]
                alerts.append({
                    "severity": "medium",
                    "type": "high_failure_rate",
                    "agent_id": agent_id,
                    "message": f"Agent {agent_id} has high failure rate ({failure_rate:.1%})",
                    "failure_rate": failure_rate
                })
        
        return alerts
    
    def _check_response_time(self) -> List[Dict[str, Any]]:
        """Check for slow response times"""
        alerts = []
        now = datetime.utcnow()
        threshold = self.alert_thresholds["response_time_threshold"]
        
        for agent_id, metrics in self.agent_metrics.items():
            last_activity = metrics.get("last_activity")
            if last_activity:
                inactive_time = (now - last_activity).total_seconds()
                if inactive_time > threshold:
                    alerts.append({
                        "severity": "medium",
                        "type": "slow_response",
                        "agent_id": agent_id,
                        "message": f"Agent {agent_id} has been inactive for {inactive_time/3600:.1f} hours",
                        "inactive_hours": inactive_time / 3600
                    })
        
        return alerts
    
    def _check_dependency_blocking(self) -> List[Dict[str, Any]]:
        """Check for dependency blocking issues"""
        alerts = []
        
        # This would need access to the dependency graph
        # For now, return empty list
        return alerts
    
    def _check_queue_overflow(self) -> List[Dict[str, Any]]:
        """Check for queue overflow"""
        alerts = []
        
        # This would need access to the queue manager
        # For now, return empty list
        return alerts
    
    async def _cleanup_old_data(self):
        """Clean up old monitoring data"""
        cutoff_time = datetime.utcnow() - timedelta(days=7)  # Keep 7 days of data
        
        # Clean up completed task metrics
        old_tasks = [
            task_id for task_id, metrics in self.task_metrics.items()
            if metrics.get("completed_at") and metrics["completed_at"] < cutoff_time
        ]
        
        for task_id in old_tasks:
            del self.task_metrics[task_id]
        
        # Clean up old monitoring history
        self.monitoring_history = [
            entry for entry in self.monitoring_history
            if entry.get("timestamp", datetime.utcnow()) > cutoff_time
        ]
    
    def get_task_metrics(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get metrics for a specific task"""
        return self.task_metrics.get(task_id)
    
    def get_agent_metrics(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Get metrics for a specific agent"""
        return self.agent_metrics.get(agent_id)
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """Get overall system metrics"""
        if not self.performance_snapshots:
            return {}
        
        latest_snapshot = self.performance_snapshots[-1]
        
        return {
            "current_performance": latest_snapshot,
            "trends": {
                key: {
                    "current": values[-1] if values else 0,
                    "average": sum(values) / len(values) if values else 0,
                    "trend": "increasing" if len(values) > 1 and values[-1] > values[0] else "stable"
                }
                for key, values in self.trend_data.items()
            },
            "total_alerts": len(self.alerts),
            "recent_alerts": len([a for a in self.alerts if (datetime.utcnow() - a["timestamp"]).total_seconds() < 3600])
        }
    
    def get_recent_alerts(self, severity: str = None, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent alerts"""
        alerts = self.alerts[-limit:] if not severity else [
            alert for alert in self.alerts[-limit*2:] 
            if alert.get("severity") == severity
        ][-limit:]
        
        return sorted(alerts, key=lambda x: x["timestamp"], reverse=True)
    
    def get_performance_report(self, hours: int = 24) -> Dict[str, Any]:
        """Generate performance report"""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        # Filter recent data
        recent_snapshots = [
            s for s in self.performance_snapshots 
            if s["timestamp"] > cutoff_time
        ]
        
        if not recent_snapshots:
            return {"error": "No data available for the specified time period"}
        
        # Calculate statistics
        throughput_values = [s["system_throughput"] for s in recent_snapshots]
        success_rates = [s["overall_success_rate"] for s in recent_snapshots]
        completion_times = [s["average_completion_time"] for s in recent_snapshots]
        
        return {
            "time_period": f"Last {hours} hours",
            "throughput": {
                "average": sum(throughput_values) / len(throughput_values),
                "peak": max(throughput_values),
                "total": sum(throughput_values)
            },
            "success_rate": {
                "average": sum(success_rates) / len(success_rates),
                "lowest": min(success_rates),
                "current": success_rates[-1]
            },
            "completion_time": {
                "average": sum(completion_times) / len(completion_times),
                "best": min(completion_times),
                "worst": max(completion_times)
            },
            "alerts_summary": self._get_alerts_summary(cutoff_time),
            "top_performing_agents": self._get_top_performing_agents(),
            "bottlenecks": self._identify_bottlenecks()
        }
    
    def _get_alerts_summary(self, since: datetime) -> Dict[str, int]:
        """Get summary of alerts since given time"""
        recent_alerts = [a for a in self.alerts if a["timestamp"] > since]
        
        summary = {}
        for alert in recent_alerts:
            alert_type = alert.get("type", "unknown")
            summary[alert_type] = summary.get(alert_type, 0) + 1
        
        return summary
    
    def _get_top_performing_agents(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get top performing agents"""
        agents = []
        
        for agent_id, metrics in self.agent_metrics.items():
            if metrics["tasks_completed"] > 0:
                agents.append({
                    "agent_id": agent_id,
                    "success_rate": metrics["success_rate"],
                    "tasks_completed": metrics["tasks_completed"],
                    "average_completion_time": metrics["average_completion_time"]
                })
        
        # Sort by success rate and completion time
        agents.sort(key=lambda x: (x["success_rate"], -x["average_completion_time"]), reverse=True)
        
        return agents[:limit]
    
    def _identify_bottlenecks(self) -> List[str]:
        """Identify system bottlenecks"""
        bottlenecks = []
        
        # Check for overloaded agents
        overloaded_agents = sum(1 for m in self.agent_metrics.values() if m["current_load"] > 3)
        if overloaded_agents > len(self.agent_metrics) * 0.5:
            bottlenecks.append("Agent capacity - many agents are overloaded")
        
        # Check for high failure rates
        high_failure_agents = sum(1 for m in self.agent_metrics.values() if m["success_rate"] < 0.7)
        if high_failure_agents > 0:
            bottlenecks.append(f"Agent reliability - {high_failure_agents} agents have high failure rates")
        
        # Check for stalled tasks
        stalled_tasks = sum(1 for m in self.task_metrics.values() if m.get("health_status") == "stalled")
        if stalled_tasks > 0:
            bottlenecks.append(f"Task execution - {stalled_tasks} tasks appear stalled")
        
        return bottlenecks

class AutomaticReassignmentSystem:
    """
    Automatic task reassignment system for handling failures and optimization
    """
    
    def __init__(self, task_engine):
        self.task_engine = task_engine
        
        # Reassignment rules and policies
        self.reassignment_rules = {
            "failure_based": self._failure_based_reassignment,
            "performance_based": self._performance_based_reassignment,
            "load_balancing": self._load_balancing_reassignment,
            "deadline_pressure": self._deadline_pressure_reassignment,
            "agent_unavailable": self._agent_unavailable_reassignment
        }
        
        # Configuration
        self.config = {
            "max_reassignment_attempts": 3,
            "failure_threshold": 2,  # Max failures before reassignment
            "agent_failure_rate_threshold": 0.3,
            "response_timeout": 1800,  # 30 minutes
            "load_imbalance_threshold": 0.7,
            "deadline_urgency_hours": 24,
            "blacklist_duration": 3600,  # 1 hour
            "enable_automatic_reassignment": True,
            "enable_proactive_reassignment": True
        }
        
        # Tracking data
        self.reassignment_history: List[Dict[str, Any]] = []
        self.agent_blacklist: Dict[str, datetime] = {}  # agent_id -> blacklist_until
        self.task_reassignment_count: Dict[str, int] = defaultdict(int)
        self.failed_assignment_attempts: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
        
        # Statistics
        self.stats = {
            "total_reassignments": 0,
            "successful_reassignments": 0,
            "failed_reassignments": 0,
            "reassignment_reasons": defaultdict(int),
            "avg_reassignment_time": 0.0
        }
        
        # Background task for proactive reassignment
        self.proactive_task: Optional[asyncio.Task] = None
        self.running = False
        
        logger.info("AutomaticReassignmentSystem initialized")
    
    async def start(self):
        """Start the automatic reassignment system"""
        if self.running:
            return
        
        self.running = True
        
        if self.config["enable_proactive_reassignment"]:
            self.proactive_task = asyncio.create_task(self._proactive_reassignment_loop())
        
        logger.info("Automatic reassignment system started")
    
    async def stop(self):
        """Stop the automatic reassignment system"""
        if not self.running:
            return
        
        self.running = False
        
        if self.proactive_task:
            self.proactive_task.cancel()
            try:
                await self.proactive_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Automatic reassignment system stopped")
    
    async def handle_task_failure(self, task_id: str, agent_id: str, failure_reason: str) -> bool:
        """Handle task failure and determine if reassignment is needed"""
        if not self.config["enable_automatic_reassignment"]:
            return False
        
        # Record failure
        failure_record = {
            "timestamp": datetime.utcnow(),
            "task_id": task_id,
            "agent_id": agent_id,
            "reason": failure_reason
        }
        
        self.failed_assignment_attempts[task_id].append(failure_record)
        
        # Check if reassignment is warranted
        if await self._should_reassign_on_failure(task_id, agent_id, failure_reason):
            return await self._execute_reassignment(task_id, "task_failure", {
                "original_agent": agent_id,
                "failure_reason": failure_reason
            })
        
        return False
    
    async def _should_reassign_on_failure(self, task_id: str, agent_id: str, failure_reason: str) -> bool:
        """Determine if task should be reassigned after failure"""
        # Check reassignment attempt limit
        if self.task_reassignment_count[task_id] >= self.config["max_reassignment_attempts"]:
            logger.warning(f"Task {task_id} has reached maximum reassignment attempts")
            return False
        
        # Check if task exists and is eligible for reassignment
        if task_id not in self.task_engine.tasks:
            return False
        
        task = self.task_engine.tasks[task_id]
        
        # Don't reassign critical failures or completed tasks
        if task.status in [TaskStatus.COMPLETED, TaskStatus.CANCELLED]:
            return False
        
        # Check agent failure pattern
        agent_failures = len(self.failed_assignment_attempts[task_id])
        if agent_failures >= self.config["failure_threshold"]:
            logger.info(f"Task {task_id} has {agent_failures} failures, triggering reassignment")
            return True
        
        # Check agent reliability
        agent_metrics = self.task_engine.monitoring_system.get_agent_metrics(agent_id)
        if agent_metrics and agent_metrics.get("success_rate", 1.0) < (1 - self.config["agent_failure_rate_threshold"]):
            logger.info(f"Agent {agent_id} has low success rate, triggering reassignment")
            return True
        
        # Check for critical/urgent tasks
        if task.priority in [TaskPriority.CRITICAL, TaskPriority.URGENT]:
            logger.info(f"Task {task_id} is {task.priority.value}, triggering immediate reassignment")
            return True
        
        # Check deadline pressure
        if task.deadline:
            time_to_deadline = task.deadline - datetime.utcnow()
            if time_to_deadline.total_seconds() < self.config["deadline_urgency_hours"] * 3600:
                logger.info(f"Task {task_id} approaching deadline, triggering reassignment")
                return True
        
        return False
    
    async def handle_agent_unavailable(self, agent_id: str, reason: str = "unresponsive") -> List[str]:
        """Handle agent becoming unavailable and reassign their tasks"""
        if not self.config["enable_automatic_reassignment"]:
            return []
        
        # Blacklist agent temporarily
        blacklist_until = datetime.utcnow() + timedelta(seconds=self.config["blacklist_duration"])
        self.agent_blacklist[agent_id] = blacklist_until
        
        # Get assigned tasks
        assigned_tasks = self.task_engine.agent_assignments.get(agent_id, [])
        reassigned_tasks = []
        
        for task_id in assigned_tasks[:]:
            if task_id in self.task_engine.tasks:
                task = self.task_engine.tasks[task_id]
                
                # Only reassign active tasks
                if task.status in [TaskStatus.ASSIGNED, TaskStatus.IN_PROGRESS]:
                    success = await self._execute_reassignment(task_id, "agent_unavailable", {
                        "original_agent": agent_id,
                        "unavailable_reason": reason
                    })
                    
                    if success:
                        reassigned_tasks.append(task_id)
        
        logger.info(f"Reassigned {len(reassigned_tasks)} tasks from unavailable agent {agent_id}")
        return reassigned_tasks
    
    async def handle_performance_degradation(self, agent_id: str) -> List[str]:
        """Handle agent performance degradation"""
        if not self.config["enable_automatic_reassignment"]:
            return []
        
        # Get agent metrics
        agent_metrics = self.task_engine.monitoring_system.get_agent_metrics(agent_id)
        if not agent_metrics:
            return []
        
        # Check if performance is below threshold
        success_rate = agent_metrics.get("success_rate", 1.0)
        if success_rate >= (1 - self.config["agent_failure_rate_threshold"]):
            return []  # Performance is acceptable
        
        # Get current assignments
        assigned_tasks = self.task_engine.agent_assignments.get(agent_id, [])
        reassigned_tasks = []
        
        # Reassign high-priority tasks first
        for task_id in assigned_tasks[:]:
            if task_id in self.task_engine.tasks:
                task = self.task_engine.tasks[task_id]
                
                # Only reassign high-priority unstarted tasks
                if (task.status == TaskStatus.ASSIGNED and 
                    task.priority in [TaskPriority.HIGH, TaskPriority.URGENT, TaskPriority.CRITICAL]):
                    
                    success = await self._execute_reassignment(task_id, "performance_degradation", {
                        "original_agent": agent_id,
                        "success_rate": success_rate
                    })
                    
                    if success:
                        reassigned_tasks.append(task_id)
        
        logger.info(f"Reassigned {len(reassigned_tasks)} high-priority tasks from underperforming agent {agent_id}")
        return reassigned_tasks
    
    async def _execute_reassignment(self, task_id: str, reason: str, metadata: Dict[str, Any] = None) -> bool:
        """Execute task reassignment"""
        start_time = datetime.utcnow()
        
        if task_id not in self.task_engine.tasks:
            return False
        
        task = self.task_engine.tasks[task_id]
        old_agent_id = task.assigned_agent
        
        # Find new agent
        new_agent_id = await self._find_reassignment_candidate(task, old_agent_id)
        
        if not new_agent_id:
            logger.warning(f"No suitable agent found for reassigning task {task_id}")
            self.stats["failed_reassignments"] += 1
            return False
        
        try:
            # Perform reassignment
            await self.task_engine._reassign_task(task, new_agent_id)
            
            # Update tracking
            self.task_reassignment_count[task_id] += 1
            
            # Record reassignment
            reassignment_record = {
                "timestamp": datetime.utcnow(),
                "task_id": task_id,
                "old_agent_id": old_agent_id,
                "new_agent_id": new_agent_id,
                "reason": reason,
                "metadata": metadata or {},
                "reassignment_attempt": self.task_reassignment_count[task_id],
                "duration": (datetime.utcnow() - start_time).total_seconds()
            }
            
            self.reassignment_history.append(reassignment_record)
            
            # Update statistics
            self.stats["total_reassignments"] += 1
            self.stats["successful_reassignments"] += 1
            self.stats["reassignment_reasons"][reason] += 1
            
            # Update average reassignment time
            total_time = self.stats["avg_reassignment_time"] * (self.stats["total_reassignments"] - 1)
            total_time += reassignment_record["duration"]
            self.stats["avg_reassignment_time"] = total_time / self.stats["total_reassignments"]
            
            logger.info(f"Successfully reassigned task {task_id} from {old_agent_id} to {new_agent_id} (reason: {reason})")
            return True
            
        except Exception as e:
            logger.error(f"Failed to reassign task {task_id}: {e}")
            self.stats["failed_reassignments"] += 1
            return False
    
    async def _find_reassignment_candidate(self, task: Task, exclude_agent_id: str) -> Optional[str]:
        """Find the best agent for reassignment"""
        # Get available agents (exclude blacklisted and original agent)
        available_agents = []
        
        for agent_id, agent in self.task_engine.agents.items():
            # Skip excluded agent
            if agent_id == exclude_agent_id:
                continue
            
            # Skip blacklisted agents
            if self._is_agent_blacklisted(agent_id):
                continue
            
            # Check if agent can handle the task
            if agent.can_handle_task(task):
                available_agents.append(agent)
        
        if not available_agents:
            return None
        
        # Use hybrid assignment strategy for best fit
        best_agent = None
        best_score = -1
        
        for agent in available_agents:
            # Calculate assignment score with reassignment penalty
            fitness_result = self.task_engine.calculate_agent_task_fitness(agent.agent_id, task.id)
            
            if fitness_result["can_handle"]:
                score = fitness_result["fitness_score"]
                
                # Apply penalty for previous failures with this task
                task_failures = len([
                    failure for failure in self.failed_assignment_attempts[task.id]
                    if failure["agent_id"] == agent.agent_id
                ])
                
                if task_failures > 0:
                    score *= (0.8 ** task_failures)  # Exponential penalty
                
                # Bonus for agents with low current load
                load_factor = 1.0 - (agent.current_load / agent.max_concurrent_tasks)
                score *= (1.0 + load_factor * 0.2)
                
                if score > best_score:
                    best_score = score
                    best_agent = agent
        
        return best_agent.agent_id if best_agent else None
    
    def _is_agent_blacklisted(self, agent_id: str) -> bool:
        """Check if agent is currently blacklisted"""
        if agent_id not in self.agent_blacklist:
            return False
        
        blacklist_until = self.agent_blacklist[agent_id]
        if datetime.utcnow() > blacklist_until:
            # Remove expired blacklist
            del self.agent_blacklist[agent_id]
            return False
        
        return True
    
    async def _proactive_reassignment_loop(self):
        """Background loop for proactive reassignment"""
        while self.running:
            try:
                await self._perform_proactive_reassignment()
                await asyncio.sleep(300)  # Check every 5 minutes
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Proactive reassignment loop error: {e}")
                await asyncio.sleep(60)
    
    async def _perform_proactive_reassignment(self):
        """Perform proactive reassignment checks"""
        # Check for overloaded agents
        await self._check_load_balancing_reassignment()
        
        # Check for deadline pressure
        await self._check_deadline_pressure_reassignment()
        
        # Check for stuck tasks
        await self._check_stuck_tasks_reassignment()
        
        # Check for unresponsive agents
        await self._check_unresponsive_agents()
    
    async def _check_load_balancing_reassignment(self):
        """Check if load balancing reassignment is needed"""
        if not self.task_engine.agents:
            return
        
        # Calculate load statistics
        agent_loads = [(agent.agent_id, agent.get_load_percentage()) for agent in self.task_engine.agents.values()]
        agent_loads.sort(key=lambda x: x[1])
        
        if len(agent_loads) < 2:
            return
        
        # Check for significant load imbalance
        max_load = agent_loads[-1][1]
        min_load = agent_loads[0][1]
        
        if max_load > 80 and min_load < 30 and (max_load - min_load) > 50:
            # Significant imbalance detected
            overloaded_agent_id = agent_loads[-1][0]
            underloaded_agent_id = agent_loads[0][0]
            
            # Find suitable tasks to reassign
            assigned_tasks = self.task_engine.agent_assignments.get(overloaded_agent_id, [])
            
            for task_id in assigned_tasks:
                if task_id in self.task_engine.tasks:
                    task = self.task_engine.tasks[task_id]
                    
                    # Only reassign pending tasks
                    if task.status == TaskStatus.ASSIGNED:
                        underloaded_agent = self.task_engine.agents[underloaded_agent_id]
                        
                        if underloaded_agent.can_handle_task(task):
                            await self._execute_reassignment(task_id, "load_balancing", {
                                "overloaded_agent": overloaded_agent_id,
                                "underloaded_agent": underloaded_agent_id,
                                "load_difference": max_load - min_load
                            })
                            break  # Only reassign one task at a time
    
    async def _check_deadline_pressure_reassignment(self):
        """Check for tasks under deadline pressure"""
        now = datetime.utcnow()
        
        for task_id, task in self.task_engine.tasks.items():
            if task.deadline and task.status in [TaskStatus.ASSIGNED, TaskStatus.IN_PROGRESS]:
                time_to_deadline = (task.deadline - now).total_seconds() / 3600  # hours
                
                # Check if task is under deadline pressure
                if time_to_deadline < self.config["deadline_urgency_hours"] and task.assigned_agent:
                    agent_metrics = self.task_engine.monitoring_system.get_agent_metrics(task.assigned_agent)
                    
                    # Check if current agent is performing poorly
                    if agent_metrics and agent_metrics.get("success_rate", 1.0) < 0.8:
                        await self._execute_reassignment(task_id, "deadline_pressure", {
                            "hours_to_deadline": time_to_deadline,
                            "agent_success_rate": agent_metrics.get("success_rate", 1.0)
                        })
    
    async def _check_stuck_tasks_reassignment(self):
        """Check for stuck tasks that need reassignment"""
        now = datetime.utcnow()
        
        for task_id, task in self.task_engine.tasks.items():
            if task.status == TaskStatus.IN_PROGRESS and task.started_at:
                # Check if task has been running too long without progress
                running_time = (now - task.started_at).total_seconds() / 3600  # hours
                
                # Get expected duration
                expected_duration = 4  # Default 4 hours
                if task.estimated_duration:
                    expected_duration = task.estimated_duration.total_seconds() / 3600
                
                # Check if task is taking too long
                if running_time > expected_duration * 2:  # 2x expected duration
                    # Check for recent progress
                    task_metrics = self.task_engine.monitoring_system.get_task_metrics(task_id)
                    
                    recent_progress = False
                    if task_metrics and task_metrics.get("progress_updates"):
                        last_update = task_metrics["progress_updates"][-1]["timestamp"]
                        if (now - last_update).total_seconds() < 3600:  # Progress in last hour
                            recent_progress = True
                    
                    if not recent_progress:
                        await self._execute_reassignment(task_id, "stuck_task", {
                            "running_hours": running_time,
                            "expected_hours": expected_duration,
                            "no_recent_progress": True
                        })
    
    async def _check_unresponsive_agents(self):
        """Check for unresponsive agents"""
        now = datetime.utcnow()
        response_timeout = self.config["response_timeout"]
        
        for agent_id, agent_metrics in self.task_engine.monitoring_system.agent_metrics.items():
            last_activity = agent_metrics.get("last_activity")
            
            if last_activity:
                inactive_time = (now - last_activity).total_seconds()
                
                if inactive_time > response_timeout:
                    # Agent appears unresponsive
                    await self.handle_agent_unavailable(agent_id, "unresponsive")
    
    # Rule-based reassignment methods
    
    async def _failure_based_reassignment(self, task_id: str, context: Dict[str, Any]) -> bool:
        """Rule for failure-based reassignment"""
        return await self.handle_task_failure(
            task_id, 
            context.get("agent_id"), 
            context.get("failure_reason", "unknown")
        )
    
    async def _performance_based_reassignment(self, agent_id: str, context: Dict[str, Any]) -> List[str]:
        """Rule for performance-based reassignment"""
        return await self.handle_performance_degradation(agent_id)
    
    async def _load_balancing_reassignment(self, context: Dict[str, Any]) -> bool:
        """Rule for load balancing reassignment"""
        await self._check_load_balancing_reassignment()
        return True
    
    async def _deadline_pressure_reassignment(self, context: Dict[str, Any]) -> bool:
        """Rule for deadline pressure reassignment"""
        await self._check_deadline_pressure_reassignment()
        return True
    
    async def _agent_unavailable_reassignment(self, agent_id: str, context: Dict[str, Any]) -> List[str]:
        """Rule for agent unavailable reassignment"""
        return await self.handle_agent_unavailable(
            agent_id, 
            context.get("reason", "unavailable")
        )
    
    # Public API methods
    
    def get_reassignment_statistics(self) -> Dict[str, Any]:
        """Get reassignment statistics"""
        return {
            "stats": self.stats.copy(),
            "config": self.config.copy(),
            "active_blacklisted_agents": [
                {"agent_id": agent_id, "blacklisted_until": blacklist_until.isoformat()}
                for agent_id, blacklist_until in self.agent_blacklist.items()
                if datetime.utcnow() < blacklist_until
            ],
            "task_reassignment_counts": dict(self.task_reassignment_count),
            "recent_reassignments": self.reassignment_history[-10:]  # Last 10 reassignments
        }
    
    def get_task_reassignment_history(self, task_id: str) -> List[Dict[str, Any]]:
        """Get reassignment history for a specific task"""
        return [
            record for record in self.reassignment_history
            if record["task_id"] == task_id
        ]
    
    def update_config(self, config_updates: Dict[str, Any]) -> bool:
        """Update reassignment configuration"""
        try:
            for key, value in config_updates.items():
                if key in self.config:
                    self.config[key] = value
                    logger.info(f"Updated reassignment config {key} to {value}")
                else:
                    logger.warning(f"Unknown config key: {key}")
            return True
        except Exception as e:
            logger.error(f"Failed to update reassignment config: {e}")
            return False
    
    def blacklist_agent(self, agent_id: str, duration_seconds: int = None) -> bool:
        """Manually blacklist an agent"""
        duration = duration_seconds or self.config["blacklist_duration"]
        blacklist_until = datetime.utcnow() + timedelta(seconds=duration)
        self.agent_blacklist[agent_id] = blacklist_until
        
        logger.info(f"Blacklisted agent {agent_id} until {blacklist_until}")
        return True
    
    def remove_agent_blacklist(self, agent_id: str) -> bool:
        """Remove agent from blacklist"""
        if agent_id in self.agent_blacklist:
            del self.agent_blacklist[agent_id]
            logger.info(f"Removed agent {agent_id} from blacklist")
            return True
        return False
    
    async def manual_reassignment(self, task_id: str, target_agent_id: str, reason: str = "manual") -> bool:
        """Manually trigger task reassignment"""
        return await self._execute_reassignment(task_id, reason, {
            "manual_reassignment": True,
            "target_agent": target_agent_id
        })

class TaskCompletionVerificationSystem:
    """
    Task completion verification system for ensuring quality and correctness
    """
    
    def __init__(self, task_engine):
        self.task_engine = task_engine
        
        # Verification rules and checks
        self.verification_rules = {
            "basic_validation": self._basic_validation_check,
            "quality_assessment": self._quality_assessment_check,
            "dependency_verification": self._dependency_verification_check,
            "output_validation": self._output_validation_check,
            "peer_review": self._peer_review_check,
            "automated_testing": self._automated_testing_check,
            "business_rules": self._business_rules_check
        }
        
        # Configuration
        self.config = {
            "enable_verification": True,
            "require_peer_review": False,
            "auto_approve_threshold": 0.8,
            "quality_score_threshold": 0.7,
            "max_verification_time": 3600,  # 1 hour
            "require_testing": True,
            "min_reviewers": 1,
            "verification_timeout": 1800,  # 30 minutes
            "enable_auto_approval": True,
            "strict_mode": False
        }
        
        # Verification tracking
        self.verification_queue: List[Dict[str, Any]] = []
        self.verification_history: List[Dict[str, Any]] = []
        self.pending_verifications: Dict[str, Dict[str, Any]] = {}  # task_id -> verification_data
        self.reviewer_assignments: Dict[str, List[str]] = defaultdict(list)  # reviewer_id -> task_ids
        
        # Quality metrics
        self.quality_metrics = {
            "total_verifications": 0,
            "approved_tasks": 0,
            "rejected_tasks": 0,
            "avg_verification_time": 0.0,
            "quality_score_distribution": defaultdict(int),
            "common_rejection_reasons": defaultdict(int)
        }
        
        # Reviewers and their capabilities
        self.reviewers: Dict[str, Dict[str, Any]] = {}  # reviewer_id -> reviewer_info
        
        # Background verification task
        self.verification_task: Optional[asyncio.Task] = None
        self.running = False
        
        logger.info("TaskCompletionVerificationSystem initialized")
    
    async def start(self):
        """Start the verification system"""
        if self.running:
            return
        
        self.running = True
        
        if self.config["enable_verification"]:
            self.verification_task = asyncio.create_task(self._verification_loop())
        
        logger.info("Task completion verification system started")
    
    async def stop(self):
        """Stop the verification system"""
        if not self.running:
            return
        
        self.running = False
        
        if self.verification_task:
            self.verification_task.cancel()
            try:
                await self.verification_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Task completion verification system stopped")
    
    def register_reviewer(self, reviewer_id: str, reviewer_info: Dict[str, Any]):
        """Register a reviewer with their capabilities"""
        self.reviewers[reviewer_id] = {
            "reviewer_id": reviewer_id,
            "name": reviewer_info.get("name", reviewer_id),
            "specializations": reviewer_info.get("specializations", []),
            "experience_level": reviewer_info.get("experience_level", "intermediate"),
            "max_concurrent_reviews": reviewer_info.get("max_concurrent_reviews", 3),
            "current_reviews": 0,
            "review_history": [],
            "success_rate": reviewer_info.get("success_rate", 1.0),
            "average_review_time": reviewer_info.get("average_review_time", 1800),  # 30 minutes
            "last_active": datetime.utcnow()
        }
        
        logger.info(f"Registered reviewer {reviewer_id} with specializations: {reviewer_info.get('specializations', [])}")
    
    async def submit_for_verification(self, task_id: str, completion_data: Dict[str, Any]) -> str:
        """Submit a completed task for verification"""
        if not self.config["enable_verification"]:
            return await self._auto_approve_task(task_id, "verification_disabled")
        
        if task_id not in self.task_engine.tasks:
            raise ValueError(f"Task {task_id} not found")
        
        task = self.task_engine.tasks[task_id]
        
        # Create verification record
        verification_id = str(uuid.uuid4())
        verification_data = {
            "verification_id": verification_id,
            "task_id": task_id,
            "submitted_at": datetime.utcnow(),
            "completion_data": completion_data,
            "status": "pending",
            "verification_checks": {},
            "quality_score": 0.0,
            "reviewer_assignments": [],
            "verification_steps": [],
            "estimated_completion": datetime.utcnow() + timedelta(seconds=self.config["verification_timeout"]),
            "rejection_reasons": [],
            "approval_reasons": []
        }
        
        self.pending_verifications[task_id] = verification_data
        self.verification_queue.append(verification_data)
        
        # Update task status
        task.status = TaskStatus.COMPLETED  # Mark as completed but pending verification
        task.progress = 1.0
        
        logger.info(f"Task {task_id} submitted for verification with ID {verification_id}")
        
        # Start verification process
        await self._process_verification(verification_data)
        
        return verification_id
    
    async def _process_verification(self, verification_data: Dict[str, Any]):
        """Process a verification request"""
        task_id = verification_data["task_id"]
        verification_id = verification_data["verification_id"]
        
        try:
            # Step 1: Basic validation
            basic_result = await self._run_verification_check("basic_validation", verification_data)
            verification_data["verification_checks"]["basic_validation"] = basic_result
            
            if not basic_result["passed"]:
                await self._reject_verification(verification_data, "Failed basic validation")
                return
            
            # Step 2: Quality assessment
            quality_result = await self._run_verification_check("quality_assessment", verification_data)
            verification_data["verification_checks"]["quality_assessment"] = quality_result
            verification_data["quality_score"] = quality_result.get("score", 0.0)
            
            # Step 3: Dependency verification
            dependency_result = await self._run_verification_check("dependency_verification", verification_data)
            verification_data["verification_checks"]["dependency_verification"] = dependency_result
            
            # Step 4: Output validation
            output_result = await self._run_verification_check("output_validation", verification_data)
            verification_data["verification_checks"]["output_validation"] = output_result
            
            # Step 5: Automated testing (if required)
            if self.config["require_testing"]:
                testing_result = await self._run_verification_check("automated_testing", verification_data)
                verification_data["verification_checks"]["automated_testing"] = testing_result
                
                if not testing_result["passed"]:
                    await self._reject_verification(verification_data, "Failed automated testing")
                    return
            
            # Step 6: Business rules validation
            business_result = await self._run_verification_check("business_rules", verification_data)
            verification_data["verification_checks"]["business_rules"] = business_result
            
            # Step 7: Peer review (if required or quality score is below threshold)
            needs_peer_review = (
                self.config["require_peer_review"] or
                verification_data["quality_score"] < self.config["quality_score_threshold"] or
                verification_data["completion_data"].get("complexity", "normal") == "high"
            )
            
            if needs_peer_review:
                await self._assign_peer_reviewers(verification_data)
                # Peer review will be handled asynchronously
                return
            
            # Step 8: Auto-approval check
            if self._should_auto_approve(verification_data):
                await self._approve_verification(verification_data, "Auto-approved based on quality metrics")
            else:
                await self._assign_peer_reviewers(verification_data)
            
        except Exception as e:
            logger.error(f"Verification process failed for task {task_id}: {e}")
            await self._reject_verification(verification_data, f"Verification process error: {str(e)}")
    
    async def _run_verification_check(self, check_name: str, verification_data: Dict[str, Any]) -> Dict[str, Any]:
        """Run a specific verification check"""
        if check_name not in self.verification_rules:
            return {"passed": True, "score": 1.0, "message": "Check not implemented"}
        
        start_time = datetime.utcnow()
        
        try:
            result = await self.verification_rules[check_name](verification_data)
            
            # Ensure result has required fields
            if "passed" not in result:
                result["passed"] = result.get("score", 0.0) >= 0.5
            
            result["check_name"] = check_name
            result["duration"] = (datetime.utcnow() - start_time).total_seconds()
            result["timestamp"] = datetime.utcnow().isoformat()
            
            verification_data["verification_steps"].append({
                "step": check_name,
                "result": result,
                "timestamp": datetime.utcnow().isoformat()
            })
            
            return result
            
        except Exception as e:
            logger.error(f"Verification check {check_name} failed: {e}")
            return {
                "passed": False,
                "score": 0.0,
                "message": f"Check failed with error: {str(e)}",
                "error": str(e)
            }
    
    async def _basic_validation_check(self, verification_data: Dict[str, Any]) -> Dict[str, Any]:
        """Basic validation check"""
        task_id = verification_data["task_id"]
        completion_data = verification_data["completion_data"]
        
        # Check required fields
        required_fields = ["result", "status", "completed_by"]
        missing_fields = [field for field in required_fields if field not in completion_data]
        
        if missing_fields:
            return {
                "passed": False,
                "score": 0.0,
                "message": f"Missing required fields: {missing_fields}"
            }
        
        # Check if task exists and is in correct state
        if task_id not in self.task_engine.tasks:
            return {
                "passed": False,
                "score": 0.0,
                "message": "Task not found"
            }
        
        task = self.task_engine.tasks[task_id]
        
        # Validate completion status
        if completion_data["status"] not in ["completed", "success"]:
            return {
                "passed": False,
                "score": 0.0,
                "message": f"Invalid completion status: {completion_data['status']}"
            }
        
        # Check if result is provided
        if not completion_data.get("result"):
            return {
                "passed": False,
                "score": 0.5,
                "message": "No result data provided"
            }
        
        return {
            "passed": True,
            "score": 1.0,
            "message": "Basic validation passed"
        }
    
    async def _quality_assessment_check(self, verification_data: Dict[str, Any]) -> Dict[str, Any]:
        """Quality assessment check"""
        completion_data = verification_data["completion_data"]
        task_id = verification_data["task_id"]
        task = self.task_engine.tasks[task_id]
        
        quality_score = 0.0
        quality_factors = []
        
        # Factor 1: Completeness (30%)
        result_data = completion_data.get("result", {})
        if isinstance(result_data, dict) and result_data:
            completeness_score = min(len(result_data) / 5, 1.0)  # Assume 5 fields indicate completeness
        else:
            completeness_score = 0.5 if result_data else 0.0
        
        quality_score += completeness_score * 0.3
        quality_factors.append(f"Completeness: {completeness_score:.2f}")
        
        # Factor 2: Documentation (20%)
        documentation_score = 0.0
        if completion_data.get("documentation"):
            doc_length = len(str(completion_data["documentation"]))
            documentation_score = min(doc_length / 200, 1.0)  # Assume 200 chars is good documentation
        
        quality_score += documentation_score * 0.2
        quality_factors.append(f"Documentation: {documentation_score:.2f}")
        
        # Factor 3: Time efficiency (20%)
        time_score = 1.0
        if task.estimated_duration and task.actual_duration:
            time_ratio = task.estimated_duration.total_seconds() / task.actual_duration.total_seconds()
            time_score = min(time_ratio, 1.0)  # Better if completed faster than estimated
        
        quality_score += time_score * 0.2
        quality_factors.append(f"Time efficiency: {time_score:.2f}")
        
        # Factor 4: Error handling (15%)
        error_handling_score = 0.8  # Default assumption
        if completion_data.get("errors_encountered"):
            errors = completion_data["errors_encountered"]
            if isinstance(errors, list) and errors:
                error_handling_score = 0.6  # Some errors encountered
            elif errors:
                error_handling_score = 0.4  # Significant errors
        
        quality_score += error_handling_score * 0.15
        quality_factors.append(f"Error handling: {error_handling_score:.2f}")
        
        # Factor 5: Code/output quality (15%)
        output_quality_score = 0.7  # Default assumption
        if completion_data.get("quality_metrics"):
            metrics = completion_data["quality_metrics"]
            if isinstance(metrics, dict):
                output_quality_score = metrics.get("overall_score", 0.7)
        
        quality_score += output_quality_score * 0.15
        quality_factors.append(f"Output quality: {output_quality_score:.2f}")
        
        return {
            "passed": quality_score >= self.config["quality_score_threshold"],
            "score": quality_score,
            "message": f"Quality assessment score: {quality_score:.2f}",
            "quality_factors": quality_factors,
            "threshold": self.config["quality_score_threshold"]
        }
    
    async def _dependency_verification_check(self, verification_data: Dict[str, Any]) -> Dict[str, Any]:
        """Verify task dependencies and impact"""
        task_id = verification_data["task_id"]
        task = self.task_engine.tasks[task_id]
        
        # Check if completing this task enables dependent tasks
        dependent_tasks = self.task_engine.dependents_graph.get(task_id, set())
        
        dependency_issues = []
        
        for dependent_id in dependent_tasks:
            if dependent_id in self.task_engine.tasks:
                dependent_task = self.task_engine.tasks[dependent_id]
                
                # Check if dependent task can now be assigned
                if dependent_task.status == TaskStatus.PENDING:
                    can_be_assigned = self.task_engine._can_be_assigned(dependent_task)
                    if not can_be_assigned:
                        dependency_issues.append(f"Dependent task {dependent_id} still cannot be assigned")
        
        # Check for dependency consistency
        completion_data = verification_data["completion_data"]
        if "dependencies_satisfied" in completion_data:
            declared_dependencies = completion_data["dependencies_satisfied"]
            actual_dependencies = [dep.depends_on for dep in task.dependencies]
            
            missing_deps = set(actual_dependencies) - set(declared_dependencies)
            if missing_deps:
                dependency_issues.append(f"Missing dependency declarations: {missing_deps}")
        
        passed = len(dependency_issues) == 0
        score = 1.0 if passed else max(0.0, 1.0 - len(dependency_issues) * 0.2)
        
        return {
            "passed": passed,
            "score": score,
            "message": "Dependency verification passed" if passed else f"Issues found: {dependency_issues}",
            "dependency_issues": dependency_issues,
            "dependent_tasks_count": len(dependent_tasks)
        }
    
    async def _output_validation_check(self, verification_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate task output format and content"""
        completion_data = verification_data["completion_data"]
        task_id = verification_data["task_id"]
        task = self.task_engine.tasks[task_id]
        
        validation_issues = []
        
        # Check output format
        result = completion_data.get("result")
        if not result:
            validation_issues.append("No result provided")
        
        # Validate based on task type
        if task.task_type == TaskType.DEVELOPMENT:
            # Check for code-related outputs
            if not completion_data.get("code_changes") and not completion_data.get("deliverables"):
                validation_issues.append("No code changes or deliverables provided for development task")
        
        elif task.task_type == TaskType.TESTING:
            # Check for test results
            if not completion_data.get("test_results"):
                validation_issues.append("No test results provided for testing task")
        
        elif task.task_type == TaskType.REVIEW:
            # Check for review feedback
            if not completion_data.get("review_feedback") and not completion_data.get("review_comments"):
                validation_issues.append("No review feedback provided for review task")
        
        # Check required output fields based on task context
        required_outputs = task.context.get("required_outputs", [])
        for required_output in required_outputs:
            if required_output not in result:
                validation_issues.append(f"Missing required output: {required_output}")
        
        # Validate output schema if provided
        output_schema = task.context.get("output_schema")
        if output_schema and isinstance(result, dict):
            for required_field in output_schema.get("required", []):
                if required_field not in result:
                    validation_issues.append(f"Missing required field in result: {required_field}")
        
        passed = len(validation_issues) == 0
        score = 1.0 if passed else max(0.0, 1.0 - len(validation_issues) * 0.15)
        
        return {
            "passed": passed,
            "score": score,
            "message": "Output validation passed" if passed else f"Validation issues: {validation_issues}",
            "validation_issues": validation_issues
        }
    
    async def _automated_testing_check(self, verification_data: Dict[str, Any]) -> Dict[str, Any]:
        """Run automated tests on the task output"""
        completion_data = verification_data["completion_data"]
        
        # Simulate automated testing
        # In a real implementation, this would run actual tests
        test_results = {
            "tests_run": 0,
            "tests_passed": 0,
            "tests_failed": 0,
            "coverage": 0.0,
            "test_details": []
        }
        
        # Check if test results are provided
        if "test_results" in completion_data:
            provided_results = completion_data["test_results"]
            if isinstance(provided_results, dict):
                test_results.update(provided_results)
        
        # Simulate some basic tests
        test_results["tests_run"] = max(test_results["tests_run"], 5)
        test_results["tests_passed"] = max(test_results["tests_passed"], 4)
        test_results["tests_failed"] = test_results["tests_run"] - test_results["tests_passed"]
        test_results["coverage"] = max(test_results["coverage"], 0.8)
        
        # Calculate pass rate
        pass_rate = test_results["tests_passed"] / test_results["tests_run"] if test_results["tests_run"] > 0 else 0
        
        passed = pass_rate >= 0.8 and test_results["coverage"] >= 0.7
        score = (pass_rate * 0.7) + (test_results["coverage"] * 0.3)
        
        return {
            "passed": passed,
            "score": score,
            "message": f"Tests: {test_results['tests_passed']}/{test_results['tests_run']} passed, Coverage: {test_results['coverage']:.1%}",
            "test_results": test_results,
            "pass_rate": pass_rate
        }
    
    async def _business_rules_check(self, verification_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate against business rules"""
        task_id = verification_data["task_id"]
        completion_data = verification_data["completion_data"]
        task = self.task_engine.tasks[task_id]
        
        rule_violations = []
        
        # Check deadline compliance
        if task.deadline:
            completed_at = completion_data.get("completed_at")
            if completed_at:
                if isinstance(completed_at, str):
                    completed_at = datetime.fromisoformat(completed_at)
                
                if completed_at > task.deadline:
                    rule_violations.append(f"Task completed after deadline ({task.deadline})")
        
        # Check resource constraints
        if task.context.get("budget_limit"):
            actual_cost = completion_data.get("actual_cost", 0)
            budget_limit = task.context["budget_limit"]
            
            if actual_cost > budget_limit:
                rule_violations.append(f"Budget exceeded: {actual_cost} > {budget_limit}")
        
        # Check compliance requirements
        compliance_requirements = task.context.get("compliance_requirements", [])
        for requirement in compliance_requirements:
            if requirement not in completion_data.get("compliance_certifications", []):
                rule_violations.append(f"Missing compliance certification: {requirement}")
        
        # Check approval requirements
        if task.priority in [TaskPriority.CRITICAL, TaskPriority.URGENT]:
            if not completion_data.get("supervisor_approval"):
                rule_violations.append("Critical/Urgent tasks require supervisor approval")
        
        passed = len(rule_violations) == 0
        score = 1.0 if passed else max(0.0, 1.0 - len(rule_violations) * 0.25)
        
        return {
            "passed": passed,
            "score": score,
            "message": "Business rules compliance passed" if passed else f"Rule violations: {rule_violations}",
            "rule_violations": rule_violations
        }
    
    async def _peer_review_check(self, verification_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle peer review process"""
        # This is handled separately in _assign_peer_reviewers
        return {
            "passed": True,
            "score": 1.0,
            "message": "Peer review will be handled separately"
        }
    
    async def _assign_peer_reviewers(self, verification_data: Dict[str, Any]):
        """Assign peer reviewers for the task"""
        task_id = verification_data["task_id"]
        task = self.task_engine.tasks[task_id]
        
        # Find suitable reviewers
        suitable_reviewers = self._find_suitable_reviewers(task)
        
        if not suitable_reviewers:
            logger.warning(f"No suitable reviewers found for task {task_id}")
            # Auto-approve if no reviewers available
            await self._approve_verification(verification_data, "No reviewers available - auto-approved")
            return
        
        # Assign reviewers
        num_reviewers = min(self.config["min_reviewers"], len(suitable_reviewers))
        assigned_reviewers = suitable_reviewers[:num_reviewers]
        
        for reviewer_id in assigned_reviewers:
            verification_data["reviewer_assignments"].append({
                "reviewer_id": reviewer_id,
                "assigned_at": datetime.utcnow(),
                "status": "pending",
                "review_deadline": datetime.utcnow() + timedelta(seconds=self.config["verification_timeout"])
            })
            
            self.reviewer_assignments[reviewer_id].append(task_id)
            self.reviewers[reviewer_id]["current_reviews"] += 1
        
        verification_data["status"] = "peer_review"
        
        logger.info(f"Assigned {len(assigned_reviewers)} reviewers for task {task_id}: {assigned_reviewers}")
    
    def _find_suitable_reviewers(self, task: Task) -> List[str]:
        """Find suitable reviewers for a task"""
        suitable_reviewers = []
        
        for reviewer_id, reviewer_info in self.reviewers.items():
            # Check availability
            if reviewer_info["current_reviews"] >= reviewer_info["max_concurrent_reviews"]:
                continue
            
            # Check specialization match
            reviewer_specializations = set(reviewer_info["specializations"])
            task_requirements = set([task.task_type.value] + task.required_capabilities)
            
            if reviewer_specializations & task_requirements:
                suitable_reviewers.append((reviewer_id, len(reviewer_specializations & task_requirements)))
        
        # Sort by specialization match and experience
        suitable_reviewers.sort(key=lambda x: (
            x[1],  # Number of matching specializations
            self.reviewers[x[0]]["success_rate"],  # Success rate
            -self.reviewers[x[0]]["current_reviews"]  # Prefer less loaded reviewers
        ), reverse=True)
        
        return [reviewer_id for reviewer_id, _ in suitable_reviewers]
    
    async def submit_peer_review(self, task_id: str, reviewer_id: str, review_data: Dict[str, Any]) -> bool:
        """Submit a peer review for a task"""
        if task_id not in self.pending_verifications:
            return False
        
        verification_data = self.pending_verifications[task_id]
        
        # Find reviewer assignment
        reviewer_assignment = None
        for assignment in verification_data["reviewer_assignments"]:
            if assignment["reviewer_id"] == reviewer_id and assignment["status"] == "pending":
                reviewer_assignment = assignment
                break
        
        if not reviewer_assignment:
            return False
        
        # Update reviewer assignment
        reviewer_assignment["status"] = "completed"
        reviewer_assignment["completed_at"] = datetime.utcnow()
        reviewer_assignment["review_data"] = review_data
        reviewer_assignment["recommendation"] = review_data.get("recommendation", "approve")
        reviewer_assignment["score"] = review_data.get("score", 0.8)
        
        # Update reviewer info
        if reviewer_id in self.reviewers:
            self.reviewers[reviewer_id]["current_reviews"] -= 1
            self.reviewers[reviewer_id]["last_active"] = datetime.utcnow()
            self.reviewers[reviewer_id]["review_history"].append({
                "task_id": task_id,
                "completed_at": datetime.utcnow(),
                "recommendation": reviewer_assignment["recommendation"]
            })
        
        # Check if all reviews are complete
        pending_reviews = [
            assignment for assignment in verification_data["reviewer_assignments"]
            if assignment["status"] == "pending"
        ]
        
        if not pending_reviews:
            # All reviews complete, make final decision
            await self._finalize_peer_review(verification_data)
        
        logger.info(f"Peer review submitted by {reviewer_id} for task {task_id}")
        return True
    
    async def _finalize_peer_review(self, verification_data: Dict[str, Any]):
        """Finalize peer review and make decision"""
        task_id = verification_data["task_id"]
        
        # Analyze all reviews
        reviews = verification_data["reviewer_assignments"]
        total_score = 0.0
        approve_count = 0
        reject_count = 0
        
        for review in reviews:
            if review["status"] == "completed":
                total_score += review["score"]
                if review["recommendation"] == "approve":
                    approve_count += 1
                else:
                    reject_count += 1
        
        completed_reviews = len([r for r in reviews if r["status"] == "completed"])
        avg_score = total_score / completed_reviews if completed_reviews > 0 else 0.0
        
        # Make decision based on reviews
        if approve_count > reject_count and avg_score >= self.config["quality_score_threshold"]:
            await self._approve_verification(verification_data, f"Peer review approved (score: {avg_score:.2f})")
        else:
            rejection_reasons = []
            for review in reviews:
                if review.get("recommendation") == "reject":
                    rejection_reasons.extend(review.get("review_data", {}).get("issues", []))
            
            await self._reject_verification(verification_data, f"Peer review rejected: {rejection_reasons}")
    
    def _should_auto_approve(self, verification_data: Dict[str, Any]) -> bool:
        """Determine if task should be auto-approved"""
        if not self.config["enable_auto_approval"]:
            return False
        
        # Check quality score
        if verification_data["quality_score"] < self.config["auto_approve_threshold"]:
            return False
        
        # Check if all verification checks passed
        checks = verification_data["verification_checks"]
        for check_name, result in checks.items():
            if not result.get("passed", False):
                return False
        
        # Check task complexity
        task_id = verification_data["task_id"]
        task = self.task_engine.tasks[task_id]
        
        if task.priority in [TaskPriority.CRITICAL, TaskPriority.URGENT]:
            return False  # Critical tasks need human review
        
        complexity = verification_data["completion_data"].get("complexity", "normal")
        if complexity == "high":
            return False
        
        return True
    
    async def _approve_verification(self, verification_data: Dict[str, Any], reason: str):
        """Approve task verification"""
        task_id = verification_data["task_id"]
        verification_data["status"] = "approved"
        verification_data["approved_at"] = datetime.utcnow()
        verification_data["approval_reason"] = reason
        
        # Update task status
        task = self.task_engine.tasks[task_id]
        task.status = TaskStatus.COMPLETED
        
        # Update metrics
        self.quality_metrics["total_verifications"] += 1
        self.quality_metrics["approved_tasks"] += 1
        
        # Calculate verification time
        verification_time = (verification_data["approved_at"] - verification_data["submitted_at"]).total_seconds()
        
        # Update average verification time
        total_time = self.quality_metrics["avg_verification_time"] * (self.quality_metrics["total_verifications"] - 1)
        total_time += verification_time
        self.quality_metrics["avg_verification_time"] = total_time / self.quality_metrics["total_verifications"]
        
        # Update quality score distribution
        score_bucket = int(verification_data["quality_score"] * 10) / 10
        self.quality_metrics["quality_score_distribution"][score_bucket] += 1
        
        # Move to history
        self.verification_history.append(verification_data.copy())
        del self.pending_verifications[task_id]
        
        # Trigger dependent tasks
        await self.task_engine._check_dependent_tasks(task_id)
        
        logger.info(f"Task {task_id} verification approved: {reason}")
    
    async def _reject_verification(self, verification_data: Dict[str, Any], reason: str):
        """Reject task verification"""
        task_id = verification_data["task_id"]
        verification_data["status"] = "rejected"
        verification_data["rejected_at"] = datetime.utcnow()
        verification_data["rejection_reason"] = reason
        
        # Update task status
        task = self.task_engine.tasks[task_id]
        task.status = TaskStatus.FAILED
        
        # Update metrics
        self.quality_metrics["total_verifications"] += 1
        self.quality_metrics["rejected_tasks"] += 1
        self.quality_metrics["common_rejection_reasons"][reason] += 1
        
        # Move to history
        self.verification_history.append(verification_data.copy())
        del self.pending_verifications[task_id]
        
        # Potentially trigger reassignment
        await self.task_engine.reassignment_system.handle_task_failure(task_id, task.assigned_agent, f"Verification failed: {reason}")
        
        logger.info(f"Task {task_id} verification rejected: {reason}")
    
    async def _auto_approve_task(self, task_id: str, reason: str) -> str:
        """Auto-approve task without verification"""
        verification_id = str(uuid.uuid4())
        
        verification_data = {
            "verification_id": verification_id,
            "task_id": task_id,
            "submitted_at": datetime.utcnow(),
            "status": "approved",
            "quality_score": 1.0,
            "approval_reason": reason
        }
        
        await self._approve_verification(verification_data, reason)
        return verification_id
    
    async def _verification_loop(self):
        """Background verification processing loop"""
        while self.running:
            try:
                await self._process_verification_queue()
                await self._check_verification_timeouts()
                await asyncio.sleep(30)  # Check every 30 seconds
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Verification loop error: {e}")
                await asyncio.sleep(60)
    
    async def _process_verification_queue(self):
        """Process pending verifications"""
        # This is mostly handled in _process_verification, but we can add
        # additional queue management logic here if needed
        pass
    
    async def _check_verification_timeouts(self):
        """Check for timed out verifications"""
        now = datetime.utcnow()
        
        for task_id, verification_data in list(self.pending_verifications.items()):
            if verification_data["estimated_completion"] < now:
                # Verification timed out
                if verification_data["status"] == "peer_review":
                    # Check if we have any completed reviews
                    completed_reviews = [
                        review for review in verification_data["reviewer_assignments"]
                        if review["status"] == "completed"
                    ]
                    
                    if completed_reviews:
                        # Use partial reviews for decision
                        await self._finalize_peer_review(verification_data)
                    else:
                        # No reviews completed, auto-approve or reject based on quality
                        if verification_data["quality_score"] >= self.config["quality_score_threshold"]:
                            await self._approve_verification(verification_data, "Timeout - auto-approved based on quality")
                        else:
                            await self._reject_verification(verification_data, "Timeout - no peer reviews completed")
                else:
                    # General timeout
                    await self._reject_verification(verification_data, "Verification timeout")
    
    # Public API methods
    
    def get_verification_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get verification status for a task"""
        if task_id in self.pending_verifications:
            return self.pending_verifications[task_id].copy()
        
        # Check history
        for verification in self.verification_history:
            if verification["task_id"] == task_id:
                return verification.copy()
        
        return None
    
    def get_verification_metrics(self) -> Dict[str, Any]:
        """Get verification system metrics"""
        return {
            "quality_metrics": self.quality_metrics.copy(),
            "config": self.config.copy(),
            "pending_verifications": len(self.pending_verifications),
            "total_reviewers": len(self.reviewers),
            "active_reviewers": len([r for r in self.reviewers.values() if r["current_reviews"] > 0]),
            "verification_queue_size": len(self.verification_queue)
        }
    
    def get_reviewer_performance(self, reviewer_id: str) -> Optional[Dict[str, Any]]:
        """Get performance metrics for a reviewer"""
        if reviewer_id not in self.reviewers:
            return None
        
        reviewer = self.reviewers[reviewer_id]
        return {
            "reviewer_id": reviewer_id,
            "current_reviews": reviewer["current_reviews"],
            "total_reviews": len(reviewer["review_history"]),
            "success_rate": reviewer["success_rate"],
            "average_review_time": reviewer["average_review_time"],
            "specializations": reviewer["specializations"],
            "last_active": reviewer["last_active"].isoformat()
        }
    
    def update_verification_config(self, config_updates: Dict[str, Any]) -> bool:
        """Update verification configuration"""
        try:
            for key, value in config_updates.items():
                if key in self.config:
                    self.config[key] = value
                    logger.info(f"Updated verification config {key} to {value}")
                else:
                    logger.warning(f"Unknown verification config key: {key}")
            return True
        except Exception as e:
            logger.error(f"Failed to update verification config: {e}")
            return False

# Global task assignment engine instance
task_engine = TaskAssignmentEngine()