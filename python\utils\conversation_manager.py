"""
Conversation History Manager for Metamorphic Reactor
Manages persistent storage and retrieval of conversation history
"""

import json
import sqlite3
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import logging
from contextlib import asynccontextmanager
import aiosqlite

from ..agents.agent_types import ConversationRound, AgentResponse, TaskContext

logger = logging.getLogger(__name__)

class ConversationManager:
    """
    Manages conversation history with persistent storage
    
    Features:
    - Persistent SQLite storage
    - Efficient querying and filtering
    - Automatic cleanup of old conversations
    - Export capabilities
    - Search and analytics
    """
    
    def __init__(self, db_path: str = "data/conversation_history.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self._initialized = False
    
    async def initialize(self):
        """Initialize the database and create tables"""
        if self._initialized:
            return
        
        async with aiosqlite.connect(self.db_path) as db:
            await self._create_tables(db)
            await db.commit()
        
        self._initialized = True
        logger.info(f"Conversation manager initialized with database: {self.db_path}")
    
    async def _create_tables(self, db: aiosqlite.Connection):
        """Create database tables"""
        
        # Tasks table
        await db.execute("""
            CREATE TABLE IF NOT EXISTS tasks (
                task_id TEXT PRIMARY KEY,
                description TEXT NOT NULL,
                status TEXT NOT NULL,
                max_rounds INTEGER NOT NULL,
                timeout_minutes INTEGER NOT NULL,
                consensus_score REAL DEFAULT 0.0,
                final_consensus TEXT,
                created_at TEXT NOT NULL,
                started_at TEXT,
                completed_at TEXT,
                context_files TEXT,  -- JSON array
                participating_agents TEXT  -- JSON array
            )
        """)
        
        # Conversation rounds table
        await db.execute("""
            CREATE TABLE IF NOT EXISTS conversation_rounds (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id TEXT NOT NULL,
                round_number INTEGER NOT NULL,
                consensus_level TEXT NOT NULL,
                consensus_score REAL NOT NULL,
                started_at TEXT NOT NULL,
                completed_at TEXT,
                duration_ms INTEGER,
                is_complete BOOLEAN DEFAULT FALSE,
                requires_meta_block BOOLEAN DEFAULT FALSE,
                meta_block_reason TEXT,
                FOREIGN KEY (task_id) REFERENCES tasks (task_id),
                UNIQUE (task_id, round_number)
            )
        """)
        
        # Agent responses table
        await db.execute("""
            CREATE TABLE IF NOT EXISTS agent_responses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id TEXT NOT NULL,
                round_number INTEGER NOT NULL,
                agent_id TEXT NOT NULL,
                session_id TEXT NOT NULL,
                role TEXT NOT NULL,
                message_id TEXT NOT NULL,
                content TEXT NOT NULL,
                reasoning TEXT,
                confidence REAL NOT NULL,
                agrees_with_proposal BOOLEAN NOT NULL,
                consensus_score REAL NOT NULL,
                suggested_changes TEXT,  -- JSON array
                token_count INTEGER NOT NULL,
                response_time_ms INTEGER NOT NULL,
                timestamp TEXT NOT NULL,
                quality_score REAL,
                complexity_level TEXT,
                FOREIGN KEY (task_id) REFERENCES tasks (task_id)
            )
        """)
        
        # Indexes for better performance
        await db.execute("CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks (status)")
        await db.execute("CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON tasks (created_at)")
        await db.execute("CREATE INDEX IF NOT EXISTS idx_rounds_task_id ON conversation_rounds (task_id)")
        await db.execute("CREATE INDEX IF NOT EXISTS idx_responses_task_id ON agent_responses (task_id)")
        await db.execute("CREATE INDEX IF NOT EXISTS idx_responses_agent_id ON agent_responses (agent_id)")
        await db.execute("CREATE INDEX IF NOT EXISTS idx_responses_timestamp ON agent_responses (timestamp)")
    
    async def save_task(self, task_context: TaskContext):
        """Save or update task context"""
        
        await self.initialize()
        
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                INSERT OR REPLACE INTO tasks (
                    task_id, description, status, max_rounds, timeout_minutes,
                    consensus_score, final_consensus, created_at, started_at, completed_at,
                    context_files, participating_agents
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                task_context.task_id,
                task_context.description,
                task_context.status.value,
                task_context.max_rounds,
                task_context.timeout_minutes,
                task_context.consensus_score,
                task_context.final_consensus,
                task_context.created_at.isoformat(),
                task_context.started_at.isoformat() if task_context.started_at else None,
                task_context.completed_at.isoformat() if task_context.completed_at else None,
                json.dumps(task_context.context_files),
                json.dumps(task_context.participating_agents)
            ))
            await db.commit()
        
        logger.debug(f"Saved task: {task_context.task_id}")
    
    async def save_conversation_round(self, task_id: str, round_context: ConversationRound):
        """Save conversation round"""
        
        await self.initialize()
        
        duration_ms = None
        if round_context.completed_at and round_context.started_at:
            duration = round_context.completed_at - round_context.started_at
            duration_ms = int(duration.total_seconds() * 1000)
        
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                INSERT OR REPLACE INTO conversation_rounds (
                    task_id, round_number, consensus_level, consensus_score,
                    started_at, completed_at, duration_ms, is_complete,
                    requires_meta_block, meta_block_reason
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                task_id,
                round_context.round_number,
                round_context.consensus_level.value,
                round_context.consensus_score,
                round_context.started_at.isoformat(),
                round_context.completed_at.isoformat() if round_context.completed_at else None,
                duration_ms,
                round_context.is_complete,
                round_context.requires_meta_block,
                round_context.meta_block_reason
            ))
            
            # Save agent responses for this round
            for response in round_context.responses:
                await self.save_agent_response(task_id, round_context.round_number, response)
            
            await db.commit()
        
        logger.debug(f"Saved conversation round {round_context.round_number} for task {task_id}")
    
    async def save_agent_response(self, task_id: str, round_number: int, response: AgentResponse):
        """Save individual agent response"""
        
        await self.initialize()
        
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                INSERT OR REPLACE INTO agent_responses (
                    task_id, round_number, agent_id, session_id, role, message_id,
                    content, reasoning, confidence, agrees_with_proposal, consensus_score,
                    suggested_changes, token_count, response_time_ms, timestamp,
                    quality_score, complexity_level
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                task_id,
                round_number,
                response.agent_id,
                response.session_id,
                response.role.value,
                response.message_id,
                response.content,
                response.reasoning,
                response.confidence,
                response.agrees_with_proposal,
                response.consensus_score,
                json.dumps(response.suggested_changes),
                response.token_count,
                response.response_time_ms,
                response.timestamp.isoformat(),
                response.quality_score,
                response.complexity_level
            ))
            await db.commit()
        
        logger.debug(f"Saved agent response from {response.agent_id} for task {task_id}")
    
    async def get_task_history(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get complete task history including all rounds and responses"""
        
        await self.initialize()
        
        async with aiosqlite.connect(self.db_path) as db:
            # Get task info
            async with db.execute("SELECT * FROM tasks WHERE task_id = ?", (task_id,)) as cursor:
                task_row = await cursor.fetchone()
                if not task_row:
                    return None
            
            # Convert to dict
            columns = [desc[0] for desc in cursor.description]
            task_data = dict(zip(columns, task_row))
            
            # Parse JSON fields
            task_data["context_files"] = json.loads(task_data["context_files"] or "[]")
            task_data["participating_agents"] = json.loads(task_data["participating_agents"] or "[]")
            
            # Get conversation rounds
            rounds = []
            async with db.execute("""
                SELECT * FROM conversation_rounds 
                WHERE task_id = ? 
                ORDER BY round_number
            """, (task_id,)) as cursor:
                async for row in cursor:
                    round_columns = [desc[0] for desc in cursor.description]
                    round_data = dict(zip(round_columns, row))
                    
                    # Get responses for this round
                    round_data["responses"] = await self._get_round_responses(db, task_id, round_data["round_number"])
                    rounds.append(round_data)
            
            task_data["conversation_rounds"] = rounds
            
            return task_data
    
    async def _get_round_responses(self, db: aiosqlite.Connection, task_id: str, round_number: int) -> List[Dict[str, Any]]:
        """Get all responses for a specific round"""
        
        responses = []
        async with db.execute("""
            SELECT * FROM agent_responses 
            WHERE task_id = ? AND round_number = ? 
            ORDER BY timestamp
        """, (task_id, round_number)) as cursor:
            async for row in cursor:
                columns = [desc[0] for desc in cursor.description]
                response_data = dict(zip(columns, row))
                
                # Parse JSON fields
                response_data["suggested_changes"] = json.loads(response_data["suggested_changes"] or "[]")
                
                responses.append(response_data)
        
        return responses
    
    async def search_conversations(
        self,
        agent_id: Optional[str] = None,
        role: Optional[str] = None,
        task_status: Optional[str] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        content_search: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Search conversations with various filters"""
        
        await self.initialize()
        
        conditions = []
        params = []
        
        base_query = """
            SELECT DISTINCT t.task_id, t.description, t.status, t.created_at, t.consensus_score,
                   COUNT(cr.round_number) as total_rounds,
                   COUNT(ar.id) as total_responses
            FROM tasks t
            LEFT JOIN conversation_rounds cr ON t.task_id = cr.task_id
            LEFT JOIN agent_responses ar ON t.task_id = ar.task_id
        """
        
        if agent_id:
            conditions.append("ar.agent_id = ?")
            params.append(agent_id)
        
        if role:
            conditions.append("ar.role = ?")
            params.append(role)
        
        if task_status:
            conditions.append("t.status = ?")
            params.append(task_status)
        
        if date_from:
            conditions.append("t.created_at >= ?")
            params.append(date_from.isoformat())
        
        if date_to:
            conditions.append("t.created_at <= ?")
            params.append(date_to.isoformat())
        
        if content_search:
            conditions.append("ar.content LIKE ?")
            params.append(f"%{content_search}%")
        
        if conditions:
            base_query += " WHERE " + " AND ".join(conditions)
        
        base_query += """
            GROUP BY t.task_id, t.description, t.status, t.created_at, t.consensus_score
            ORDER BY t.created_at DESC
            LIMIT ?
        """
        params.append(limit)
        
        results = []
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute(base_query, params) as cursor:
                async for row in cursor:
                    columns = [desc[0] for desc in cursor.description]
                    results.append(dict(zip(columns, row)))
        
        return results
    
    async def get_agent_statistics(self, agent_id: str) -> Dict[str, Any]:
        """Get statistics for a specific agent"""
        
        await self.initialize()
        
        async with aiosqlite.connect(self.db_path) as db:
            stats = {}
            
            # Basic response stats
            async with db.execute("""
                SELECT COUNT(*) as total_responses,
                       AVG(confidence) as avg_confidence,
                       AVG(consensus_score) as avg_consensus_score,
                       AVG(response_time_ms) as avg_response_time,
                       SUM(token_count) as total_tokens
                FROM agent_responses
                WHERE agent_id = ?
            """, (agent_id,)) as cursor:
                row = await cursor.fetchone()
                if row:
                    columns = [desc[0] for desc in cursor.description]
                    stats.update(dict(zip(columns, row)))
            
            # Agreement rate
            async with db.execute("""
                SELECT 
                    SUM(CASE WHEN agrees_with_proposal THEN 1 ELSE 0 END) as agreements,
                    COUNT(*) as total
                FROM agent_responses
                WHERE agent_id = ?
            """, (agent_id,)) as cursor:
                row = await cursor.fetchone()
                if row and row[1] > 0:
                    stats["agreement_rate"] = row[0] / row[1]
                else:
                    stats["agreement_rate"] = 0.0
            
            # Task participation
            async with db.execute("""
                SELECT COUNT(DISTINCT task_id) as tasks_participated
                FROM agent_responses
                WHERE agent_id = ?
            """, (agent_id,)) as cursor:
                row = await cursor.fetchone()
                if row:
                    stats["tasks_participated"] = row[0]
            
            return stats
    
    async def cleanup_old_conversations(self, days_to_keep: int = 30) -> int:
        """Clean up conversations older than specified days"""
        
        await self.initialize()
        
        cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)
        
        async with aiosqlite.connect(self.db_path) as db:
            # Get task IDs to delete
            async with db.execute("""
                SELECT task_id FROM tasks 
                WHERE created_at < ? AND status IN ('completed', 'failed', 'stopped')
            """, (cutoff_date.isoformat(),)) as cursor:
                task_ids = [row[0] async for row in cursor]
            
            if not task_ids:
                return 0
            
            # Delete related records
            for task_id in task_ids:
                await db.execute("DELETE FROM agent_responses WHERE task_id = ?", (task_id,))
                await db.execute("DELETE FROM conversation_rounds WHERE task_id = ?", (task_id,))
                await db.execute("DELETE FROM tasks WHERE task_id = ?", (task_id,))
            
            await db.commit()
        
        logger.info(f"Cleaned up {len(task_ids)} old conversations")
        return len(task_ids)
    
    async def export_conversation(self, task_id: str, format: str = "json") -> str:
        """Export conversation in specified format"""
        
        history = await self.get_task_history(task_id)
        if not history:
            raise ValueError(f"Task {task_id} not found")
        
        if format.lower() == "json":
            return json.dumps(history, indent=2, default=str)
        elif format.lower() == "markdown":
            return self._export_as_markdown(history)
        else:
            raise ValueError(f"Unsupported export format: {format}")
    
    def _export_as_markdown(self, history: Dict[str, Any]) -> str:
        """Export conversation as markdown"""
        
        md = f"""# Task: {history['description']}

**Status**: {history['status']}
**Created**: {history['created_at']}
**Consensus Score**: {history['consensus_score']:.2f}

## Conversation Rounds

"""
        
        for round_data in history['conversation_rounds']:
            md += f"""### Round {round_data['round_number']}
**Consensus Level**: {round_data['consensus_level']}
**Consensus Score**: {round_data['consensus_score']:.2f}

"""
            
            for response in round_data['responses']:
                md += f"""#### {response['role'].title()} Agent ({response['agent_id']})
**Confidence**: {response['confidence']:.2f}
**Agrees**: {"Yes" if response['agrees_with_proposal'] else "No"}

{response['content']}

---

"""
        
        return md
    
    async def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics"""
        
        await self.initialize()
        
        stats = {}
        async with aiosqlite.connect(self.db_path) as db:
            # Count tables
            for table in ["tasks", "conversation_rounds", "agent_responses"]:
                async with db.execute(f"SELECT COUNT(*) FROM {table}") as cursor:
                    count = await cursor.fetchone()
                    stats[f"{table}_count"] = count[0] if count else 0
            
            # Database size
            stats["database_size_bytes"] = self.db_path.stat().st_size
            stats["database_path"] = str(self.db_path)
        
        return stats


# Global conversation manager instance
conversation_manager = ConversationManager()