"""
Base agent class for Metamorphic Reactor
Provides common functionality for all agent types
"""

import asyncio
import logging
import time
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, AsyncGenerator
from datetime import datetime

from .agent_types import (
    AgentConfig, AgentResponse, AgentRole, AgentStatus, 
    TaskContext, ConversationHistory
)

logger = logging.getLogger(__name__)

class BaseAgent(ABC):
    """Abstract base class for all agent types"""
    
    def __init__(self, config: AgentConfig):
        self.config = config
        self.status = AgentStatus.IDLE
        self.conversation_history: ConversationHistory = []
        self.response_times: List[int] = []
        self.error_count = 0
        self.last_activity = datetime.utcnow()
        
        # Performance metrics
        self.total_responses = 0
        self.total_tokens = 0
        self.average_confidence = 0.0
        
        logger.info(f"Initialized {self.config.role} agent: {self.config.agent_id}")
    
    @property
    def agent_id(self) -> str:
        return self.config.agent_id
    
    @property
    def role(self) -> AgentRole:
        return self.config.role
    
    @property
    def session_id(self) -> str:
        return self.config.session_id
    
    @abstractmethod
    async def generate_response(
        self, 
        task_context: TaskContext, 
        conversation_history: ConversationHistory,
        current_proposals: List[AgentResponse]
    ) -> AgentResponse:
        """Generate a response to the current task state"""
        pass
    
    @abstractmethod
    def get_role_prompt(self, task_context: TaskContext) -> str:
        """Get the role-specific system prompt"""
        pass
    
    async def process_task(
        self,
        task_context: TaskContext,
        conversation_history: ConversationHistory,
        current_proposals: List[AgentResponse]
    ) -> AgentResponse:
        """Main task processing method with error handling and metrics"""
        
        start_time = time.time()
        self.status = AgentStatus.THINKING
        self.last_activity = datetime.utcnow()
        
        try:
            # Validate inputs
            if not task_context or not task_context.description:
                raise ValueError("Invalid task context provided")
            
            # Generate response
            self.status = AgentStatus.RESPONDING
            response = await self.generate_response(
                task_context, conversation_history, current_proposals
            )
            
            # Update metrics
            response_time = int((time.time() - start_time) * 1000)
            response.response_time_ms = response_time
            self.response_times.append(response_time)
            
            # Keep only last 100 response times
            if len(self.response_times) > 100:
                self.response_times.pop(0)
            
            self.total_responses += 1
            self.total_tokens += response.token_count
            self.average_confidence = (
                (self.average_confidence * (self.total_responses - 1) + response.confidence) 
                / self.total_responses
            )
            
            # Add to conversation history
            self.conversation_history.append({
                "agent_id": self.agent_id,
                "role": self.role.value,
                "content": response.content,
                "timestamp": response.timestamp.isoformat(),
                "confidence": response.confidence
            })
            
            self.status = AgentStatus.IDLE
            logger.info(f"Agent {self.agent_id} completed response in {response_time}ms")
            
            return response
            
        except Exception as e:
            self.error_count += 1
            self.status = AgentStatus.ERROR
            
            logger.error(f"Error in agent {self.agent_id}: {e}")
            
            # Return error response
            return AgentResponse(
                agent_id=self.agent_id,
                session_id=self.session_id,
                role=self.role,
                message_id=f"error_{int(time.time())}",
                content=f"Error processing task: {str(e)}",
                confidence=0.0,
                agrees_with_proposal=False,
                consensus_score=0.0,
                token_count=0,
                response_time_ms=int((time.time() - start_time) * 1000)
            )
    
    async def stream_response(
        self,
        task_context: TaskContext,
        conversation_history: ConversationHistory,
        current_proposals: List[AgentResponse]
    ) -> AsyncGenerator[str, None]:
        """Stream response tokens in real-time"""
        
        self.status = AgentStatus.THINKING
        
        try:
            # Get full response first (in real implementation, this would stream)
            response = await self.generate_response(
                task_context, conversation_history, current_proposals
            )
            
            # Simulate token streaming
            tokens = response.content.split(' ')
            
            for i, token in enumerate(tokens):
                yield f"{token} "
                
                # Add slight delay to simulate real streaming
                if i % 5 == 0:  # Pause every 5 tokens
                    await asyncio.sleep(0.1)
            
            self.status = AgentStatus.IDLE
            
        except Exception as e:
            logger.error(f"Error streaming response: {e}")
            yield f"[ERROR: {str(e)}]"
            self.status = AgentStatus.ERROR
    
    def evaluate_consensus(
        self, 
        current_proposals: List[AgentResponse],
        own_response: AgentResponse
    ) -> float:
        """Evaluate consensus score with other agents"""
        
        if not current_proposals:
            return 1.0  # First response defaults to full consensus
        
        # Simple consensus evaluation based on response similarity
        # In a real implementation, this would use semantic similarity
        consensus_scores = []
        
        for proposal in current_proposals:
            if proposal.agent_id == self.agent_id:
                continue
            
            # Check if responses are generally aligned
            alignment_score = self._calculate_response_alignment(
                own_response.content, proposal.content
            )
            consensus_scores.append(alignment_score)
        
        return sum(consensus_scores) / len(consensus_scores) if consensus_scores else 1.0
    
    def _calculate_response_alignment(self, response1: str, response2: str) -> float:
        """Simple text similarity calculation"""
        
        # Convert to lowercase and split into words
        words1 = set(response1.lower().split())
        words2 = set(response2.lower().split())
        
        # Calculate Jaccard similarity
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        return intersection / union if union > 0 else 0.0
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics"""
        
        return {
            "agent_id": self.agent_id,
            "role": self.role.value,
            "status": self.status.value,
            "total_responses": self.total_responses,
            "total_tokens": self.total_tokens,
            "average_confidence": round(self.average_confidence, 3),
            "average_response_time": (
                round(sum(self.response_times) / len(self.response_times), 2) 
                if self.response_times else 0
            ),
            "error_count": self.error_count,
            "last_activity": self.last_activity.isoformat(),
            "conversation_length": len(self.conversation_history)
        }
    
    async def reset_session(self):
        """Reset agent session state"""
        
        self.status = AgentStatus.IDLE
        self.conversation_history.clear()
        self.last_activity = datetime.utcnow()
        
        logger.info(f"Reset session for agent {self.agent_id}")
    
    async def shutdown(self):
        """Graceful shutdown of agent"""
        
        self.status = AgentStatus.STOPPED
        logger.info(f"Agent {self.agent_id} shutting down")
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(id={self.agent_id}, role={self.role.value}, status={self.status.value})>"