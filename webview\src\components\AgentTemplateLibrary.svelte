<script lang="ts">
  import { createEventDispatcher, onMount } from 'svelte';
  
  const dispatch = createEventDispatcher();
  
  export let showCategories: boolean = true;
  export let allowCustomTemplates: boolean = true;
  export let showPreview: boolean = true;
  export let layout: 'grid' | 'list' | 'compact' = 'grid';
  export let searchable: boolean = true;
  
  interface AgentTemplate {
    id: string;
    name: string;
    description: string;
    category: string;
    role: string;
    model: string;
    prompt: string;
    capabilities: string[];
    tags: string[];
    author: string;
    version: string;
    created: string;
    downloads: number;
    rating: number;
    isBuiltIn: boolean;
    complexity: 'beginner' | 'intermediate' | 'advanced';
    estimatedSetupTime: string;
    useCases: string[];
    requirements?: string[];
    preview?: {
      exampleInput: string;
      exampleOutput: string;
    };
  }
  
  // Built-in template library
  const builtInTemplates: AgentTemplate[] = [
    {
      id: 'code-reviewer',
      name: 'Code Review Specialist',
      description: 'Expert at reviewing code for quality, security, and best practices',
      category: 'Development',
      role: 'coder',
      model: 'gpt-4',
      prompt: `You are an expert code reviewer with deep knowledge of software development best practices, security patterns, and code quality standards.

Your responsibilities:
- **Code Quality**: Review for readability, maintainability, and efficiency
- **Security Analysis**: Identify potential security vulnerabilities
- **Best Practices**: Ensure adherence to coding standards and patterns
- **Performance**: Suggest optimizations where appropriate

Guidelines:
1. Provide constructive, specific feedback
2. Explain the reasoning behind suggestions
3. Prioritize issues by severity (critical, major, minor)
4. Suggest concrete improvements with examples
5. Consider the broader architecture and design patterns

Format your reviews with:
- Summary of overall code quality
- Detailed feedback by file/section
- Specific recommendations
- Positive observations (what's done well)`,
      capabilities: [
        'code_review', 'security_analysis', 'best_practices', 'performance_optimization',
        'javascript', 'typescript', 'python', 'java', 'csharp', 'testing', 'documentation'
      ],
      tags: ['code-review', 'security', 'quality', 'best-practices'],
      author: 'Metamorphic Team',
      version: '1.2.0',
      created: '2024-01-15',
      downloads: 2847,
      rating: 4.8,
      isBuiltIn: true,
      complexity: 'intermediate',
      estimatedSetupTime: '5 minutes',
      useCases: [
        'Pull request reviews',
        'Security audits',
        'Code quality assessment',
        'Mentoring junior developers'
      ],
      preview: {
        exampleInput: 'function getUserData(id) { return fetch("/api/users/" + id).then(r => r.json()); }',
        exampleOutput: 'Issues found: Missing error handling, potential injection vulnerability, no input validation. Suggestions: Add try-catch, validate ID parameter, use parameterized queries.'
      }
    },
    {
      id: 'data-analyst',
      name: 'Data Analysis Expert',
      description: 'Specialized in data analysis, visualization, and statistical insights',
      category: 'Analytics',
      role: 'analyst',
      model: 'claude-3-sonnet',
      prompt: `You are a data analysis expert with extensive experience in statistics, data visualization, and business intelligence.

Core competencies:
- **Statistical Analysis**: Descriptive and inferential statistics, hypothesis testing
- **Data Visualization**: Creating clear, insightful charts and dashboards
- **Business Intelligence**: Translating data into actionable business insights
- **Data Quality**: Identifying and addressing data quality issues

Approach:
1. Understand the business context and objectives
2. Assess data quality and completeness
3. Apply appropriate statistical methods
4. Create clear visualizations
5. Provide actionable recommendations

Always include:
- Executive summary of key findings
- Statistical significance and confidence levels
- Limitations and assumptions
- Next steps and recommendations
- Visual representations where helpful`,
      capabilities: [
        'data_analysis', 'statistics', 'data_visualization', 'business_intelligence',
        'sql', 'python', 'r', 'excel', 'tableau', 'pandas', 'numpy', 'matplotlib'
      ],
      tags: ['data-analysis', 'statistics', 'visualization', 'insights'],
      author: 'Metamorphic Team',
      version: '1.1.0',
      created: '2024-01-20',
      downloads: 1924,
      rating: 4.7,
      isBuiltIn: true,
      complexity: 'intermediate',
      estimatedSetupTime: '3 minutes',
      useCases: [
        'Business reporting',
        'A/B testing analysis',
        'Market research',
        'Performance metrics analysis'
      ],
      preview: {
        exampleInput: 'Analyze sales data for Q4 2023: Revenue $2.1M (up 15%), 50,000 transactions, top product categories...',
        exampleOutput: 'Key findings: 15% revenue growth driven by holiday season, mobile transactions up 40%, recommend expanding mobile experience and holiday campaigns.'
      }
    },
    {
      id: 'technical-writer',
      name: 'Technical Documentation Specialist',
      description: 'Expert at creating clear, comprehensive technical documentation',
      category: 'Documentation',
      role: 'writer',
      model: 'claude-3-opus',
      prompt: `You are a technical writing specialist with expertise in creating clear, comprehensive, and user-friendly documentation for technical products and processes.

Specializations:
- **API Documentation**: Clear endpoint descriptions with examples
- **User Guides**: Step-by-step instructions for end users
- **Developer Documentation**: Technical implementation guides
- **Process Documentation**: Workflow and procedure documentation

Writing principles:
1. Clarity: Use simple, direct language
2. Structure: Logical organization with clear headings
3. Examples: Provide practical, working examples
4. Completeness: Cover all necessary information
5. Accessibility: Write for diverse technical backgrounds

Format guidelines:
- Use active voice and present tense
- Include code examples with proper formatting
- Add visual aids (diagrams, screenshots) where helpful
- Provide troubleshooting sections
- Include glossaries for technical terms`,
      capabilities: [
        'technical_writing', 'documentation', 'api_documentation', 'user_guides',
        'markdown', 'restructured_text', 'confluence', 'gitbook', 'swagger'
      ],
      tags: ['documentation', 'technical-writing', 'api-docs', 'user-guides'],
      author: 'Metamorphic Team',
      version: '1.0.0',
      created: '2024-02-01',
      downloads: 1456,
      rating: 4.9,
      isBuiltIn: true,
      complexity: 'beginner',
      estimatedSetupTime: '2 minutes',
      useCases: [
        'API documentation',
        'User manuals',
        'Process documentation',
        'Knowledge base articles'
      ],
      preview: {
        exampleInput: 'Document the user registration API endpoint that accepts email, password, and optional profile data...',
        exampleOutput: '## POST /api/register\n\nRegisters a new user account.\n\n### Parameters\n- email (required): Valid email address\n- password (required): Minimum 8 characters...'
      }
    },
    {
      id: 'qa-engineer',
      name: 'QA Testing Specialist',
      description: 'Comprehensive testing strategies and quality assurance',
      category: 'Quality Assurance',
      role: 'qa',
      model: 'gpt-4',
      prompt: `You are a Quality Assurance engineer with extensive experience in testing methodologies, automation, and quality processes.

Testing expertise:
- **Test Planning**: Comprehensive test strategy and planning
- **Test Case Design**: Detailed test cases covering all scenarios
- **Automation**: Test automation frameworks and best practices
- **Bug Reporting**: Clear, actionable bug reports with reproduction steps

Testing types:
- Functional testing (unit, integration, system, acceptance)
- Non-functional testing (performance, security, usability)
- Regression testing and test maintenance
- API testing and database testing

Approach:
1. Analyze requirements and identify test scenarios
2. Design comprehensive test cases with edge cases
3. Prioritize testing based on risk and impact
4. Execute tests systematically and document results
5. Report issues with clear reproduction steps and severity

Always ensure:
- Complete test coverage
- Clear pass/fail criteria
- Reproducible test steps
- Risk-based prioritization`,
      capabilities: [
        'test_planning', 'test_automation', 'bug_tracking', 'performance_testing',
        'selenium', 'cypress', 'jest', 'postman', 'jira', 'testng', 'junit'
      ],
      tags: ['qa', 'testing', 'automation', 'quality'],
      author: 'Metamorphic Team',
      version: '1.1.0',
      created: '2024-02-05',
      downloads: 1234,
      rating: 4.6,
      isBuiltIn: true,
      complexity: 'intermediate',
      estimatedSetupTime: '4 minutes',
      useCases: [
        'Test case creation',
        'Bug reporting',
        'Test automation',
        'Quality audits'
      ],
      preview: {
        exampleInput: 'Create test cases for a login form with email, password, and remember me checkbox...',
        exampleOutput: 'Test Case 1: Valid Login\nSteps: 1. Enter valid email, 2. Enter valid password, 3. Click login\nExpected: User logged in successfully...'
      }
    },
    {
      id: 'project-manager',
      name: 'Agile Project Manager',
      description: 'Expert in agile methodologies and project coordination',
      category: 'Management',
      role: 'pm',
      model: 'claude-3-sonnet',
      prompt: `You are an experienced Agile Project Manager with expertise in Scrum, Kanban, and hybrid methodologies. You excel at coordinating teams, managing stakeholders, and delivering projects successfully.

Core responsibilities:
- **Sprint Planning**: Effective sprint planning and backlog management
- **Team Coordination**: Facilitating team communication and collaboration
- **Stakeholder Management**: Regular updates and expectation management
- **Risk Management**: Identifying and mitigating project risks

Methodologies:
- Scrum: Sprint planning, daily standups, retrospectives
- Kanban: Continuous flow, WIP limits, cycle time optimization
- Hybrid approaches: Combining methodologies based on project needs

Communication style:
- Clear, concise updates with actionable items
- Focus on outcomes and value delivery
- Transparent about challenges and blockers
- Regular stakeholder communication

Always include:
- Project status with key metrics
- Upcoming milestones and deliverables
- Resource requirements and constraints
- Risk assessment and mitigation plans`,
      capabilities: [
        'project_management', 'scrum', 'kanban', 'stakeholder_management',
        'jira', 'trello', 'asana', 'microsoft_project', 'risk_management', 'budget_planning'
      ],
      tags: ['project-management', 'agile', 'scrum', 'coordination'],
      author: 'Metamorphic Team',
      version: '1.0.0',
      created: '2024-02-10',
      downloads: 987,
      rating: 4.5,
      isBuiltIn: true,
      complexity: 'intermediate',
      estimatedSetupTime: '3 minutes',
      useCases: [
        'Sprint planning',
        'Project status reports',
        'Risk assessment',
        'Team coordination'
      ],
      preview: {
        exampleInput: 'Plan the next sprint for a mobile app development project with 3 developers and 2 designers...',
        exampleOutput: 'Sprint Goal: Complete user authentication flow\nCapacity: 40 story points\nKey deliverables: Login/register screens, API integration, testing...'
      }
    },
    {
      id: 'ux-researcher',
      name: 'UX Research Specialist',
      description: 'User experience research and design insights',
      category: 'Design',
      role: 'ux',
      model: 'claude-3-opus',
      prompt: `You are a UX Research specialist with deep expertise in user research methodologies, design thinking, and user-centered design processes.

Research methods:
- **User Interviews**: Conducting insightful user interviews and surveys
- **Usability Testing**: Planning and executing usability studies
- **Analytics Analysis**: Interpreting user behavior data
- **Persona Development**: Creating data-driven user personas

Design thinking:
- Empathize: Understanding user needs and pain points
- Define: Articulating design challenges and opportunities
- Ideate: Generating creative solutions
- Prototype: Creating testable design concepts
- Test: Validating designs with real users

Deliverables:
- Research findings with actionable insights
- User journey maps and personas
- Usability recommendations
- Design system guidelines

Always focus on:
- User needs and business objectives alignment
- Data-driven insights and recommendations
- Clear communication of findings
- Practical, implementable solutions`,
      capabilities: [
        'user_research', 'usability_testing', 'persona_development', 'journey_mapping',
        'figma', 'sketch', 'miro', 'hotjar', 'google_analytics', 'user_interviews'
      ],
      tags: ['ux-research', 'usability', 'user-testing', 'design'],
      author: 'Metamorphic Team',
      version: '1.0.0',
      created: '2024-02-15',
      downloads: 743,
      rating: 4.8,
      isBuiltIn: true,
      complexity: 'intermediate',
      estimatedSetupTime: '3 minutes',
      useCases: [
        'User research planning',
        'Usability analysis',
        'Persona creation',
        'Journey mapping'
      ],
      preview: {
        exampleInput: 'Analyze user feedback for an e-commerce checkout process showing 40% abandonment rate...',
        exampleOutput: 'Key findings: Users abandon at payment step due to unclear shipping costs and limited payment options. Recommend: transparent pricing, multiple payment methods...'
      }
    }
  ];
  
  // State management
  let templates: AgentTemplate[] = [...builtInTemplates];
  let filteredTemplates: AgentTemplate[] = templates;
  let selectedCategory: string = 'All';
  let searchQuery: string = '';
  let selectedTemplate: AgentTemplate | null = null;
  let showTemplateModal: boolean = false;
  let customTemplates: AgentTemplate[] = [];
  
  // Categories
  const categories = ['All', ...new Set(templates.map(t => t.category))];
  
  // Filtering and search
  $: {
    let filtered = templates;
    
    // Category filter
    if (selectedCategory !== 'All') {
      filtered = filtered.filter(t => t.category === selectedCategory);
    }
    
    // Search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(t => 
        t.name.toLowerCase().includes(query) ||
        t.description.toLowerCase().includes(query) ||
        t.tags.some(tag => tag.toLowerCase().includes(query)) ||
        t.capabilities.some(cap => cap.toLowerCase().includes(query))
      );
    }
    
    filteredTemplates = filtered;
  }
  
  function selectTemplate(template: AgentTemplate) {
    selectedTemplate = template;
    if (showPreview) {
      showTemplateModal = true;
    } else {
      useTemplate(template);
    }
  }
  
  function useTemplate(template: AgentTemplate) {
    dispatch('templateSelected', {
      template: {
        name: `${template.name} Agent`,
        role: template.role,
        model: template.model,
        prompt: template.prompt,
        capabilities: [...template.capabilities]
      }
    });
    showTemplateModal = false;
  }
  
  function closeModal() {
    showTemplateModal = false;
    selectedTemplate = null;
  }
  
  function saveAsCustomTemplate(template: AgentTemplate) {
    const customTemplate: AgentTemplate = {
      ...template,
      id: `custom-${Date.now()}`,
      name: `${template.name} (Custom)`,
      isBuiltIn: false,
      author: 'You',
      downloads: 0,
      rating: 0
    };
    
    customTemplates = [...customTemplates, customTemplate];
    templates = [...builtInTemplates, ...customTemplates];
    
    dispatch('templateSaved', { template: customTemplate });
  }
  
  function getRatingStars(rating: number): string {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    let stars = '★'.repeat(fullStars);
    if (hasHalfStar) stars += '☆';
    return stars.padEnd(5, '☆');
  }
  
  function getComplexityColor(complexity: string): string {
    switch (complexity) {
      case 'beginner': return '#10b981';
      case 'intermediate': return '#f59e0b';
      case 'advanced': return '#ef4444';
      default: return '#6b7280';
    }
  }
  
  function getComplexityIcon(complexity: string): string {
    switch (complexity) {
      case 'beginner': return '🟢';
      case 'intermediate': return '🟡';
      case 'advanced': return '🔴';
      default: return '⚪';
    }
  }
  
  onMount(() => {
    // Load custom templates from storage
    const saved = localStorage.getItem('customAgentTemplates');
    if (saved) {
      try {
        customTemplates = JSON.parse(saved);
        templates = [...builtInTemplates, ...customTemplates];
      } catch (e) {
        console.warn('Failed to load custom templates:', e);
      }
    }
  });
</script>

<div class="agent-template-library {layout}">
  <!-- Header -->
  <div class="library-header">
    <div class="header-title">
      <h2>Agent Template Library</h2>
      <p class="header-subtitle">Choose from pre-built agent templates or create your own</p>
    </div>
    
    {#if searchable}
      <div class="search-container">
        <input
          type="text"
          placeholder="Search templates..."
          bind:value={searchQuery}
          class="search-input"
        />
        <div class="search-icon">🔍</div>
      </div>
    {/if}
  </div>
  
  <!-- Categories and Filters -->
  {#if showCategories}
    <div class="categories-bar">
      <div class="categories-list">
        {#each categories as category}
          <button
            type="button"
            class="category-btn"
            class:active={selectedCategory === category}
            on:click={() => selectedCategory = category}
          >
            {category}
            {#if category !== 'All'}
              <span class="category-count">
                {templates.filter(t => t.category === category).length}
              </span>
            {/if}
          </button>
        {/each}
      </div>
      
      <div class="filters-info">
        <span class="results-count">{filteredTemplates.length} templates</span>
      </div>
    </div>
  {/if}
  
  <!-- Templates Grid/List -->
  <div class="templates-container">
    {#if layout === 'grid'}
      <div class="templates-grid">
        {#each filteredTemplates as template (template.id)}
          <div class="template-card" on:click={() => selectTemplate(template)}>
            <div class="card-header">
              <div class="template-info">
                <h3 class="template-name">{template.name}</h3>
                <span class="template-category">{template.category}</span>
              </div>
              <div class="template-meta">
                <span class="complexity-badge" style="color: {getComplexityColor(template.complexity)}">
                  {getComplexityIcon(template.complexity)} {template.complexity}
                </span>
              </div>
            </div>
            
            <div class="card-content">
              <p class="template-description">{template.description}</p>
              
              <div class="template-details">
                <div class="detail-item">
                  <span class="detail-label">Role:</span>
                  <span class="detail-value">{template.role}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">Model:</span>
                  <span class="detail-value">{template.model}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">Setup:</span>
                  <span class="detail-value">{template.estimatedSetupTime}</span>
                </div>
              </div>
              
              <div class="capabilities-preview">
                {#each template.capabilities.slice(0, 4) as capability}
                  <span class="capability-tag">{capability}</span>
                {/each}
                {#if template.capabilities.length > 4}
                  <span class="more-capabilities">+{template.capabilities.length - 4}</span>
                {/if}
              </div>
            </div>
            
            <div class="card-footer">
              <div class="rating-info">
                <span class="rating-stars">{getRatingStars(template.rating)}</span>
                <span class="rating-value">({template.rating})</span>
              </div>
              <div class="download-count">
                {template.downloads} downloads
              </div>
            </div>
          </div>
        {/each}
      </div>
      
    {:else if layout === 'list'}
      <div class="templates-list">
        {#each filteredTemplates as template (template.id)}
          <div class="template-list-item" on:click={() => selectTemplate(template)}>
            <div class="list-item-header">
              <div class="template-title">
                <h3>{template.name}</h3>
                <span class="template-category">{template.category}</span>
                <span class="complexity-badge" style="color: {getComplexityColor(template.complexity)}">
                  {getComplexityIcon(template.complexity)} {template.complexity}
                </span>
              </div>
              <div class="template-stats">
                <span class="rating">{getRatingStars(template.rating)} ({template.rating})</span>
                <span class="downloads">{template.downloads} downloads</span>
              </div>
            </div>
            
            <div class="list-item-content">
              <p class="template-description">{template.description}</p>
              <div class="template-tags">
                {#each template.tags as tag}
                  <span class="tag">{tag}</span>
                {/each}
              </div>
            </div>
          </div>
        {/each}
      </div>
      
    {:else if layout === 'compact'}
      <div class="templates-compact">
        {#each filteredTemplates as template (template.id)}
          <button
            type="button"
            class="template-compact-item"
            on:click={() => selectTemplate(template)}
          >
            <div class="compact-header">
              <span class="template-name">{template.name}</span>
              <span class="template-rating">{template.rating}★</span>
            </div>
            <div class="compact-details">
              <span class="template-category">{template.category}</span>
              <span class="template-complexity">{template.complexity}</span>
            </div>
          </button>
        {/each}
      </div>
    {/if}
    
    {#if filteredTemplates.length === 0}
      <div class="no-templates">
        <div class="no-templates-icon">📚</div>
        <h3>No templates found</h3>
        <p>Try adjusting your search or category filters</p>
      </div>
    {/if}
  </div>
</div>

<!-- Template Preview Modal -->
{#if showTemplateModal && selectedTemplate}
  <div class="modal-overlay" on:click={closeModal}>
    <div class="modal-content" on:click|stopPropagation>
      <div class="modal-header">
        <div class="modal-title">
          <h2>{selectedTemplate.name}</h2>
          <div class="template-badges">
            <span class="category-badge">{selectedTemplate.category}</span>
            <span class="complexity-badge" style="color: {getComplexityColor(selectedTemplate.complexity)}">
              {getComplexityIcon(selectedTemplate.complexity)} {selectedTemplate.complexity}
            </span>
          </div>
        </div>
        <button type="button" class="close-btn" on:click={closeModal}>×</button>
      </div>
      
      <div class="modal-body">
        <div class="template-overview">
          <p class="template-description">{selectedTemplate.description}</p>
          
          <div class="template-stats">
            <div class="stat-item">
              <span class="stat-label">Rating:</span>
              <span class="stat-value">{getRatingStars(selectedTemplate.rating)} ({selectedTemplate.rating})</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Downloads:</span>
              <span class="stat-value">{selectedTemplate.downloads}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Author:</span>
              <span class="stat-value">{selectedTemplate.author}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Version:</span>
              <span class="stat-value">{selectedTemplate.version}</span>
            </div>
          </div>
        </div>
        
        <div class="template-sections">
          <!-- Configuration -->
          <div class="section">
            <h4>Configuration</h4>
            <div class="config-grid">
              <div class="config-item">
                <span class="config-label">Role:</span>
                <span class="config-value">{selectedTemplate.role}</span>
              </div>
              <div class="config-item">
                <span class="config-label">Model:</span>
                <span class="config-value">{selectedTemplate.model}</span>
              </div>
              <div class="config-item">
                <span class="config-label">Setup Time:</span>
                <span class="config-value">{selectedTemplate.estimatedSetupTime}</span>
              </div>
            </div>
          </div>
          
          <!-- Prompt Preview -->
          <div class="section">
            <h4>Prompt Preview</h4>
            <div class="prompt-preview">
              <pre>{selectedTemplate.prompt.substring(0, 500)}{selectedTemplate.prompt.length > 500 ? '...' : ''}</pre>
            </div>
          </div>
          
          <!-- Capabilities -->
          <div class="section">
            <h4>Capabilities ({selectedTemplate.capabilities.length})</h4>
            <div class="capabilities-grid">
              {#each selectedTemplate.capabilities as capability}
                <span class="capability-chip">{capability}</span>
              {/each}
            </div>
          </div>
          
          <!-- Use Cases -->
          <div class="section">
            <h4>Use Cases</h4>
            <ul class="use-cases-list">
              {#each selectedTemplate.useCases as useCase}
                <li>{useCase}</li>
              {/each}
            </ul>
          </div>
          
          <!-- Preview Example -->
          {#if selectedTemplate.preview}
            <div class="section">
              <h4>Example</h4>
              <div class="example-preview">
                <div class="example-input">
                  <strong>Input:</strong>
                  <p>{selectedTemplate.preview.exampleInput}</p>
                </div>
                <div class="example-output">
                  <strong>Output:</strong>
                  <p>{selectedTemplate.preview.exampleOutput}</p>
                </div>
              </div>
            </div>
          {/if}
        </div>
      </div>
      
      <div class="modal-actions">
        <button type="button" class="btn-secondary" on:click={closeModal}>
          Cancel
        </button>
        {#if allowCustomTemplates && selectedTemplate.isBuiltIn}
          <button 
            type="button" 
            class="btn-secondary"
            on:click={() => saveAsCustomTemplate(selectedTemplate)}
          >
            Save as Custom
          </button>
        {/if}
        <button 
          type="button" 
          class="btn-primary"
          on:click={() => useTemplate(selectedTemplate)}
        >
          Use Template
        </button>
      </div>
    </div>
  </div>
{/if}

<style>
  .agent-template-library {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #1f2937;
    border: 1px solid #374151;
    border-radius: 12px;
    overflow: hidden;
  }
  
  .library-header {
    padding: 24px;
    border-bottom: 1px solid #374151;
    background: #111827;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 24px;
  }
  
  .header-title h2 {
    margin: 0 0 4px 0;
    color: #ffffff;
    font-size: 20px;
    font-weight: 600;
  }
  
  .header-subtitle {
    margin: 0;
    color: #9ca3af;
    font-size: 14px;
  }
  
  .search-container {
    position: relative;
    min-width: 300px;
  }
  
  .search-input {
    width: 100%;
    padding: 10px 40px 10px 12px;
    background: #374151;
    border: 1px solid #4b5563;
    border-radius: 8px;
    color: #ffffff;
    font-size: 14px;
    outline: none;
  }
  
  .search-input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
  
  .search-input::placeholder {
    color: #9ca3af;
  }
  
  .search-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
  }
  
  .categories-bar {
    padding: 16px 24px;
    border-bottom: 1px solid #374151;
    background: #111827;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .categories-list {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }
  
  .category-btn {
    padding: 6px 12px;
    background: #374151;
    border: 1px solid #4b5563;
    border-radius: 6px;
    color: #d1d5db;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 6px;
  }
  
  .category-btn:hover {
    background: #4b5563;
  }
  
  .category-btn.active {
    background: #3b82f6;
    border-color: #3b82f6;
    color: #ffffff;
  }
  
  .category-count {
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
  }
  
  .filters-info {
    color: #9ca3af;
    font-size: 14px;
  }
  
  .templates-container {
    padding: 24px;
    max-height: 600px;
    overflow-y: auto;
  }
  
  /* Grid Layout */
  .templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
  }
  
  .template-card {
    background: #111827;
    border: 1px solid #374151;
    border-radius: 8px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .template-card:hover {
    border-color: #4b5563;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
  }
  
  .template-name {
    margin: 0 0 4px 0;
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
  }
  
  .template-category {
    color: #9ca3af;
    font-size: 12px;
    background: #374151;
    padding: 2px 6px;
    border-radius: 3px;
  }
  
  .complexity-badge {
    font-size: 12px;
    font-weight: 500;
  }
  
  .template-description {
    color: #d1d5db;
    font-size: 14px;
    line-height: 1.5;
    margin: 0 0 16px 0;
  }
  
  .template-details {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    margin-bottom: 16px;
  }
  
  .detail-item {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }
  
  .detail-label {
    color: #9ca3af;
    font-size: 11px;
    font-weight: 500;
  }
  
  .detail-value {
    color: #ffffff;
    font-size: 12px;
  }
  
  .capabilities-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-bottom: 16px;
  }
  
  .capability-tag {
    padding: 2px 6px;
    background: #374151;
    color: #d1d5db;
    border-radius: 3px;
    font-size: 11px;
  }
  
  .more-capabilities {
    padding: 2px 6px;
    background: #4b5563;
    color: #9ca3af;
    border-radius: 3px;
    font-size: 11px;
  }
  
  .card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 12px;
    border-top: 1px solid #374151;
  }
  
  .rating-info {
    display: flex;
    align-items: center;
    gap: 4px;
  }
  
  .rating-stars {
    color: #fbbf24;
    font-size: 12px;
  }
  
  .rating-value {
    color: #9ca3af;
    font-size: 12px;
  }
  
  .download-count {
    color: #9ca3af;
    font-size: 12px;
  }
  
  /* List Layout */
  .templates-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .template-list-item {
    background: #111827;
    border: 1px solid #374151;
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .template-list-item:hover {
    border-color: #4b5563;
  }
  
  .list-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }
  
  .template-title {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .template-title h3 {
    margin: 0;
    color: #ffffff;
    font-size: 16px;
  }
  
  .template-stats {
    display: flex;
    gap: 16px;
    font-size: 12px;
    color: #9ca3af;
  }
  
  .list-item-content {
    margin-left: 0;
  }
  
  .template-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 8px;
  }
  
  .tag {
    padding: 2px 6px;
    background: #374151;
    color: #d1d5db;
    border-radius: 3px;
    font-size: 11px;
  }
  
  /* Compact Layout */
  .templates-compact {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 8px;
  }
  
  .template-compact-item {
    background: #111827;
    border: 1px solid #374151;
    border-radius: 6px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.2s;
    text-align: left;
  }
  
  .template-compact-item:hover {
    border-color: #4b5563;
  }
  
  .compact-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
  }
  
  .compact-header .template-name {
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
  }
  
  .template-rating {
    color: #fbbf24;
    font-size: 12px;
  }
  
  .compact-details {
    display: flex;
    gap: 8px;
    font-size: 12px;
    color: #9ca3af;
  }
  
  /* No Templates */
  .no-templates {
    text-align: center;
    padding: 60px 20px;
    color: #9ca3af;
  }
  
  .no-templates-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  .no-templates h3 {
    margin: 0 0 8px 0;
    color: #d1d5db;
  }
  
  /* Modal */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
  }
  
  .modal-content {
    background: #1f2937;
    border: 1px solid #374151;
    border-radius: 12px;
    max-width: 900px;
    max-height: 90vh;
    width: 90vw;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 24px;
    border-bottom: 1px solid #374151;
  }
  
  .modal-title h2 {
    margin: 0 0 8px 0;
    color: #ffffff;
    font-size: 20px;
  }
  
  .template-badges {
    display: flex;
    gap: 8px;
  }
  
  .category-badge {
    padding: 4px 8px;
    background: #3b82f6;
    color: white;
    border-radius: 4px;
    font-size: 12px;
  }
  
  .close-btn {
    background: none;
    border: none;
    color: #9ca3af;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
  }
  
  .close-btn:hover {
    color: #ffffff;
  }
  
  .modal-body {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
  }
  
  .template-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
    margin: 16px 0;
  }
  
  .stat-item {
    display: flex;
    justify-content: space-between;
  }
  
  .stat-label {
    color: #9ca3af;
    font-size: 14px;
  }
  
  .stat-value {
    color: #ffffff;
    font-size: 14px;
  }
  
  .template-sections {
    margin-top: 24px;
  }
  
  .section {
    margin-bottom: 24px;
  }
  
  .section h4 {
    margin: 0 0 12px 0;
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
  }
  
  .config-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }
  
  .config-item {
    display: flex;
    justify-content: space-between;
  }
  
  .config-label {
    color: #9ca3af;
    font-size: 14px;
  }
  
  .config-value {
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
  }
  
  .prompt-preview {
    background: #111827;
    border: 1px solid #374151;
    border-radius: 6px;
    padding: 16px;
  }
  
  .prompt-preview pre {
    color: #d1d5db;
    font-size: 13px;
    line-height: 1.5;
    margin: 0;
    white-space: pre-wrap;
    font-family: 'Consolas', 'Monaco', monospace;
  }
  
  .capabilities-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
  }
  
  .capability-chip {
    padding: 4px 8px;
    background: #374151;
    color: #d1d5db;
    border-radius: 4px;
    font-size: 12px;
  }
  
  .use-cases-list {
    margin: 0;
    padding-left: 20px;
    color: #d1d5db;
  }
  
  .use-cases-list li {
    margin-bottom: 4px;
  }
  
  .example-preview {
    background: #111827;
    border: 1px solid #374151;
    border-radius: 6px;
    padding: 16px;
  }
  
  .example-input, .example-output {
    margin-bottom: 12px;
  }
  
  .example-input strong, .example-output strong {
    color: #ffffff;
    display: block;
    margin-bottom: 4px;
  }
  
  .example-input p, .example-output p {
    color: #d1d5db;
    font-size: 13px;
    margin: 0;
    font-family: 'Consolas', 'Monaco', monospace;
  }
  
  .modal-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    padding: 20px 24px;
    border-top: 1px solid #374151;
  }
  
  .btn-secondary {
    padding: 10px 20px;
    background: #374151;
    border: 1px solid #4b5563;
    border-radius: 6px;
    color: #d1d5db;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .btn-secondary:hover {
    background: #4b5563;
  }
  
  .btn-primary {
    padding: 10px 20px;
    background: #3b82f6;
    border: 1px solid #3b82f6;
    border-radius: 6px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .btn-primary:hover {
    background: #2563eb;
  }
</style>