<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  
  const dispatch = createEventDispatcher();
  
  export let agentData: AgentConfig;
  export let showActions: boolean = true;
  export let showFullDetails: boolean = false;
  export let variant: 'card' | 'modal' | 'sidebar' | 'inline' = 'card';
  export let size: 'small' | 'medium' | 'large' = 'medium';
  export let allowEdit: boolean = true;
  export let allowTest: boolean = true;
  export let isCreating: boolean = false;
  
  interface AgentConfig {
    id?: string;
    name: string;
    role: string;
    model: string;
    prompt: string;
    capabilities: string[];
    team?: string;
    status?: 'active' | 'inactive' | 'draft';
    created?: string;
    lastModified?: string;
    usage?: {
      totalRequests: number;
      successRate: number;
      avgResponseTime: number;
    };
    metadata?: {
      version: string;
      tags: string[];
      description: string;
      author: string;
    };
  }
  
  // Role configurations for display
  const roleConfigs = {
    coder: { 
      color: '#10b981', 
      icon: '💻',
      description: 'Specialized in code generation, debugging, and software development'
    },
    debugger: { 
      color: '#f59e0b', 
      icon: '🔍',
      description: 'Expert at finding and fixing bugs, analyzing code issues'
    },
    pm: { 
      color: '#3b82f6', 
      icon: '📋',
      description: 'Project management, planning, and team coordination'
    },
    ux: { 
      color: '#8b5cf6', 
      icon: '🎨',
      description: 'User experience design, interface optimization, and usability'
    },
    qa: { 
      color: '#ef4444', 
      icon: '✅',
      description: 'Quality assurance, testing strategies, and validation'
    },
    analyst: { 
      color: '#06b6d4', 
      icon: '📊',
      description: 'Data analysis, insights generation, and reporting'
    },
    researcher: { 
      color: '#84cc16', 
      icon: '🔬',
      description: 'Research, information gathering, and analysis'
    },
    writer: { 
      color: '#f97316', 
      icon: '✍️',
      description: 'Content creation, documentation, and communication'
    }
  };
  
  // Model configurations
  const modelConfigs = {
    'gpt-4': { provider: 'OpenAI', type: 'Advanced', color: '#10b981' },
    'gpt-3.5-turbo': { provider: 'OpenAI', type: 'Standard', color: '#3b82f6' },
    'claude-3-opus': { provider: 'Anthropic', type: 'Advanced', color: '#8b5cf6' },
    'claude-3-sonnet': { provider: 'Anthropic', type: 'Balanced', color: '#06b6d4' },
    'claude-3-haiku': { provider: 'Anthropic', type: 'Fast', color: '#84cc16' },
    'gemini-pro': { provider: 'Google', type: 'Advanced', color: '#f59e0b' },
    'gemini-pro-vision': { provider: 'Google', type: 'Multimodal', color: '#ef4444' }
  };
  
  $: roleConfig = roleConfigs[agentData.role.toLowerCase()] || roleConfigs.coder;
  $: modelConfig = modelConfigs[agentData.model] || { provider: 'Unknown', type: 'Standard', color: '#6b7280' };
  
  $: sizeClasses = {
    small: 'text-sm',
    medium: 'text-base', 
    large: 'text-lg'
  }[size];
  
  let showFullPrompt = false;
  let testResults: any = null;
  let isTestingAgent = false;
  
  function handleEdit() {
    dispatch('edit', { agent: agentData });
  }
  
  function handleTest() {
    isTestingAgent = true;
    
    // Simulate agent testing
    setTimeout(() => {
      testResults = {
        status: 'success',
        responseTime: Math.round(Math.random() * 2000 + 500),
        output: `Hello! I'm ${agentData.name}, a ${agentData.role} agent powered by ${agentData.model}. I'm ready to help you with tasks related to my expertise.`,
        timestamp: new Date().toISOString()
      };
      isTestingAgent = false;
      dispatch('tested', { agent: agentData, results: testResults });
    }, 2000);
  }
  
  function handleDeploy() {
    dispatch('deploy', { agent: agentData });
  }
  
  function handleSave() {
    dispatch('save', { agent: agentData });
  }
  
  function formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
  
  function truncateText(text: string, maxLength: number): string {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  }
  
  function getCapabilityColor(capability: string): string {
    let hash = 0;
    for (let i = 0; i < capability.length; i++) {
      hash = capability.charCodeAt(i) + ((hash << 5) - hash);
    }
    const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];
    return colors[Math.abs(hash) % colors.length];
  }
  
  function calculateCompleteness(): number {
    let score = 0;
    if (agentData.name?.trim()) score += 20;
    if (agentData.role) score += 20;
    if (agentData.model) score += 20;
    if (agentData.prompt?.trim()) score += 25;
    if (agentData.capabilities?.length > 0) score += 15;
    return score;
  }
  
  $: completeness = calculateCompleteness();
</script>

<div class="agent-preview {variant} {sizeClasses}">
  {#if variant === 'card'}
    <!-- Card Layout -->
    <div class="preview-card">
      <!-- Header -->
      <div class="card-header">
        <div class="agent-title">
          <div class="role-indicator" style="background-color: {roleConfig.color}">
            {roleConfig.icon}
          </div>
          <div class="title-info">
            <h3 class="agent-name">{agentData.name || 'Unnamed Agent'}</h3>
            <div class="agent-subtitle">
              <span class="role-badge" style="background-color: {roleConfig.color}">
                {agentData.role || 'No Role'}
              </span>
              <span class="model-badge" style="background-color: {modelConfig.color}">
                {modelConfig.provider} {modelConfig.type}
              </span>
            </div>
          </div>
        </div>
        
        {#if isCreating}
          <div class="completeness-indicator">
            <div class="completeness-bar">
              <div 
                class="completeness-fill" 
                style="width: {completeness}%; background-color: {completeness > 80 ? '#10b981' : completeness > 50 ? '#f59e0b' : '#ef4444'}"
              ></div>
            </div>
            <span class="completeness-text">{completeness}% Complete</span>
          </div>
        {/if}
      </div>
      
      <!-- Content -->
      <div class="card-content">
        <!-- Role Description -->
        <div class="role-description">
          <p>{roleConfig.description}</p>
        </div>
        
        <!-- Model Info -->
        <div class="model-info">
          <h4>Model Configuration</h4>
          <div class="model-details">
            <span class="model-name">{agentData.model || 'No model selected'}</span>
            {#if modelConfig.provider !== 'Unknown'}
              <span class="model-provider">by {modelConfig.provider}</span>
            {/if}
          </div>
        </div>
        
        <!-- Prompt Preview -->
        {#if agentData.prompt}
          <div class="prompt-preview">
            <h4>Prompt</h4>
            <div class="prompt-content">
              <p class="prompt-text">
                {showFullPrompt ? agentData.prompt : truncateText(agentData.prompt, 150)}
              </p>
              {#if agentData.prompt.length > 150}
                <button 
                  type="button" 
                  class="toggle-prompt-btn"
                  on:click={() => showFullPrompt = !showFullPrompt}
                >
                  {showFullPrompt ? 'Show Less' : 'Show More'}
                </button>
              {/if}
            </div>
          </div>
        {/if}
        
        <!-- Capabilities -->
        {#if agentData.capabilities?.length > 0}
          <div class="capabilities-section">
            <h4>Capabilities ({agentData.capabilities.length})</h4>
            <div class="capabilities-list">
              {#each agentData.capabilities.slice(0, showFullDetails ? undefined : 6) as capability}
                <span 
                  class="capability-tag"
                  style="background-color: {getCapabilityColor(capability)}"
                >
                  {capability}
                </span>
              {/each}
              {#if !showFullDetails && agentData.capabilities.length > 6}
                <span class="more-capabilities">
                  +{agentData.capabilities.length - 6} more
                </span>
              {/if}
            </div>
          </div>
        {/if}
        
        <!-- Team Assignment -->
        {#if agentData.team}
          <div class="team-section">
            <h4>Team Assignment</h4>
            <div class="team-info">
              <span class="team-name">{agentData.team}</span>
            </div>
          </div>
        {/if}
        
        <!-- Usage Stats (if available) -->
        {#if agentData.usage && !isCreating}
          <div class="usage-stats">
            <h4>Performance</h4>
            <div class="stats-grid">
              <div class="stat-item">
                <span class="stat-value">{agentData.usage.totalRequests}</span>
                <span class="stat-label">Requests</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{agentData.usage.successRate}%</span>
                <span class="stat-label">Success Rate</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{agentData.usage.avgResponseTime}ms</span>
                <span class="stat-label">Avg Response</span>
              </div>
            </div>
          </div>
        {/if}
        
        <!-- Test Results -->
        {#if testResults}
          <div class="test-results">
            <h4>Test Results</h4>
            <div class="test-output">
              <div class="test-status {testResults.status}">
                {testResults.status === 'success' ? '✅' : '❌'} 
                {testResults.status === 'success' ? 'Test Passed' : 'Test Failed'}
                <span class="response-time">({testResults.responseTime}ms)</span>
              </div>
              <div class="test-message">
                {testResults.output}
              </div>
              <div class="test-timestamp">
                Tested at {formatDate(testResults.timestamp)}
              </div>
            </div>
          </div>
        {/if}
        
        <!-- Metadata (if in full details mode) -->
        {#if showFullDetails && agentData.metadata}
          <div class="metadata-section">
            <h4>Metadata</h4>
            <div class="metadata-grid">
              <div class="metadata-item">
                <span class="metadata-label">Version:</span>
                <span class="metadata-value">{agentData.metadata.version}</span>
              </div>
              <div class="metadata-item">
                <span class="metadata-label">Author:</span>
                <span class="metadata-value">{agentData.metadata.author}</span>
              </div>
              <div class="metadata-item">
                <span class="metadata-label">Description:</span>
                <span class="metadata-value">{agentData.metadata.description}</span>
              </div>
            </div>
          </div>
        {/if}
      </div>
      
      <!-- Actions -->
      {#if showActions}
        <div class="card-actions">
          {#if isCreating}
            <button 
              type="button" 
              class="action-btn primary"
              on:click={handleSave}
              disabled={completeness < 80}
            >
              {completeness >= 80 ? 'Save Agent' : `Complete Setup (${completeness}%)`}
            </button>
          {:else}
            <button 
              type="button" 
              class="action-btn secondary"
              on:click={handleTest}
              disabled={isTestingAgent || !allowTest}
            >
              {isTestingAgent ? 'Testing...' : 'Test Agent'}
            </button>
            
            {#if allowEdit}
              <button 
                type="button" 
                class="action-btn secondary"
                on:click={handleEdit}
              >
                Edit
              </button>
            {/if}
            
            <button 
              type="button" 
              class="action-btn primary"
              on:click={handleDeploy}
            >
              Deploy
            </button>
          {/if}
        </div>
      {/if}
    </div>
    
  {:else if variant === 'modal'}
    <!-- Modal Layout -->
    <div class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h2>{agentData.name || 'Agent Preview'}</h2>
          <button type="button" class="close-btn" on:click={() => dispatch('close')}>
            ×
          </button>
        </div>
        
        <div class="modal-body">
          <!-- Full agent details with all sections expanded -->
          <div class="full-preview">
            <!-- Role and Model Section -->
            <div class="preview-section">
              <h3>Configuration</h3>
              <div class="config-grid">
                <div class="config-item">
                  <label>Role:</label>
                  <div class="role-display">
                    <span class="role-icon">{roleConfig.icon}</span>
                    <span>{agentData.role}</span>
                  </div>
                </div>
                <div class="config-item">
                  <label>Model:</label>
                  <span>{agentData.model} ({modelConfig.provider})</span>
                </div>
                <div class="config-item">
                  <label>Team:</label>
                  <span>{agentData.team || 'Unassigned'}</span>
                </div>
                <div class="config-item">
                  <label>Status:</label>
                  <span class="status-badge {agentData.status || 'draft'}">
                    {agentData.status || 'draft'}
                  </span>
                </div>
              </div>
            </div>
            
            <!-- Prompt Section -->
            <div class="preview-section">
              <h3>Prompt</h3>
              <div class="prompt-display">
                <pre>{agentData.prompt || 'No prompt defined'}</pre>
              </div>
            </div>
            
            <!-- Capabilities Section -->
            <div class="preview-section">
              <h3>Capabilities</h3>
              <div class="capabilities-grid">
                {#each agentData.capabilities || [] as capability}
                  <span 
                    class="capability-chip"
                    style="background-color: {getCapabilityColor(capability)}"
                  >
                    {capability}
                  </span>
                {/each}
              </div>
            </div>
          </div>
        </div>
        
        {#if showActions}
          <div class="modal-actions">
            <button type="button" class="action-btn secondary" on:click={() => dispatch('close')}>
              Close
            </button>
            {#if allowEdit}
              <button type="button" class="action-btn secondary" on:click={handleEdit}>
                Edit
              </button>
            {/if}
            <button type="button" class="action-btn primary" on:click={handleTest}>
              Test Agent
            </button>
          </div>
        {/if}
      </div>
    </div>
    
  {:else if variant === 'sidebar'}
    <!-- Sidebar Layout -->
    <div class="sidebar-preview">
      <div class="sidebar-header">
        <h3>{agentData.name || 'Agent Preview'}</h3>
      </div>
      
      <div class="sidebar-content">
        <!-- Compact sections for sidebar -->
        <div class="sidebar-section">
          <h4>Role</h4>
          <div class="role-compact">
            <span class="role-icon">{roleConfig.icon}</span>
            <span>{agentData.role}</span>
          </div>
        </div>
        
        <div class="sidebar-section">
          <h4>Model</h4>
          <span>{agentData.model}</span>
        </div>
        
        <div class="sidebar-section">
          <h4>Capabilities</h4>
          <div class="capabilities-compact">
            {#each (agentData.capabilities || []).slice(0, 4) as capability}
              <span class="capability-mini">{capability}</span>
            {/each}
            {#if (agentData.capabilities || []).length > 4}
              <span class="more-mini">+{(agentData.capabilities || []).length - 4}</span>
            {/if}
          </div>
        </div>
      </div>
      
      {#if showActions}
        <div class="sidebar-actions">
          <button type="button" class="action-btn secondary small" on:click={handleTest}>
            Test
          </button>
          {#if allowEdit}
            <button type="button" class="action-btn secondary small" on:click={handleEdit}>
              Edit
            </button>
          {/if}
        </div>
      {/if}
    </div>
    
  {:else if variant === 'inline'}
    <!-- Inline Layout -->
    <div class="inline-preview">
      <div class="inline-header">
        <div class="inline-title">
          <span class="role-icon">{roleConfig.icon}</span>
          <span class="agent-name">{agentData.name}</span>
          <span class="role-badge small" style="background-color: {roleConfig.color}">
            {agentData.role}
          </span>
        </div>
        {#if showActions}
          <div class="inline-actions">
            <button type="button" class="action-btn mini" on:click={handleTest}>Test</button>
            {#if allowEdit}
              <button type="button" class="action-btn mini" on:click={handleEdit}>Edit</button>
            {/if}
          </div>
        {/if}
      </div>
      
      <div class="inline-content">
        <div class="inline-details">
          <span class="model-info">{agentData.model}</span>
          <span class="capability-count">{(agentData.capabilities || []).length} capabilities</span>
          {#if agentData.team}
            <span class="team-info">Team: {agentData.team}</span>
          {/if}
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  .agent-preview {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
  
  /* Card Layout */
  .preview-card {
    background: #1f2937;
    border: 1px solid #374151;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.2s;
  }
  
  .preview-card:hover {
    border-color: #4b5563;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  .card-header {
    padding: 20px;
    border-bottom: 1px solid #374151;
    background: #111827;
  }
  
  .agent-title {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 12px;
  }
  
  .role-indicator {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    flex-shrink: 0;
  }
  
  .title-info {
    flex: 1;
  }
  
  .agent-name {
    margin: 0 0 8px 0;
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
  }
  
  .agent-subtitle {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }
  
  .role-badge, .model-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    color: white;
  }
  
  .role-badge.small {
    padding: 2px 6px;
    font-size: 11px;
  }
  
  .completeness-indicator {
    margin-top: 12px;
  }
  
  .completeness-bar {
    height: 6px;
    background: #374151;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 4px;
  }
  
  .completeness-fill {
    height: 100%;
    transition: width 0.3s ease;
  }
  
  .completeness-text {
    font-size: 12px;
    color: #9ca3af;
  }
  
  .card-content {
    padding: 20px;
  }
  
  .card-content > div {
    margin-bottom: 16px;
  }
  
  .card-content > div:last-child {
    margin-bottom: 0;
  }
  
  .card-content h4 {
    margin: 0 0 8px 0;
    color: #d1d5db;
    font-size: 14px;
    font-weight: 600;
  }
  
  .role-description p {
    color: #9ca3af;
    font-size: 14px;
    line-height: 1.5;
    margin: 0;
  }
  
  .model-details {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .model-name {
    color: #ffffff;
    font-weight: 500;
  }
  
  .model-provider {
    color: #9ca3af;
    font-size: 13px;
  }
  
  .prompt-content {
    background: #111827;
    border: 1px solid #374151;
    border-radius: 6px;
    padding: 12px;
  }
  
  .prompt-text {
    color: #d1d5db;
    font-size: 13px;
    line-height: 1.5;
    margin: 0 0 8px 0;
    white-space: pre-wrap;
  }
  
  .toggle-prompt-btn {
    background: none;
    border: none;
    color: #3b82f6;
    font-size: 12px;
    cursor: pointer;
    padding: 0;
  }
  
  .toggle-prompt-btn:hover {
    text-decoration: underline;
  }
  
  .capabilities-list {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
  }
  
  .capability-tag {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    color: white;
    font-weight: 500;
  }
  
  .more-capabilities {
    padding: 4px 8px;
    background: #374151;
    color: #9ca3af;
    border-radius: 4px;
    font-size: 12px;
  }
  
  .team-info {
    color: #d1d5db;
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }
  
  .stat-item {
    text-align: center;
    padding: 8px;
    background: #111827;
    border-radius: 6px;
  }
  
  .stat-value {
    display: block;
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
  }
  
  .stat-label {
    display: block;
    color: #9ca3af;
    font-size: 12px;
    margin-top: 2px;
  }
  
  .test-results {
    background: #111827;
    border: 1px solid #374151;
    border-radius: 6px;
    padding: 12px;
  }
  
  .test-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    margin-bottom: 8px;
  }
  
  .test-status.success {
    color: #10b981;
  }
  
  .test-status.failure {
    color: #ef4444;
  }
  
  .response-time {
    color: #9ca3af;
    font-size: 12px;
    font-weight: normal;
  }
  
  .test-message {
    color: #d1d5db;
    font-size: 13px;
    line-height: 1.5;
    margin-bottom: 8px;
  }
  
  .test-timestamp {
    color: #6b7280;
    font-size: 11px;
  }
  
  .card-actions {
    padding: 16px 20px;
    border-top: 1px solid #374151;
    background: #111827;
    display: flex;
    gap: 8px;
    justify-content: flex-end;
  }
  
  .action-btn {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    border: 1px solid transparent;
  }
  
  .action-btn.primary {
    background: #3b82f6;
    color: white;
  }
  
  .action-btn.primary:hover {
    background: #2563eb;
  }
  
  .action-btn.primary:disabled {
    background: #374151;
    color: #6b7280;
    cursor: not-allowed;
  }
  
  .action-btn.secondary {
    background: #374151;
    color: #d1d5db;
    border-color: #4b5563;
  }
  
  .action-btn.secondary:hover {
    background: #4b5563;
  }
  
  .action-btn.small {
    padding: 6px 12px;
    font-size: 13px;
  }
  
  .action-btn.mini {
    padding: 4px 8px;
    font-size: 12px;
  }
  
  /* Modal Layout */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
  }
  
  .modal-content {
    background: #1f2937;
    border: 1px solid #374151;
    border-radius: 12px;
    max-width: 800px;
    max-height: 90vh;
    width: 90vw;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #374151;
  }
  
  .modal-header h2 {
    margin: 0;
    color: #ffffff;
  }
  
  .close-btn {
    background: none;
    border: none;
    color: #9ca3af;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
  }
  
  .close-btn:hover {
    color: #ffffff;
  }
  
  .modal-body {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
  }
  
  .preview-section {
    margin-bottom: 24px;
  }
  
  .preview-section h3 {
    margin: 0 0 12px 0;
    color: #ffffff;
    font-size: 16px;
  }
  
  .config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
  }
  
  .config-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }
  
  .config-item label {
    color: #9ca3af;
    font-size: 12px;
    font-weight: 500;
  }
  
  .role-display {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #d1d5db;
  }
  
  .role-icon {
    font-size: 16px;
  }
  
  .status-badge {
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
  }
  
  .status-badge.active {
    background: #10b981;
    color: white;
  }
  
  .status-badge.draft {
    background: #f59e0b;
    color: white;
  }
  
  .status-badge.inactive {
    background: #6b7280;
    color: white;
  }
  
  .prompt-display {
    background: #111827;
    border: 1px solid #374151;
    border-radius: 6px;
    padding: 16px;
  }
  
  .prompt-display pre {
    color: #d1d5db;
    font-size: 13px;
    line-height: 1.5;
    margin: 0;
    white-space: pre-wrap;
    font-family: 'Consolas', 'Monaco', monospace;
  }
  
  .capabilities-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .capability-chip {
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 13px;
    color: white;
    font-weight: 500;
  }
  
  .modal-actions {
    padding: 16px 20px;
    border-top: 1px solid #374151;
    display: flex;
    gap: 8px;
    justify-content: flex-end;
  }
  
  /* Sidebar Layout */
  .sidebar-preview {
    background: #1f2937;
    border: 1px solid #374151;
    border-radius: 8px;
    padding: 16px;
    width: 280px;
  }
  
  .sidebar-header h3 {
    margin: 0 0 16px 0;
    color: #ffffff;
    font-size: 16px;
  }
  
  .sidebar-section {
    margin-bottom: 12px;
  }
  
  .sidebar-section h4 {
    margin: 0 0 6px 0;
    color: #9ca3af;
    font-size: 12px;
    font-weight: 500;
  }
  
  .role-compact {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #d1d5db;
  }
  
  .capabilities-compact {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }
  
  .capability-mini {
    padding: 2px 6px;
    background: #374151;
    color: #d1d5db;
    border-radius: 3px;
    font-size: 11px;
  }
  
  .more-mini {
    padding: 2px 6px;
    background: #4b5563;
    color: #9ca3af;
    border-radius: 3px;
    font-size: 11px;
  }
  
  .sidebar-actions {
    margin-top: 16px;
    display: flex;
    gap: 8px;
  }
  
  /* Inline Layout */
  .inline-preview {
    background: #1f2937;
    border: 1px solid #374151;
    border-radius: 6px;
    padding: 12px;
  }
  
  .inline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }
  
  .inline-title {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .inline-actions {
    display: flex;
    gap: 4px;
  }
  
  .inline-details {
    display: flex;
    gap: 12px;
    font-size: 12px;
    color: #9ca3af;
  }
  
  .model-info, .capability-count, .team-info {
    white-space: nowrap;
  }
</style>