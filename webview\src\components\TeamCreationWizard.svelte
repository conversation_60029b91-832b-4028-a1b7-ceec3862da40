<script>
    import { createEventDispatcher } from 'svelte';
    
    const dispatch = createEventDispatcher();
    
    let currentStep = 1;
    const totalSteps = 4;
    
    let teamData = {
        name: '',
        description: '',
        maxMembers: 10,
        objectives: [''],
        communicationChannel: 'default',
        initialMembers: [],
        leaderId: null
    };
    
    let availableAgents = [];
    let selectedAgents = [];
    let loading = false;
    let error = null;
    
    export { availableAgents, loading, error };
    
    function nextStep() {
        if (currentStep < totalSteps) {
            currentStep++;
        }
    }
    
    function prevStep() {
        if (currentStep > 1) {
            currentStep--;
        }
    }
    
    function addObjective() {
        teamData.objectives = [...teamData.objectives, ''];
    }
    
    function removeObjective(index) {
        teamData.objectives = teamData.objectives.filter((_, i) => i !== index);
    }
    
    function toggleAgentSelection(agent) {
        const index = selectedAgents.findIndex(a => a.id === agent.id);
        if (index >= 0) {
            selectedAgents = selectedAgents.filter(a => a.id !== agent.id);
        } else {
            selectedAgents = [...selectedAgents, agent];
        }
    }
    
    function setLeader(agent) {
        teamData.leaderId = agent.id;
        if (!selectedAgents.find(a => a.id === agent.id)) {
            selectedAgents = [...selectedAgents, agent];
        }
    }
    
    function isStepValid() {
        switch (currentStep) {
            case 1:
                return teamData.name.trim() !== '';
            case 2:
                return teamData.objectives.some(obj => obj.trim() !== '');
            case 3:
                return selectedAgents.length > 0;
            case 4:
                return true;
            default:
                return false;
        }
    }
    
    function createTeam() {
        teamData.initialMembers = selectedAgents.map(agent => agent.id);
        dispatch('create', teamData);
    }
    
    function cancel() {
        dispatch('cancel');
    }
    
    function getRoleColor(role) {
        const colors = {
            'coder': 'bg-blue-100 text-blue-800',
            'debugger': 'bg-red-100 text-red-800',
            'pm': 'bg-green-100 text-green-800',
            'ux': 'bg-purple-100 text-purple-800',
            'qa': 'bg-yellow-100 text-yellow-800',
            'critic': 'bg-orange-100 text-orange-800',
            'planner': 'bg-indigo-100 text-indigo-800'
        };
        return colors[role] || 'bg-gray-100 text-gray-800';
    }
</script>

<div class="team-creation-wizard max-w-2xl mx-auto p-6">
    <div class="mb-6">
        <h2 class="text-2xl font-bold mb-2">Create New Team</h2>
        <div class="flex items-center space-x-2">
            {#each Array(totalSteps) as _, index}
                <div class="flex items-center">
                    <div
                        class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium {
                            index + 1 <= currentStep ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-600'
                        }"
                    >
                        {index + 1}
                    </div>
                    {#if index < totalSteps - 1}
                        <div class="w-8 h-1 {index + 1 < currentStep ? 'bg-blue-500' : 'bg-gray-200'}"></div>
                    {/if}
                </div>
            {/each}
        </div>
    </div>
    
    {#if error}
        <div class="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
        </div>
    {/if}
    
    <!-- Step 1: Basic Information -->
    {#if currentStep === 1}
        <div class="space-y-4">
            <h3 class="text-lg font-semibold">Basic Information</h3>
            
            <div>
                <label class="block text-sm font-medium mb-1">Team Name *</label>
                <input
                    bind:value={teamData.name}
                    type="text"
                    class="w-full p-2 border rounded"
                    placeholder="Enter team name"
                    required
                />
            </div>
            
            <div>
                <label class="block text-sm font-medium mb-1">Description</label>
                <textarea
                    bind:value={teamData.description}
                    class="w-full p-2 border rounded"
                    placeholder="Describe the team's purpose and goals"
                    rows="3"
                ></textarea>
            </div>
            
            <div>
                <label class="block text-sm font-medium mb-1">Maximum Members</label>
                <input
                    bind:value={teamData.maxMembers}
                    type="number"
                    min="1"
                    max="50"
                    class="w-full p-2 border rounded"
                />
            </div>
            
            <div>
                <label class="block text-sm font-medium mb-1">Communication Channel</label>
                <input
                    bind:value={teamData.communicationChannel}
                    type="text"
                    class="w-full p-2 border rounded"
                    placeholder="Channel name for team communication"
                />
            </div>
        </div>
    {/if}
    
    <!-- Step 2: Objectives -->
    {#if currentStep === 2}
        <div class="space-y-4">
            <h3 class="text-lg font-semibold">Team Objectives</h3>
            <p class="text-gray-600">Define what this team aims to achieve</p>
            
            {#each teamData.objectives as objective, index}
                <div class="flex items-center space-x-2">
                    <input
                        bind:value={teamData.objectives[index]}
                        type="text"
                        class="flex-1 p-2 border rounded"
                        placeholder="Enter objective"
                    />
                    <button
                        type="button"
                        on:click={() => removeObjective(index)}
                        class="px-3 py-2 bg-red-500 text-white rounded hover:bg-red-600"
                        disabled={teamData.objectives.length <= 1}
                    >
                        ×
                    </button>
                </div>
            {/each}
            
            <button
                type="button"
                on:click={addObjective}
                class="text-blue-500 hover:text-blue-700"
            >
                + Add Objective
            </button>
        </div>
    {/if}
    
    <!-- Step 3: Select Members -->
    {#if currentStep === 3}
        <div class="space-y-4">
            <h3 class="text-lg font-semibold">Select Team Members</h3>
            <p class="text-gray-600">Choose agents to include in your team</p>
            
            <div class="grid grid-cols-1 gap-3 max-h-96 overflow-y-auto">
                {#each availableAgents as agent}
                    <div
                        class="p-3 border rounded cursor-pointer hover:bg-gray-50 transition-colors {
                            selectedAgents.find(a => a.id === agent.id) ? 'bg-blue-50 border-blue-300' : ''
                        }"
                        on:click={() => toggleAgentSelection(agent)}
                    >
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="font-medium">{agent.name}</div>
                                <div class="flex items-center space-x-2 text-sm">
                                    <span class="px-2 py-1 rounded text-xs {getRoleColor(agent.role)}">
                                        {agent.role}
                                    </span>
                                    <span class="text-gray-500">
                                        {agent.tasks_completed} tasks • {(agent.success_rate * 100).toFixed(1)}% success
                                    </span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                {#if teamData.leaderId === agent.id}
                                    <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded">Leader</span>
                                {/if}
                                <input
                                    type="checkbox"
                                    checked={selectedAgents.find(a => a.id === agent.id) !== undefined}
                                    class="w-4 h-4"
                                />
                            </div>
                        </div>
                    </div>
                {/each}
            </div>
            
            <div class="text-sm text-gray-600">
                Selected: {selectedAgents.length} of {availableAgents.length} agents
            </div>
        </div>
    {/if}
    
    <!-- Step 4: Select Leader -->
    {#if currentStep === 4}
        <div class="space-y-4">
            <h3 class="text-lg font-semibold">Select Team Leader</h3>
            <p class="text-gray-600">Choose a leader from the selected team members</p>
            
            {#if selectedAgents.length === 0}
                <p class="text-red-600">No team members selected. Please go back and select at least one member.</p>
            {:else}
                <div class="space-y-2">
                    {#each selectedAgents as agent}
                        <div
                            class="p-3 border rounded cursor-pointer hover:bg-gray-50 transition-colors {
                                teamData.leaderId === agent.id ? 'bg-yellow-50 border-yellow-300' : ''
                            }"
                            on:click={() => setLeader(agent)}
                        >
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="font-medium">{agent.name}</div>
                                    <div class="flex items-center space-x-2 text-sm">
                                        <span class="px-2 py-1 rounded text-xs {getRoleColor(agent.role)}">
                                            {agent.role}
                                        </span>
                                        <span class="text-gray-500">
                                            {agent.tasks_completed} tasks • {(agent.success_rate * 100).toFixed(1)}% success
                                        </span>
                                    </div>
                                </div>
                                <input
                                    type="radio"
                                    bind:group={teamData.leaderId}
                                    value={agent.id}
                                    class="w-4 h-4"
                                />
                            </div>
                        </div>
                    {/each}
                </div>
                
                <div class="p-3 bg-blue-50 rounded">
                    <p class="text-sm text-blue-800">
                        <strong>Optional:</strong> You can skip selecting a leader now and assign one later.
                    </p>
                </div>
            {/if}
        </div>
    {/if}
    
    <!-- Navigation -->
    <div class="flex justify-between mt-6">
        <button
            on:click={prevStep}
            class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
            disabled={currentStep === 1}
        >
            Previous
        </button>
        
        <button
            on:click={cancel}
            class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
        >
            Cancel
        </button>
        
        {#if currentStep < totalSteps}
            <button
                on:click={nextStep}
                class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                disabled={!isStepValid()}
            >
                Next
            </button>
        {:else}
            <button
                on:click={createTeam}
                class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                disabled={loading || selectedAgents.length === 0}
            >
                {loading ? 'Creating...' : 'Create Team'}
            </button>
        {/if}
    </div>
</div>

<style>
    .team-creation-wizard {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
</style>