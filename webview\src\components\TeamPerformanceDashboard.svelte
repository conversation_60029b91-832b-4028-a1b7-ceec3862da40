<script>
    import { onMount } from 'svelte';
    import { vscode } from '../utils/vscode';
    
    export let teamId;
    
    let analytics = null;
    let loading = false;
    let error = null;
    
    onMount(async () => {
        if (teamId) {
            await loadAnalytics();
        }
    });
    
    $: if (teamId) {
        loadAnalytics();
    }
    
    async function loadAnalytics() {
        try {
            loading = true;
            error = null;
            
            const response = await vscode.postMessage({
                command: 'getTeamAnalytics',
                data: { teamId }
            });
            
            if (response.success) {
                analytics = response.analytics;
            } else {
                error = response.error || 'Failed to load analytics';
            }
        } catch (err) {
            error = `Failed to load analytics: ${err.message}`;
            console.error('Error loading analytics:', err);
        } finally {
            loading = false;
        }
    }
    
    function getHealthColor(score) {
        if (score >= 0.8) return 'text-green-600';
        if (score >= 0.6) return 'text-yellow-600';
        return 'text-red-600';
    }
    
    function getHealthIcon(score) {
        if (score >= 0.8) return '✅';
        if (score >= 0.6) return '⚠️';
        return '❌';
    }
    
    function formatDuration(seconds) {
        if (seconds < 60) return `${Math.round(seconds)}s`;
        if (seconds < 3600) return `${Math.round(seconds / 60)}m`;
        return `${Math.round(seconds / 3600)}h`;
    }
    
    function formatDate(dateString) {
        return new Date(dateString).toLocaleDateString();
    }
</script>

<div class="team-performance-dashboard p-4">
    <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold">Team Performance Dashboard</h3>
        <button
            on:click={loadAnalytics}
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            disabled={loading}
        >
            {loading ? 'Refreshing...' : 'Refresh'}
        </button>
    </div>
    
    {#if error}
        <div class="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
        </div>
    {/if}
    
    {#if loading && !analytics}
        <div class="text-center py-8">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
            <p class="mt-4 text-gray-600">Loading analytics...</p>
        </div>
    {:else if analytics}
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Key Metrics -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h4 class="font-semibold mb-4">Key Metrics</h4>
                <div class="grid grid-cols-2 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">{analytics.tasks_completed}</div>
                        <div class="text-sm text-gray-600">Tasks Completed</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">{(analytics.success_rate * 100).toFixed(1)}%</div>
                        <div class="text-sm text-gray-600">Success Rate</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600">{analytics.member_count}</div>
                        <div class="text-sm text-gray-600">Team Members</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-orange-600">
                            {analytics.avg_completion_time > 0 ? formatDuration(analytics.avg_completion_time) : 'N/A'}
                        </div>
                        <div class="text-sm text-gray-600">Avg. Completion Time</div>
                    </div>
                </div>
            </div>
            
            <!-- Performance Trends -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h4 class="font-semibold mb-4">Performance Trends</h4>
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Tasks per Day</span>
                        <span class="font-medium">{analytics.performance_trends.tasks_per_day.toFixed(1)}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Efficiency Score</span>
                        <span class="font-medium">{analytics.performance_trends.efficiency_score.toFixed(2)}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Collaboration Score</span>
                        <span class="font-medium">{(analytics.performance_trends.collaboration_score * 100).toFixed(1)}%</span>
                    </div>
                </div>
            </div>
            
            <!-- Health Indicators -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h4 class="font-semibold mb-4">Team Health</h4>
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Member Stability</span>
                        <div class="flex items-center space-x-2">
                            <span class="text-lg">{getHealthIcon(analytics.health_indicators.member_stability)}</span>
                            <span class="font-medium {getHealthColor(analytics.health_indicators.member_stability)}">
                                {(analytics.health_indicators.member_stability * 100).toFixed(1)}%
                            </span>
                        </div>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Communication Activity</span>
                        <div class="flex items-center space-x-2">
                            <span class="text-lg">{getHealthIcon(Math.min(analytics.health_indicators.communication_activity / 5, 1))}</span>
                            <span class="font-medium">
                                {analytics.health_indicators.communication_activity.toFixed(1)}/day
                            </span>
                        </div>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Leadership Stability</span>
                        <div class="flex items-center space-x-2">
                            <span class="text-lg">{getHealthIcon(analytics.health_indicators.leadership_stability)}</span>
                            <span class="font-medium {getHealthColor(analytics.health_indicators.leadership_stability)}">
                                {analytics.health_indicators.leadership_stability > 0 ? 'Stable' : 'No Leader'}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Role Distribution -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h4 class="font-semibold mb-4">Role Distribution</h4>
                <div class="space-y-2">
                    {#each Object.entries(analytics.role_distribution) as [role, count]}
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600 capitalize">{role}</span>
                            <div class="flex items-center space-x-2">
                                <div class="w-16 bg-gray-200 rounded">
                                    <div
                                        class="h-2 bg-blue-500 rounded"
                                        style="width: {(count / analytics.member_count) * 100}%"
                                    ></div>
                                </div>
                                <span class="font-medium text-sm">{count}</span>
                            </div>
                        </div>
                    {/each}
                </div>
            </div>
            
            <!-- Team Members Performance -->
            <div class="bg-white p-6 rounded-lg shadow lg:col-span-2">
                <h4 class="font-semibold mb-4">Member Performance</h4>
                <div class="overflow-x-auto">
                    <table class="w-full text-sm">
                        <thead>
                            <tr class="border-b">
                                <th class="text-left p-2">Member</th>
                                <th class="text-left p-2">Role</th>
                                <th class="text-right p-2">Tasks</th>
                                <th class="text-right p-2">Success Rate</th>
                                <th class="text-right p-2">Avg. Response</th>
                                <th class="text-right p-2">Performance</th>
                            </tr>
                        </thead>
                        <tbody>
                            {#each analytics.members as member}
                                <tr class="border-b">
                                    <td class="p-2 font-medium">{member.agent_id}</td>
                                    <td class="p-2">
                                        <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                                            {member.role}
                                        </span>
                                    </td>
                                    <td class="p-2 text-right">{member.tasks_completed}</td>
                                    <td class="p-2 text-right">{(member.success_rate * 100).toFixed(1)}%</td>
                                    <td class="p-2 text-right">{formatDuration(member.avg_response_time / 1000)}</td>
                                    <td class="p-2 text-right">
                                        <div class="flex items-center justify-end space-x-2">
                                            <div class="w-12 bg-gray-200 rounded">
                                                <div
                                                    class="h-2 bg-green-500 rounded"
                                                    style="width: {member.success_rate * 100}%"
                                                ></div>
                                            </div>
                                            <span class="text-xs text-gray-500">{(member.success_rate * 100).toFixed(0)}%</span>
                                        </div>
                                    </td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- Recommendations -->
            <div class="bg-white p-6 rounded-lg shadow lg:col-span-2">
                <h4 class="font-semibold mb-4">Recommendations</h4>
                {#if analytics.recommendations.length > 0}
                    <div class="space-y-2">
                        {#each analytics.recommendations as recommendation}
                            <div class="flex items-start space-x-3">
                                <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                                <p class="text-gray-700">{recommendation}</p>
                            </div>
                        {/each}
                    </div>
                {:else}
                    <p class="text-gray-600">No recommendations at this time. Your team is performing well!</p>
                {/if}
            </div>
        </div>
    {:else}
        <div class="text-center py-8">
            <p class="text-gray-600">No analytics available</p>
        </div>
    {/if}
</div>

<style>
    .team-performance-dashboard {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
</style>