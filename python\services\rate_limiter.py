"""
Rate Limiting System for Metamorphic Reactor
Advanced rate limiting with sliding windows, burst control, and adaptive throttling
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import aiosqlite
from pathlib import Path

logger = logging.getLogger(__name__)

class LimitType(str, Enum):
    """Rate limit types"""
    REQUESTS_PER_MINUTE = "rpm"
    REQUESTS_PER_HOUR = "rph"
    REQUESTS_PER_DAY = "rpd"
    TOKENS_PER_MINUTE = "tpm"
    TOKENS_PER_HOUR = "tph"
    TOKENS_PER_DAY = "tpd"
    COST_PER_HOUR = "cph"
    COST_PER_DAY = "cpd"
    COST_PER_MONTH = "cpm"

class LimitScope(str, Enum):
    """Rate limit scope"""
    GLOBAL = "global"           # System-wide limits
    USER = "user"               # Per-user limits
    API_KEY = "api_key"         # Per-API-key limits
    TASK = "task"               # Per-task limits
    AGENT = "agent"             # Per-agent limits

@dataclass
class RateLimit:
    """Rate limit definition"""
    limit_id: str
    scope: LimitScope
    scope_id: str  # user_id, key_id, task_id, etc.
    limit_type: LimitType
    limit_value: float
    window_seconds: int
    burst_allowance: float = 1.2  # 20% burst over limit
    adaptive: bool = False  # Enable adaptive throttling
    priority: int = 1  # 1 = highest priority
    enabled: bool = True
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()

@dataclass
class UsageWindow:
    """Sliding window for usage tracking"""
    window_start: float
    window_end: float
    request_count: int = 0
    token_count: int = 0
    cost_total: float = 0.0
    request_times: deque = None
    
    def __post_init__(self):
        if self.request_times is None:
            self.request_times = deque()

@dataclass
class RateLimitResult:
    """Result of rate limit check"""
    allowed: bool
    limit_hit: Optional[RateLimit] = None
    retry_after_seconds: Optional[float] = None
    current_usage: Optional[float] = None
    limit_value: Optional[float] = None
    window_reset_time: Optional[datetime] = None
    metadata: Dict[str, Any] = None

class RateLimiter:
    """
    Advanced rate limiting system with multiple strategies
    
    Features:
    - Sliding window rate limiting
    - Token bucket algorithm for burst control
    - Adaptive throttling based on system load
    - Multiple limit types (requests, tokens, cost)
    - Hierarchical scoping (global, user, API key, task, agent)
    - Persistent storage and recovery
    """
    
    def __init__(self, db_path: str = "data/rate_limits.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # In-memory structures for fast lookups
        self.rate_limits: Dict[str, RateLimit] = {}
        self.usage_windows: Dict[str, Dict[str, UsageWindow]] = defaultdict(dict)
        self.token_buckets: Dict[str, Dict[str, float]] = defaultdict(dict)  # Current token count
        self.last_refill: Dict[str, Dict[str, float]] = defaultdict(dict)  # Last refill time
        
        # Adaptive throttling state
        self.system_load_factor = 1.0  # 1.0 = normal, >1.0 = high load
        self.adaptive_enabled = True
        
        self._initialized = False
        
        # Default system limits
        self.default_limits = [
            RateLimit("global_rpm", LimitScope.GLOBAL, "system", LimitType.REQUESTS_PER_MINUTE, 1000, 60),
            RateLimit("global_tpm", LimitScope.GLOBAL, "system", LimitType.TOKENS_PER_MINUTE, 100000, 60),
            RateLimit("global_cpd", LimitScope.GLOBAL, "system", LimitType.COST_PER_DAY, 100.0, 86400),
            RateLimit("user_rpm", LimitScope.USER, "default", LimitType.REQUESTS_PER_MINUTE, 100, 60),
            RateLimit("user_tpm", LimitScope.USER, "default", LimitType.TOKENS_PER_MINUTE, 10000, 60),
            RateLimit("user_cpd", LimitScope.USER, "default", LimitType.COST_PER_DAY, 10.0, 86400),
            RateLimit("task_rpm", LimitScope.TASK, "default", LimitType.REQUESTS_PER_MINUTE, 50, 60),
            RateLimit("task_tpm", LimitScope.TASK, "default", LimitType.TOKENS_PER_MINUTE, 5000, 60),
        ]
    
    async def initialize(self):
        """Initialize the rate limiter"""
        if self._initialized:
            return
        
        await self._create_tables()
        await self._load_limits_from_db()
        await self._create_default_limits()
        
        # Start background cleanup task
        asyncio.create_task(self._cleanup_task())
        
        self._initialized = True
        logger.info("Rate Limiter initialized successfully")
    
    async def _create_tables(self):
        """Create database tables"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                CREATE TABLE IF NOT EXISTS rate_limits (
                    limit_id TEXT PRIMARY KEY,
                    scope TEXT NOT NULL,
                    scope_id TEXT NOT NULL,
                    limit_type TEXT NOT NULL,
                    limit_value REAL NOT NULL,
                    window_seconds INTEGER NOT NULL,
                    burst_allowance REAL DEFAULT 1.2,
                    adaptive BOOLEAN DEFAULT FALSE,
                    priority INTEGER DEFAULT 1,
                    enabled BOOLEAN DEFAULT TRUE,
                    created_at TEXT NOT NULL
                )
            """)
            
            await db.execute("""
                CREATE TABLE IF NOT EXISTS usage_history (
                    record_id TEXT PRIMARY KEY,
                    scope TEXT NOT NULL,
                    scope_id TEXT NOT NULL,
                    request_count INTEGER DEFAULT 0,
                    token_count INTEGER DEFAULT 0,
                    cost_total REAL DEFAULT 0.0,
                    window_start TEXT NOT NULL,
                    window_end TEXT NOT NULL,
                    created_at TEXT NOT NULL
                )
            """)
            
            # Indexes
            await db.execute("CREATE INDEX IF NOT EXISTS idx_limits_scope ON rate_limits (scope, scope_id)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_usage_scope ON usage_history (scope, scope_id)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_usage_window ON usage_history (window_start, window_end)")
            
            await db.commit()
    
    async def _load_limits_from_db(self):
        """Load rate limits from database"""
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute("SELECT * FROM rate_limits WHERE enabled = TRUE") as cursor:
                async for row in cursor:
                    limit = RateLimit(
                        limit_id=row[0],
                        scope=LimitScope(row[1]),
                        scope_id=row[2],
                        limit_type=LimitType(row[3]),
                        limit_value=row[4],
                        window_seconds=row[5],
                        burst_allowance=row[6],
                        adaptive=bool(row[7]),
                        priority=row[8],
                        enabled=bool(row[9]),
                        created_at=datetime.fromisoformat(row[10])
                    )
                    self.rate_limits[limit.limit_id] = limit
        
        logger.info(f"Loaded {len(self.rate_limits)} rate limits from database")
    
    async def _create_default_limits(self):
        """Create default rate limits if they don't exist"""
        for default_limit in self.default_limits:
            if default_limit.limit_id not in self.rate_limits:
                await self.add_rate_limit(default_limit)
    
    async def add_rate_limit(self, rate_limit: RateLimit) -> bool:
        """Add a new rate limit"""
        await self.initialize()
        
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute("""
                    INSERT OR REPLACE INTO rate_limits (
                        limit_id, scope, scope_id, limit_type, limit_value,
                        window_seconds, burst_allowance, adaptive, priority,
                        enabled, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    rate_limit.limit_id, rate_limit.scope.value, rate_limit.scope_id,
                    rate_limit.limit_type.value, rate_limit.limit_value,
                    rate_limit.window_seconds, rate_limit.burst_allowance,
                    rate_limit.adaptive, rate_limit.priority, rate_limit.enabled,
                    rate_limit.created_at.isoformat()
                ))
                await db.commit()
            
            self.rate_limits[rate_limit.limit_id] = rate_limit
            logger.info(f"Added rate limit: {rate_limit.limit_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add rate limit {rate_limit.limit_id}: {e}")
            return False
    
    async def check_rate_limit(
        self,
        scope: LimitScope,
        scope_id: str,
        request_tokens: int = 1,
        request_cost: float = 0.0,
        context: Dict[str, Any] = None
    ) -> RateLimitResult:
        """
        Check if request is within rate limits
        
        Args:
            scope: The scope of the limit check
            scope_id: The specific ID within the scope
            request_tokens: Number of tokens for this request
            request_cost: Cost of this request
            context: Additional context for adaptive limiting
            
        Returns:
            RateLimitResult: Result of the rate limit check
        """
        await self.initialize()
        
        now = time.time()
        
        # Get applicable rate limits (most restrictive first)
        applicable_limits = self._get_applicable_limits(scope, scope_id)
        
        for rate_limit in applicable_limits:
            result = await self._check_single_limit(
                rate_limit, now, request_tokens, request_cost, context
            )
            
            if not result.allowed:
                return result
        
        # All limits passed - record the usage
        await self._record_usage(scope, scope_id, now, 1, request_tokens, request_cost)
        
        return RateLimitResult(allowed=True)
    
    def _get_applicable_limits(self, scope: LimitScope, scope_id: str) -> List[RateLimit]:
        """Get applicable rate limits in priority order"""
        
        applicable = []
        
        # Add specific scope limits
        for limit in self.rate_limits.values():
            if not limit.enabled:
                continue
            
            if limit.scope == scope and (limit.scope_id == scope_id or limit.scope_id == "default"):
                applicable.append(limit)
        
        # Add global limits
        for limit in self.rate_limits.values():
            if limit.scope == LimitScope.GLOBAL and limit.enabled:
                applicable.append(limit)
        
        # Sort by priority (1 = highest priority)
        applicable.sort(key=lambda l: l.priority)
        
        return applicable
    
    async def _check_single_limit(
        self,
        rate_limit: RateLimit,
        now: float,
        request_tokens: int,
        request_cost: float,
        context: Dict[str, Any]
    ) -> RateLimitResult:
        """Check a single rate limit"""
        
        window_key = f"{rate_limit.scope.value}:{rate_limit.scope_id}:{rate_limit.limit_type.value}"
        
        # Get or create usage window
        if window_key not in self.usage_windows:
            self.usage_windows[window_key] = UsageWindow(
                window_start=now,
                window_end=now + rate_limit.window_seconds
            )
        
        window = self.usage_windows[window_key]
        
        # Slide the window if needed
        if now >= window.window_end:
            window.window_start = now
            window.window_end = now + rate_limit.window_seconds
            window.request_count = 0
            window.token_count = 0
            window.cost_total = 0.0
            window.request_times.clear()
        
        # Remove old requests from sliding window
        cutoff_time = now - rate_limit.window_seconds
        while window.request_times and window.request_times[0] < cutoff_time:
            window.request_times.popleft()
        
        # Determine what value to check based on limit type
        current_value = 0.0
        request_value = 0.0
        
        if rate_limit.limit_type in [LimitType.REQUESTS_PER_MINUTE, LimitType.REQUESTS_PER_HOUR, LimitType.REQUESTS_PER_DAY]:
            current_value = len(window.request_times)
            request_value = 1.0
        elif rate_limit.limit_type in [LimitType.TOKENS_PER_MINUTE, LimitType.TOKENS_PER_HOUR, LimitType.TOKENS_PER_DAY]:
            current_value = window.token_count
            request_value = request_tokens
        elif rate_limit.limit_type in [LimitType.COST_PER_HOUR, LimitType.COST_PER_DAY, LimitType.COST_PER_MONTH]:
            current_value = window.cost_total
            request_value = request_cost
        
        # Apply adaptive throttling
        effective_limit = rate_limit.limit_value
        if rate_limit.adaptive and self.adaptive_enabled:
            effective_limit = effective_limit / self.system_load_factor
        
        # Check if request would exceed limit
        projected_value = current_value + request_value
        
        # Allow burst up to burst_allowance
        burst_limit = effective_limit * rate_limit.burst_allowance
        
        if projected_value > burst_limit:
            # Calculate retry after time
            retry_after = self._calculate_retry_after(window, rate_limit, now)
            
            return RateLimitResult(
                allowed=False,
                limit_hit=rate_limit,
                retry_after_seconds=retry_after,
                current_usage=current_value,
                limit_value=effective_limit,
                window_reset_time=datetime.fromtimestamp(window.window_end),
                metadata={
                    "burst_limit": burst_limit,
                    "projected_value": projected_value,
                    "adaptive_factor": self.system_load_factor if rate_limit.adaptive else 1.0
                }
            )
        
        return RateLimitResult(allowed=True)
    
    def _calculate_retry_after(self, window: UsageWindow, rate_limit: RateLimit, now: float) -> float:
        """Calculate how long to wait before retrying"""
        
        # Simple calculation: time until window slides enough to allow request
        if window.request_times:
            oldest_request = window.request_times[0]
            retry_after = oldest_request + rate_limit.window_seconds - now
            return max(0, retry_after)
        
        return rate_limit.window_seconds
    
    async def _record_usage(
        self,
        scope: LimitScope,
        scope_id: str,
        timestamp: float,
        request_count: int,
        token_count: int,
        cost_total: float
    ):
        """Record usage in all relevant windows"""
        
        for limit in self.rate_limits.values():
            if not limit.enabled:
                continue
            
            if (limit.scope == scope and (limit.scope_id == scope_id or limit.scope_id == "default")) or \
               limit.scope == LimitScope.GLOBAL:
                
                window_key = f"{limit.scope.value}:{limit.scope_id}:{limit.limit_type.value}"
                
                if window_key in self.usage_windows:
                    window = self.usage_windows[window_key]
                    window.request_count += request_count
                    window.token_count += token_count
                    window.cost_total += cost_total
                    window.request_times.append(timestamp)
    
    async def get_usage_stats(
        self,
        scope: LimitScope,
        scope_id: str,
        hours: int = 24
    ) -> Dict[str, Any]:
        """Get usage statistics for a scope"""
        
        await self.initialize()
        
        stats = {
            "scope": scope.value,
            "scope_id": scope_id,
            "time_range_hours": hours,
            "limits": {},
            "current_usage": {},
            "utilization": {}
        }
        
        # Get applicable limits
        applicable_limits = self._get_applicable_limits(scope, scope_id)
        
        for limit in applicable_limits:
            window_key = f"{limit.scope.value}:{limit.scope_id}:{limit.limit_type.value}"
            
            stats["limits"][limit.limit_type.value] = {
                "limit_value": limit.limit_value,
                "window_seconds": limit.window_seconds,
                "burst_allowance": limit.burst_allowance
            }
            
            if window_key in self.usage_windows:
                window = self.usage_windows[window_key]
                
                if limit.limit_type in [LimitType.REQUESTS_PER_MINUTE, LimitType.REQUESTS_PER_HOUR, LimitType.REQUESTS_PER_DAY]:
                    current = len(window.request_times)
                elif limit.limit_type in [LimitType.TOKENS_PER_MINUTE, LimitType.TOKENS_PER_HOUR, LimitType.TOKENS_PER_DAY]:
                    current = window.token_count
                else:
                    current = window.cost_total
                
                stats["current_usage"][limit.limit_type.value] = current
                stats["utilization"][limit.limit_type.value] = current / limit.limit_value if limit.limit_value > 0 else 0
        
        return stats
    
    async def reset_usage(self, scope: LimitScope, scope_id: str, limit_type: Optional[LimitType] = None):
        """Reset usage for a scope (emergency function)"""
        
        await self.initialize()
        
        for limit in self.rate_limits.values():
            if limit.scope == scope and limit.scope_id == scope_id:
                if limit_type is None or limit.limit_type == limit_type:
                    window_key = f"{limit.scope.value}:{limit.scope_id}:{limit.limit_type.value}"
                    if window_key in self.usage_windows:
                        window = self.usage_windows[window_key]
                        window.request_count = 0
                        window.token_count = 0
                        window.cost_total = 0.0
                        window.request_times.clear()
        
        logger.warning(f"Reset usage for {scope.value}:{scope_id}")
    
    async def update_system_load(self, load_factor: float):
        """Update system load factor for adaptive throttling"""
        
        self.system_load_factor = max(0.1, min(10.0, load_factor))  # Clamp between 0.1 and 10.0
        logger.debug(f"Updated system load factor: {self.system_load_factor}")
    
    async def enable_adaptive_throttling(self, enabled: bool):
        """Enable or disable adaptive throttling"""
        
        self.adaptive_enabled = enabled
        logger.info(f"Adaptive throttling {'enabled' if enabled else 'disabled'}")
    
    async def _cleanup_task(self):
        """Background task to clean up old usage data"""
        
        while True:
            try:
                await asyncio.sleep(3600)  # Run every hour
                
                now = time.time()
                cutoff_time = now - 86400  # Keep 24 hours of data
                
                # Clean up old usage windows
                for window_key, window in list(self.usage_windows.items()):
                    if window.window_end < cutoff_time:
                        del self.usage_windows[window_key]
                
                # Clean up database
                async with aiosqlite.connect(self.db_path) as db:
                    await db.execute("""
                        DELETE FROM usage_history 
                        WHERE window_end < ?
                    """, (datetime.fromtimestamp(cutoff_time).isoformat(),))
                    await db.commit()
                
                logger.debug("Completed rate limiter cleanup")
                
            except Exception as e:
                logger.error(f"Error in rate limiter cleanup: {e}")
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get system status and metrics"""
        
        return {
            "initialized": self._initialized,
            "total_limits": len(self.rate_limits),
            "active_windows": len(self.usage_windows),
            "adaptive_enabled": self.adaptive_enabled,
            "system_load_factor": self.system_load_factor,
            "memory_usage": {
                "limits": len(self.rate_limits),
                "windows": len(self.usage_windows),
                "token_buckets": sum(len(buckets) for buckets in self.token_buckets.values())
            }
        }
    
    async def cleanup(self):
        """Cleanup resources"""
        logger.info("Rate Limiter cleanup completed")


# Global rate limiter instance
rate_limiter = RateLimiter()