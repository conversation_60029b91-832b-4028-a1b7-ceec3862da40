<script>
    import { onMount } from 'svelte';
    import { vscode } from '../utils/vscode';
    
    export let teamId;
    
    let messages = [];
    let newMessage = '';
    let loading = false;
    let error = null;
    let messageContainer;
    
    onMount(async () => {
        if (teamId) {
            await loadMessages();
        }
    });
    
    $: if (teamId) {
        loadMessages();
    }
    
    async function loadMessages() {
        try {
            loading = true;
            error = null;
            
            const response = await vscode.postMessage({
                command: 'getTeamMessages',
                data: { teamId }
            });
            
            if (response.success) {
                messages = response.messages || [];
                setTimeout(scrollToBottom, 100);
            } else {
                error = response.error || 'Failed to load messages';
            }
        } catch (err) {
            error = `Failed to load messages: ${err.message}`;
            console.error('Error loading messages:', err);
        } finally {
            loading = false;
        }
    }
    
    async function sendMessage() {
        if (!newMessage.trim()) return;
        
        try {
            loading = true;
            error = null;
            
            const response = await vscode.postMessage({
                command: 'sendTeamMessage',
                data: {
                    teamId,
                    message: newMessage.trim()
                }
            });
            
            if (response.success) {
                newMessage = '';
                await loadMessages();
            } else {
                error = response.error || 'Failed to send message';
            }
        } catch (err) {
            error = `Failed to send message: ${err.message}`;
            console.error('Error sending message:', err);
        } finally {
            loading = false;
        }
    }
    
    async function clearMessages() {
        if (!confirm('Are you sure you want to clear all messages?')) return;
        
        try {
            loading = true;
            error = null;
            
            const response = await vscode.postMessage({
                command: 'clearTeamMessages',
                data: { teamId }
            });
            
            if (response.success) {
                messages = [];
            } else {
                error = response.error || 'Failed to clear messages';
            }
        } catch (err) {
            error = `Failed to clear messages: ${err.message}`;
            console.error('Error clearing messages:', err);
        } finally {
            loading = false;
        }
    }
    
    function scrollToBottom() {
        if (messageContainer) {
            messageContainer.scrollTop = messageContainer.scrollHeight;
        }
    }
    
    function handleKeyPress(event) {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            sendMessage();
        }
    }
    
    function formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
    
    function formatDate(timestamp) {
        const date = new Date(timestamp);
        const today = new Date();
        const yesterday = new Date();
        yesterday.setDate(today.getDate() - 1);
        
        if (date.toDateString() === today.toDateString()) {
            return 'Today';
        } else if (date.toDateString() === yesterday.toDateString()) {
            return 'Yesterday';
        } else {
            return date.toLocaleDateString();
        }
    }
    
    function shouldShowDateSeparator(message, index) {
        if (index === 0) return true;
        const currentDate = new Date(message.timestamp).toDateString();
        const previousDate = new Date(messages[index - 1].timestamp).toDateString();
        return currentDate !== previousDate;
    }
    
    function getMessageTypeColor(type) {
        const colors = {
            'message': 'bg-blue-50 border-blue-200',
            'system': 'bg-gray-50 border-gray-200',
            'alert': 'bg-red-50 border-red-200',
            'info': 'bg-green-50 border-green-200'
        };
        return colors[type] || 'bg-gray-50 border-gray-200';
    }
    
    function getMessageIcon(type) {
        const icons = {
            'message': '💬',
            'system': '🔧',
            'alert': '⚠️',
            'info': 'ℹ️'
        };
        return icons[type] || '💬';
    }
</script>

<div class="team-communication-panel h-full flex flex-col">
    <!-- Header -->
    <div class="flex justify-between items-center p-4 border-b">
        <h3 class="text-lg font-semibold">Team Communication</h3>
        <div class="flex items-center space-x-2">
            <button
                on:click={loadMessages}
                class="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
                disabled={loading}
            >
                Refresh
            </button>
            <button
                on:click={clearMessages}
                class="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm"
                disabled={loading || messages.length === 0}
            >
                Clear
            </button>
        </div>
    </div>
    
    {#if error}
        <div class="mx-4 mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
        </div>
    {/if}
    
    <!-- Messages -->
    <div class="flex-1 overflow-y-auto p-4" bind:this={messageContainer}>
        {#if loading && messages.length === 0}
            <div class="text-center py-8">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                <p class="mt-2 text-gray-600">Loading messages...</p>
            </div>
        {:else if messages.length === 0}
            <div class="text-center py-8">
                <p class="text-gray-600">No messages yet. Start the conversation!</p>
            </div>
        {:else}
            <div class="space-y-4">
                {#each messages as message, index}
                    {#if shouldShowDateSeparator(message, index)}
                        <div class="flex items-center justify-center my-4">
                            <div class="flex-1 border-t border-gray-300"></div>
                            <div class="px-4 text-sm text-gray-500 bg-white">
                                {formatDate(message.timestamp)}
                            </div>
                            <div class="flex-1 border-t border-gray-300"></div>
                        </div>
                    {/if}
                    
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center text-sm">
                            {getMessageIcon(message.type)}
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="font-medium text-sm">
                                    {message.sender_id === 'system' ? 'System' : message.sender_id}
                                </span>
                                <span class="text-xs text-gray-500">
                                    {formatTimestamp(message.timestamp)}
                                </span>
                                {#if message.is_notification}
                                    <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded">
                                        Notification
                                    </span>
                                {/if}
                            </div>
                            <div class="p-3 rounded-lg border {getMessageTypeColor(message.type)}">
                                <p class="text-sm">{message.message}</p>
                            </div>
                        </div>
                    </div>
                {/each}
            </div>
        {/if}
    </div>
    
    <!-- Message Input -->
    <div class="p-4 border-t">
        <div class="flex items-end space-x-2">
            <div class="flex-1">
                <textarea
                    bind:value={newMessage}
                    on:keypress={handleKeyPress}
                    placeholder="Type your message..."
                    class="w-full p-2 border rounded resize-none"
                    rows="1"
                    style="min-height: 40px; max-height: 120px;"
                    disabled={loading}
                ></textarea>
            </div>
            <button
                on:click={sendMessage}
                class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
                disabled={loading || !newMessage.trim()}
            >
                Send
            </button>
        </div>
        <div class="flex justify-between items-center mt-2 text-xs text-gray-500">
            <span>Press Enter to send, Shift+Enter for new line</span>
            <span>{messages.length} messages</span>
        </div>
    </div>
</div>

<style>
    .team-communication-panel {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    
    textarea {
        transition: height 0.2s ease;
    }
    
    textarea:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
</style>