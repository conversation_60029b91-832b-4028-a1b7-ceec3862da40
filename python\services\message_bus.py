"""
Message Bus for Metamorphic Reactor
Handles inter-agent communication, message routing, and event distribution
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Callable, Set, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
import uuid
import sqlite3
import os
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

class MessageType(str, Enum):
    """Message types for the message bus"""
    DIRECT = "direct"           # Direct agent-to-agent message
    BROADCAST = "broadcast"     # Message to all agents
    TEAM = "team"              # Message to team members
    SYSTEM = "system"          # System notifications
    TASK = "task"              # Task-related messages
    CONSENSUS = "consensus"     # Consensus building messages
    ESCALATION = "escalation"   # Escalation messages
    HEARTBEAT = "heartbeat"     # Health check messages
    EVENT = "event"            # Generic event messages
    FEEDBACK = "feedback"       # Feedback messages

class MessagePriority(str, Enum):
    """Message priority levels"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"
    CRITICAL = "critical"

class MessageStatus(str, Enum):
    """Message delivery status"""
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    READ = "read"
    FAILED = "failed"
    EXPIRED = "expired"
    ESCALATED = "escalated"

class EscalationTrigger(str, Enum):
    """Escalation trigger conditions"""
    FAILED_DELIVERY = "failed_delivery"
    NO_RESPONSE = "no_response"
    TIMEOUT = "timeout"
    PRIORITY_THRESHOLD = "priority_threshold"
    RETRY_LIMIT = "retry_limit"
    CONTENT_BASED = "content_based"
    SENDER_BASED = "sender_based"

class EscalationAction(str, Enum):
    """Escalation actions"""
    NOTIFY_SUPERVISOR = "notify_supervisor"
    INCREASE_PRIORITY = "increase_priority"
    BROADCAST_ALERT = "broadcast_alert"
    LOG_INCIDENT = "log_incident"
    FORWARD_TO_TEAM = "forward_to_team"
    RETRY_WITH_BACKUP = "retry_with_backup"
    MANUAL_INTERVENTION = "manual_intervention"

@dataclass
class Message:
    """Message structure for the message bus"""
    id: str
    type: MessageType
    sender_id: str
    recipient_id: Optional[str] = None
    team_id: Optional[str] = None
    content: str = ""
    data: Dict[str, Any] = field(default_factory=dict)
    priority: MessagePriority = MessagePriority.NORMAL
    timestamp: datetime = field(default_factory=datetime.utcnow)
    expires_at: Optional[datetime] = None
    status: MessageStatus = MessageStatus.PENDING
    retry_count: int = 0
    max_retries: int = 3
    correlation_id: Optional[str] = None
    reply_to: Optional[str] = None
    escalation_level: int = 0
    escalation_history: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary"""
        return {
            "id": self.id,
            "type": self.type.value,
            "sender_id": self.sender_id,
            "recipient_id": self.recipient_id,
            "team_id": self.team_id,
            "content": self.content,
            "data": self.data,
            "priority": self.priority.value,
            "timestamp": self.timestamp.isoformat(),
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "status": self.status.value,
            "retry_count": self.retry_count,
            "max_retries": self.max_retries,
            "correlation_id": self.correlation_id,
            "reply_to": self.reply_to,
            "escalation_level": self.escalation_level,
            "escalation_history": self.escalation_history
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Message':
        """Create message from dictionary"""
        return cls(
            id=data["id"],
            type=MessageType(data["type"]),
            sender_id=data["sender_id"],
            recipient_id=data.get("recipient_id"),
            team_id=data.get("team_id"),
            content=data.get("content", ""),
            data=data.get("data", {}),
            priority=MessagePriority(data.get("priority", MessagePriority.NORMAL)),
            timestamp=datetime.fromisoformat(data["timestamp"]),
            expires_at=datetime.fromisoformat(data["expires_at"]) if data.get("expires_at") else None,
            status=MessageStatus(data.get("status", MessageStatus.PENDING)),
            retry_count=data.get("retry_count", 0),
            max_retries=data.get("max_retries", 3),
            correlation_id=data.get("correlation_id"),
            reply_to=data.get("reply_to"),
            escalation_level=data.get("escalation_level", 0),
            escalation_history=data.get("escalation_history", [])
        )

@dataclass
class MessageHandler:
    """Message handler configuration"""
    handler_id: str
    agent_id: str
    message_types: Set[MessageType]
    callback: Callable[[Message], None]
    filter_func: Optional[Callable[[Message], bool]] = None
    priority: int = 0
    active: bool = True

class PriorityQueue:
    """
    Priority-based message queue with FIFO ordering within priority levels
    """
    
    def __init__(self, max_size: int = 1000):
        self.queues = {
            MessagePriority.CRITICAL: deque(),
            MessagePriority.HIGH: deque(),
            MessagePriority.NORMAL: deque(),
            MessagePriority.LOW: deque()
        }
        self.max_size = max_size
        self.total_size = 0
        self.priority_order = [
            MessagePriority.CRITICAL,
            MessagePriority.HIGH,
            MessagePriority.NORMAL,
            MessagePriority.LOW
        ]
    
    def put(self, message: Message) -> bool:
        """Add message to appropriate priority queue"""
        if self.total_size >= self.max_size:
            # Remove lowest priority message if queue is full
            if not self._remove_lowest_priority():
                return False
        
        self.queues[message.priority].append(message)
        self.total_size += 1
        return True
    
    def get(self) -> Optional[Message]:
        """Get highest priority message"""
        for priority in self.priority_order:
            if self.queues[priority]:
                self.total_size -= 1
                return self.queues[priority].popleft()
        return None
    
    def peek(self) -> Optional[Message]:
        """Peek at highest priority message without removing"""
        for priority in self.priority_order:
            if self.queues[priority]:
                return self.queues[priority][0]
        return None
    
    def size(self) -> int:
        """Get total queue size"""
        return self.total_size
    
    def size_by_priority(self) -> Dict[MessagePriority, int]:
        """Get size by priority level"""
        return {priority: len(queue) for priority, queue in self.queues.items()}
    
    def clear(self):
        """Clear all queues"""
        for queue in self.queues.values():
            queue.clear()
        self.total_size = 0
    
    def _remove_lowest_priority(self) -> bool:
        """Remove lowest priority message to make space"""
        for priority in reversed(self.priority_order):
            if self.queues[priority]:
                self.queues[priority].popleft()
                self.total_size -= 1
                return True
        return False

class MessageQueue:
    """
    Advanced message queue with persistence and filtering
    """
    
    def __init__(self, queue_id: str, max_size: int = 1000):
        self.queue_id = queue_id
        self.priority_queue = PriorityQueue(max_size)
        self.message_filters: List[Callable[[Message], bool]] = []
        self.subscribers: Set[str] = set()
        self.created_at = datetime.utcnow()
        self.stats = {
            "messages_received": 0,
            "messages_delivered": 0,
            "messages_filtered": 0,
            "messages_dropped": 0
        }
    
    def add_filter(self, filter_func: Callable[[Message], bool]):
        """Add a message filter"""
        self.message_filters.append(filter_func)
    
    def remove_filter(self, filter_func: Callable[[Message], bool]):
        """Remove a message filter"""
        if filter_func in self.message_filters:
            self.message_filters.remove(filter_func)
    
    def add_subscriber(self, subscriber_id: str):
        """Add a subscriber to the queue"""
        self.subscribers.add(subscriber_id)
    
    def remove_subscriber(self, subscriber_id: str):
        """Remove a subscriber from the queue"""
        self.subscribers.discard(subscriber_id)
    
    def enqueue(self, message: Message) -> bool:
        """Add message to queue with filtering"""
        self.stats["messages_received"] += 1
        
        # Apply filters
        for filter_func in self.message_filters:
            if not filter_func(message):
                self.stats["messages_filtered"] += 1
                return False
        
        # Add to priority queue
        if self.priority_queue.put(message):
            return True
        else:
            self.stats["messages_dropped"] += 1
            return False
    
    def dequeue(self) -> Optional[Message]:
        """Remove and return highest priority message"""
        message = self.priority_queue.get()
        if message:
            self.stats["messages_delivered"] += 1
        return message
    
    def peek(self) -> Optional[Message]:
        """Peek at highest priority message"""
        return self.priority_queue.peek()
    
    def size(self) -> int:
        """Get queue size"""
        return self.priority_queue.size()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get queue statistics"""
        return {
            "queue_id": self.queue_id,
            "size": self.size(),
            "size_by_priority": self.priority_queue.size_by_priority(),
            "subscribers": len(self.subscribers),
            "filters": len(self.message_filters),
            "created_at": self.created_at.isoformat(),
            **self.stats
        }
    
    def clear(self):
        """Clear the queue"""
        self.priority_queue.clear()

@dataclass
class EscalationRule:
    """Escalation rule configuration"""
    rule_id: str
    trigger: EscalationTrigger
    condition: Callable[[Message], bool]
    action: EscalationAction
    escalation_targets: List[str]
    priority: int = 0
    max_escalations: int = 3
    cooldown_period: timedelta = timedelta(minutes=5)
    enabled: bool = True
    hit_count: int = 0
    last_triggered: Optional[datetime] = None
    
    def can_trigger(self) -> bool:
        """Check if the rule can be triggered (not in cooldown)"""
        if not self.enabled:
            return False
        
        if self.last_triggered is None:
            return True
        
        return datetime.utcnow() - self.last_triggered > self.cooldown_period

class EscalationManager:
    """
    Manages message escalation rules and processing
    """
    
    def __init__(self, message_bus: 'MessageBus'):
        self.message_bus = message_bus
        self.escalation_rules: Dict[str, EscalationRule] = {}
        self.escalation_history: List[Dict[str, Any]] = []
        self.supervisors: Dict[str, List[str]] = {}  # agent_id -> supervisor_ids
        self.escalation_chains: Dict[str, List[str]] = {}  # role -> escalation_chain
        
        # Default escalation targets
        self.default_supervisors = ["supervisor", "admin"]
        self.emergency_contacts = ["emergency_admin"]
        
        logger.info("EscalationManager initialized")
    
    def add_escalation_rule(self, rule: EscalationRule):
        """Add an escalation rule"""
        self.escalation_rules[rule.rule_id] = rule
        logger.info(f"Added escalation rule {rule.rule_id}")
    
    def remove_escalation_rule(self, rule_id: str) -> bool:
        """Remove an escalation rule"""
        if rule_id in self.escalation_rules:
            del self.escalation_rules[rule_id]
            logger.info(f"Removed escalation rule {rule_id}")
            return True
        return False
    
    def set_supervisor(self, agent_id: str, supervisor_ids: List[str]):
        """Set supervisors for an agent"""
        self.supervisors[agent_id] = supervisor_ids
        logger.info(f"Set supervisors for {agent_id}: {supervisor_ids}")
    
    def set_escalation_chain(self, role: str, escalation_chain: List[str]):
        """Set escalation chain for a role"""
        self.escalation_chains[role] = escalation_chain
        logger.info(f"Set escalation chain for {role}: {escalation_chain}")
    
    async def check_escalation(self, message: Message) -> bool:
        """Check if message should be escalated"""
        escalated = False
        
        # Sort rules by priority
        sorted_rules = sorted(
            self.escalation_rules.values(),
            key=lambda r: r.priority,
            reverse=True
        )
        
        for rule in sorted_rules:
            if not rule.can_trigger():
                continue
            
            try:
                # Check if rule condition matches
                if rule.condition(message):
                    # Check specific trigger conditions
                    if await self._check_trigger_condition(message, rule.trigger):
                        await self._execute_escalation(message, rule)
                        escalated = True
                        
                        # Update rule statistics
                        rule.hit_count += 1
                        rule.last_triggered = datetime.utcnow()
                        
                        # Log escalation
                        self._log_escalation(message, rule)
                        
                        # Only trigger first matching rule
                        break
                        
            except Exception as e:
                logger.error(f"Error checking escalation rule {rule.rule_id}: {e}")
        
        return escalated
    
    async def _check_trigger_condition(self, message: Message, trigger: EscalationTrigger) -> bool:
        """Check specific trigger conditions"""
        if trigger == EscalationTrigger.FAILED_DELIVERY:
            return message.status == MessageStatus.FAILED
        
        elif trigger == EscalationTrigger.RETRY_LIMIT:
            return message.retry_count >= message.max_retries
        
        elif trigger == EscalationTrigger.TIMEOUT:
            if message.expires_at:
                return datetime.utcnow() > message.expires_at
            return False
        
        elif trigger == EscalationTrigger.PRIORITY_THRESHOLD:
            return message.priority in [MessagePriority.URGENT, MessagePriority.CRITICAL]
        
        elif trigger == EscalationTrigger.NO_RESPONSE:
            # Check if this is a request that hasn't received a response
            if message.reply_to:
                # Look for response in message history
                responses = await self._find_responses(message.correlation_id)
                return len(responses) == 0
            return False
        
        elif trigger == EscalationTrigger.CONTENT_BASED:
            # Content-based triggers would be handled by rule condition
            return True
        
        elif trigger == EscalationTrigger.SENDER_BASED:
            # Sender-based triggers would be handled by rule condition
            return True
        
        return False
    
    async def _find_responses(self, correlation_id: str) -> List[Message]:
        """Find response messages for a correlation ID"""
        if correlation_id:
            return self.message_bus.get_conversation_history(correlation_id)
        return []
    
    async def _execute_escalation(self, message: Message, rule: EscalationRule):
        """Execute escalation action"""
        try:
            if rule.action == EscalationAction.NOTIFY_SUPERVISOR:
                await self._notify_supervisor(message, rule)
            
            elif rule.action == EscalationAction.INCREASE_PRIORITY:
                await self._increase_priority(message)
            
            elif rule.action == EscalationAction.BROADCAST_ALERT:
                await self._broadcast_alert(message, rule)
            
            elif rule.action == EscalationAction.LOG_INCIDENT:
                await self._log_incident(message, rule)
            
            elif rule.action == EscalationAction.FORWARD_TO_TEAM:
                await self._forward_to_team(message, rule)
            
            elif rule.action == EscalationAction.RETRY_WITH_BACKUP:
                await self._retry_with_backup(message, rule)
            
            elif rule.action == EscalationAction.MANUAL_INTERVENTION:
                await self._request_manual_intervention(message, rule)
            
            # Mark message as escalated
            message.status = MessageStatus.ESCALATED
            message.escalation_level += 1
            message.escalation_history.append(f"Escalated by rule {rule.rule_id} at {datetime.utcnow().isoformat()}")
            
        except Exception as e:
            logger.error(f"Error executing escalation action {rule.action}: {e}")
    
    async def _notify_supervisor(self, message: Message, rule: EscalationRule):
        """Notify supervisor about escalated message"""
        # Get supervisors for the sender
        supervisors = self.supervisors.get(message.sender_id, self.default_supervisors)
        
        # Use rule targets if specified
        if rule.escalation_targets:
            supervisors = rule.escalation_targets
        
        for supervisor in supervisors:
            await self.message_bus.send_message(
                message_type=MessageType.ESCALATION,
                sender_id="escalation_manager",
                recipient_id=supervisor,
                content=f"Escalated message from {message.sender_id}: {message.content}",
                data={
                    "original_message_id": message.id,
                    "escalation_reason": rule.trigger.value,
                    "escalation_level": message.escalation_level,
                    "original_sender": message.sender_id,
                    "original_content": message.content
                },
                priority=MessagePriority.HIGH
            )
    
    async def _increase_priority(self, message: Message):
        """Increase message priority"""
        priority_levels = [
            MessagePriority.LOW,
            MessagePriority.NORMAL,
            MessagePriority.HIGH,
            MessagePriority.URGENT,
            MessagePriority.CRITICAL
        ]
        
        current_index = priority_levels.index(message.priority)
        if current_index < len(priority_levels) - 1:
            message.priority = priority_levels[current_index + 1]
            logger.info(f"Increased priority of message {message.id} to {message.priority}")
    
    async def _broadcast_alert(self, message: Message, rule: EscalationRule):
        """Broadcast alert about escalated message"""
        alert_content = f"ALERT: Message escalation triggered - {rule.trigger.value}"
        
        await self.message_bus.send_message(
            message_type=MessageType.BROADCAST,
            sender_id="escalation_manager",
            content=alert_content,
            data={
                "alert_type": "escalation",
                "original_message_id": message.id,
                "escalation_reason": rule.trigger.value,
                "escalation_level": message.escalation_level
            },
            priority=MessagePriority.CRITICAL
        )
    
    async def _log_incident(self, message: Message, rule: EscalationRule):
        """Log incident for escalated message"""
        incident = {
            "incident_id": str(uuid.uuid4()),
            "timestamp": datetime.utcnow().isoformat(),
            "message_id": message.id,
            "sender_id": message.sender_id,
            "recipient_id": message.recipient_id,
            "escalation_rule": rule.rule_id,
            "escalation_reason": rule.trigger.value,
            "escalation_level": message.escalation_level,
            "content": message.content[:200],  # Truncate for logging
            "priority": message.priority.value,
            "status": message.status.value
        }
        
        logger.warning(f"Escalation incident logged: {incident}")
        
        # Could also save to database or external incident management system
    
    async def _forward_to_team(self, message: Message, rule: EscalationRule):
        """Forward message to escalation team"""
        team_members = rule.escalation_targets or self.default_supervisors
        
        for member in team_members:
            await self.message_bus.send_message(
                message_type=MessageType.DIRECT,
                sender_id="escalation_manager",
                recipient_id=member,
                content=f"Forwarded escalated message: {message.content}",
                data={
                    "forwarded_message_id": message.id,
                    "original_sender": message.sender_id,
                    "escalation_reason": rule.trigger.value
                },
                priority=MessagePriority.HIGH
            )
    
    async def _retry_with_backup(self, message: Message, rule: EscalationRule):
        """Retry message with backup recipients"""
        backup_recipients = rule.escalation_targets
        
        if backup_recipients:
            for recipient in backup_recipients:
                await self.message_bus.send_message(
                    message_type=MessageType.DIRECT,
                    sender_id=message.sender_id,
                    recipient_id=recipient,
                    content=message.content,
                    data={
                        **message.data,
                        "backup_retry": True,
                        "original_message_id": message.id
                    },
                    priority=message.priority,
                    correlation_id=message.correlation_id
                )
    
    async def _request_manual_intervention(self, message: Message, rule: EscalationRule):
        """Request manual intervention for escalated message"""
        intervention_targets = rule.escalation_targets or self.emergency_contacts
        
        for target in intervention_targets:
            await self.message_bus.send_message(
                message_type=MessageType.DIRECT,
                sender_id="escalation_manager",
                recipient_id=target,
                content=f"MANUAL INTERVENTION REQUIRED: {message.content}",
                data={
                    "intervention_required": True,
                    "original_message_id": message.id,
                    "escalation_reason": rule.trigger.value,
                    "escalation_level": message.escalation_level,
                    "original_sender": message.sender_id
                },
                priority=MessagePriority.CRITICAL
            )
    
    def _log_escalation(self, message: Message, rule: EscalationRule):
        """Log escalation event"""
        escalation_event = {
            "timestamp": datetime.utcnow().isoformat(),
            "message_id": message.id,
            "rule_id": rule.rule_id,
            "trigger": rule.trigger.value,
            "action": rule.action.value,
            "escalation_level": message.escalation_level,
            "sender_id": message.sender_id,
            "recipient_id": message.recipient_id
        }
        
        self.escalation_history.append(escalation_event)
        
        # Keep only recent escalations
        if len(self.escalation_history) > 1000:
            self.escalation_history = self.escalation_history[-1000:]
    
    def get_escalation_statistics(self) -> Dict[str, Any]:
        """Get escalation statistics"""
        total_escalations = len(self.escalation_history)
        
        # Group by trigger type
        trigger_counts = {}
        action_counts = {}
        
        for event in self.escalation_history:
            trigger = event["trigger"]
            action = event["action"]
            
            trigger_counts[trigger] = trigger_counts.get(trigger, 0) + 1
            action_counts[action] = action_counts.get(action, 0) + 1
        
        return {
            "total_escalations": total_escalations,
            "escalation_rules": len(self.escalation_rules),
            "trigger_distribution": trigger_counts,
            "action_distribution": action_counts,
            "rule_hit_counts": {
                rule_id: rule.hit_count 
                for rule_id, rule in self.escalation_rules.items()
            }
        }
    
    def create_basic_escalation_rules(self):
        """Create basic escalation rules"""
        # Failed delivery escalation
        self.add_escalation_rule(EscalationRule(
            rule_id="failed_delivery",
            trigger=EscalationTrigger.FAILED_DELIVERY,
            condition=lambda msg: msg.status == MessageStatus.FAILED,
            action=EscalationAction.NOTIFY_SUPERVISOR,
            escalation_targets=self.default_supervisors,
            priority=10
        ))
        
        # High priority timeout escalation
        self.add_escalation_rule(EscalationRule(
            rule_id="critical_timeout",
            trigger=EscalationTrigger.TIMEOUT,
            condition=lambda msg: msg.priority == MessagePriority.CRITICAL,
            action=EscalationAction.BROADCAST_ALERT,
            escalation_targets=[],
            priority=20
        ))
        
        # Retry limit escalation
        self.add_escalation_rule(EscalationRule(
            rule_id="retry_limit",
            trigger=EscalationTrigger.RETRY_LIMIT,
            condition=lambda msg: msg.retry_count >= msg.max_retries,
            action=EscalationAction.LOG_INCIDENT,
            escalation_targets=self.default_supervisors,
            priority=15
        ))

class FeedbackType(str, Enum):
    """Types of feedback messages"""
    PERFORMANCE = "performance"
    QUALITY = "quality"
    COLLABORATION = "collaboration"
    TASK_COMPLETION = "task_completion"
    COMMUNICATION = "communication"
    SUGGESTION = "suggestion"
    COMPLAINT = "complaint"
    PRAISE = "praise"
    BUG_REPORT = "bug_report"
    FEATURE_REQUEST = "feature_request"

class FeedbackSentiment(str, Enum):
    """Sentiment of feedback"""
    POSITIVE = "positive"
    NEGATIVE = "negative"
    NEUTRAL = "neutral"
    MIXED = "mixed"

@dataclass
class FeedbackMessage:
    """Feedback message structure"""
    id: str
    feedback_type: FeedbackType
    sender_id: str
    target_id: Optional[str] = None  # Who the feedback is about
    recipient_id: Optional[str] = None  # Who should receive the feedback
    team_id: Optional[str] = None
    content: str = ""
    sentiment: FeedbackSentiment = FeedbackSentiment.NEUTRAL
    rating: Optional[int] = None  # 1-5 scale
    tags: List[str] = field(default_factory=list)
    context: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.utcnow)
    processed: bool = False
    response_required: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "id": self.id,
            "feedback_type": self.feedback_type.value,
            "sender_id": self.sender_id,
            "target_id": self.target_id,
            "recipient_id": self.recipient_id,
            "team_id": self.team_id,
            "content": self.content,
            "sentiment": self.sentiment.value,
            "rating": self.rating,
            "tags": self.tags,
            "context": self.context,
            "timestamp": self.timestamp.isoformat(),
            "processed": self.processed,
            "response_required": self.response_required
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FeedbackMessage':
        """Create from dictionary"""
        return cls(
            id=data["id"],
            feedback_type=FeedbackType(data["feedback_type"]),
            sender_id=data["sender_id"],
            target_id=data.get("target_id"),
            recipient_id=data.get("recipient_id"),
            team_id=data.get("team_id"),
            content=data.get("content", ""),
            sentiment=FeedbackSentiment(data.get("sentiment", FeedbackSentiment.NEUTRAL)),
            rating=data.get("rating"),
            tags=data.get("tags", []),
            context=data.get("context", {}),
            timestamp=datetime.fromisoformat(data["timestamp"]),
            processed=data.get("processed", False),
            response_required=data.get("response_required", False)
        )

class FeedbackProcessor:
    """
    Processes feedback messages and generates insights
    """
    
    def __init__(self, message_bus: 'MessageBus'):
        self.message_bus = message_bus
        self.feedback_history: List[FeedbackMessage] = []
        self.feedback_handlers: Dict[FeedbackType, List[Callable]] = defaultdict(list)
        self.sentiment_analyzer = None  # Could integrate with sentiment analysis service
        self.auto_response_enabled = True
        
        # Feedback processing rules
        self.processing_rules: List[Dict[str, Any]] = []
        
        # Statistics
        self.feedback_stats = {
            "total_feedback": 0,
            "feedback_by_type": {},
            "feedback_by_sentiment": {},
            "avg_rating": 0.0,
            "response_rate": 0.0
        }
        
        logger.info("FeedbackProcessor initialized")
    
    def register_feedback_handler(self, feedback_type: FeedbackType, handler: Callable):
        """Register a handler for specific feedback type"""
        self.feedback_handlers[feedback_type].append(handler)
        logger.info(f"Registered handler for feedback type {feedback_type}")
    
    def add_processing_rule(self, rule_id: str, condition: Callable, action: Callable, priority: int = 0):
        """Add a feedback processing rule"""
        rule = {
            "id": rule_id,
            "condition": condition,
            "action": action,
            "priority": priority,
            "hit_count": 0
        }
        
        self.processing_rules.append(rule)
        self.processing_rules.sort(key=lambda x: x["priority"], reverse=True)
        
        logger.info(f"Added feedback processing rule {rule_id}")
    
    async def process_feedback(self, message: Message) -> Optional[FeedbackMessage]:
        """Process a feedback message"""
        if message.type != MessageType.FEEDBACK:
            return None
        
        try:
            # Parse feedback message
            feedback_data = message.data.get("feedback", {})
            feedback = FeedbackMessage.from_dict({
                "id": message.id,
                "feedback_type": feedback_data.get("type", FeedbackType.SUGGESTION.value),
                "sender_id": message.sender_id,
                "target_id": feedback_data.get("target_id"),
                "recipient_id": message.recipient_id,
                "team_id": message.team_id,
                "content": message.content,
                "sentiment": feedback_data.get("sentiment", FeedbackSentiment.NEUTRAL.value),
                "rating": feedback_data.get("rating"),
                "tags": feedback_data.get("tags", []),
                "context": feedback_data.get("context", {}),
                "timestamp": message.timestamp.isoformat(),
                "processed": False,
                "response_required": feedback_data.get("response_required", False)
            })
            
            # Analyze sentiment if not provided
            if feedback.sentiment == FeedbackSentiment.NEUTRAL:
                feedback.sentiment = self._analyze_sentiment(feedback.content)
            
            # Apply processing rules
            await self._apply_processing_rules(feedback)
            
            # Call registered handlers
            await self._call_handlers(feedback)
            
            # Store feedback
            self.feedback_history.append(feedback)
            self._update_statistics(feedback)
            
            # Generate auto-response if enabled
            if self.auto_response_enabled and feedback.response_required:
                await self._generate_auto_response(feedback)
            
            # Mark as processed
            feedback.processed = True
            
            logger.info(f"Processed feedback {feedback.id} from {feedback.sender_id}")
            return feedback
            
        except Exception as e:
            logger.error(f"Error processing feedback message {message.id}: {e}")
            return None
    
    def _analyze_sentiment(self, content: str) -> FeedbackSentiment:
        """Analyze sentiment of feedback content"""
        # Simple keyword-based sentiment analysis
        positive_keywords = ["good", "great", "excellent", "amazing", "helpful", "thank", "appreciate"]
        negative_keywords = ["bad", "terrible", "awful", "horrible", "hate", "problem", "issue", "bug"]
        
        content_lower = content.lower()
        positive_count = sum(1 for keyword in positive_keywords if keyword in content_lower)
        negative_count = sum(1 for keyword in negative_keywords if keyword in content_lower)
        
        if positive_count > negative_count:
            return FeedbackSentiment.POSITIVE
        elif negative_count > positive_count:
            return FeedbackSentiment.NEGATIVE
        else:
            return FeedbackSentiment.NEUTRAL
    
    async def _apply_processing_rules(self, feedback: FeedbackMessage):
        """Apply processing rules to feedback"""
        for rule in self.processing_rules:
            try:
                if rule["condition"](feedback):
                    await rule["action"](feedback)
                    rule["hit_count"] += 1
                    logger.debug(f"Applied processing rule {rule['id']} to feedback {feedback.id}")
            except Exception as e:
                logger.error(f"Error applying processing rule {rule['id']}: {e}")
    
    async def _call_handlers(self, feedback: FeedbackMessage):
        """Call registered handlers for feedback type"""
        handlers = self.feedback_handlers.get(feedback.feedback_type, [])
        
        for handler in handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(feedback)
                else:
                    handler(feedback)
            except Exception as e:
                logger.error(f"Error calling feedback handler: {e}")
    
    async def _generate_auto_response(self, feedback: FeedbackMessage):
        """Generate automatic response to feedback"""
        response_content = self._create_response_content(feedback)
        
        if response_content:
            await self.message_bus.send_message(
                message_type=MessageType.DIRECT,
                sender_id="feedback_processor",
                recipient_id=feedback.sender_id,
                content=response_content,
                data={
                    "response_to_feedback": feedback.id,
                    "auto_generated": True
                },
                priority=MessagePriority.NORMAL,
                correlation_id=feedback.id
            )
    
    def _create_response_content(self, feedback: FeedbackMessage) -> str:
        """Create response content based on feedback type and sentiment"""
        if feedback.feedback_type == FeedbackType.PRAISE:
            return f"Thank you for your positive feedback! We're glad to hear that you're happy with the service."
        
        elif feedback.feedback_type == FeedbackType.COMPLAINT:
            return f"Thank you for bringing this to our attention. We take your concerns seriously and will investigate this issue."
        
        elif feedback.feedback_type == FeedbackType.SUGGESTION:
            return f"Thank you for your suggestion. We appreciate your input and will consider it for future improvements."
        
        elif feedback.feedback_type == FeedbackType.BUG_REPORT:
            return f"Thank you for reporting this bug. We will investigate and work on fixing it as soon as possible."
        
        elif feedback.feedback_type == FeedbackType.FEATURE_REQUEST:
            return f"Thank you for your feature request. We'll add it to our backlog for consideration."
        
        else:
            return f"Thank you for your feedback. We value your input and will review it carefully."
    
    def _update_statistics(self, feedback: FeedbackMessage):
        """Update feedback statistics"""
        self.feedback_stats["total_feedback"] += 1
        
        # Update by type
        feedback_type = feedback.feedback_type.value
        if feedback_type not in self.feedback_stats["feedback_by_type"]:
            self.feedback_stats["feedback_by_type"][feedback_type] = 0
        self.feedback_stats["feedback_by_type"][feedback_type] += 1
        
        # Update by sentiment
        sentiment = feedback.sentiment.value
        if sentiment not in self.feedback_stats["feedback_by_sentiment"]:
            self.feedback_stats["feedback_by_sentiment"][sentiment] = 0
        self.feedback_stats["feedback_by_sentiment"][sentiment] += 1
        
        # Update average rating
        if feedback.rating is not None:
            rated_feedback = [f for f in self.feedback_history if f.rating is not None]
            if rated_feedback:
                total_rating = sum(f.rating for f in rated_feedback)
                self.feedback_stats["avg_rating"] = total_rating / len(rated_feedback)
    
    async def send_feedback(
        self,
        sender_id: str,
        feedback_type: FeedbackType,
        content: str,
        target_id: Optional[str] = None,
        recipient_id: Optional[str] = None,
        team_id: Optional[str] = None,
        rating: Optional[int] = None,
        tags: List[str] = None,
        context: Dict[str, Any] = None,
        response_required: bool = False
    ) -> str:
        """Send feedback message"""
        feedback_data = {
            "type": feedback_type.value,
            "target_id": target_id,
            "rating": rating,
            "tags": tags or [],
            "context": context or {},
            "response_required": response_required
        }
        
        message_id = await self.message_bus.send_message(
            message_type=MessageType.FEEDBACK,
            sender_id=sender_id,
            recipient_id=recipient_id,
            team_id=team_id,
            content=content,
            data={"feedback": feedback_data},
            priority=MessagePriority.NORMAL
        )
        
        return message_id
    
    def get_feedback_summary(
        self,
        feedback_type: Optional[FeedbackType] = None,
        target_id: Optional[str] = None,
        days: int = 30
    ) -> Dict[str, Any]:
        """Get feedback summary"""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # Filter feedback
        filtered_feedback = [
            f for f in self.feedback_history 
            if f.timestamp >= cutoff_date
        ]
        
        if feedback_type:
            filtered_feedback = [f for f in filtered_feedback if f.feedback_type == feedback_type]
        
        if target_id:
            filtered_feedback = [f for f in filtered_feedback if f.target_id == target_id]
        
        if not filtered_feedback:
            return {"total": 0, "summary": "No feedback found"}
        
        # Calculate statistics
        total_count = len(filtered_feedback)
        sentiment_counts = {}
        type_counts = {}
        ratings = []
        
        for feedback in filtered_feedback:
            # Count sentiments
            sentiment = feedback.sentiment.value
            sentiment_counts[sentiment] = sentiment_counts.get(sentiment, 0) + 1
            
            # Count types
            ftype = feedback.feedback_type.value
            type_counts[ftype] = type_counts.get(ftype, 0) + 1
            
            # Collect ratings
            if feedback.rating is not None:
                ratings.append(feedback.rating)
        
        # Calculate averages
        avg_rating = sum(ratings) / len(ratings) if ratings else None
        
        # Most common tags
        all_tags = []
        for feedback in filtered_feedback:
            all_tags.extend(feedback.tags)
        
        tag_counts = {}
        for tag in all_tags:
            tag_counts[tag] = tag_counts.get(tag, 0) + 1
        
        top_tags = sorted(tag_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        
        return {
            "total": total_count,
            "sentiment_distribution": sentiment_counts,
            "type_distribution": type_counts,
            "average_rating": avg_rating,
            "rating_count": len(ratings),
            "top_tags": top_tags,
            "period_days": days
        }
    
    def get_feedback_trends(self, days: int = 30) -> Dict[str, Any]:
        """Get feedback trends over time"""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # Group feedback by day
        daily_feedback = {}
        for feedback in self.feedback_history:
            if feedback.timestamp >= cutoff_date:
                day = feedback.timestamp.date().isoformat()
                if day not in daily_feedback:
                    daily_feedback[day] = {
                        "total": 0,
                        "positive": 0,
                        "negative": 0,
                        "neutral": 0
                    }
                
                daily_feedback[day]["total"] += 1
                daily_feedback[day][feedback.sentiment.value] += 1
        
        return {
            "daily_feedback": daily_feedback,
            "period_days": days
        }
    
    def get_agent_feedback(self, agent_id: str, days: int = 30) -> Dict[str, Any]:
        """Get feedback about a specific agent"""
        return self.get_feedback_summary(target_id=agent_id, days=days)
    
    def get_feedback_statistics(self) -> Dict[str, Any]:
        """Get overall feedback statistics"""
        return self.feedback_stats
    
    def create_feedback_processing_rules(self):
        """Create basic feedback processing rules"""
        # High-priority feedback rule
        self.add_processing_rule(
            "high_priority_feedback",
            lambda f: f.feedback_type in [FeedbackType.COMPLAINT, FeedbackType.BUG_REPORT] and f.rating is not None and f.rating <= 2,
            self._handle_high_priority_feedback,
            priority=10
        )
        
        # Positive feedback rule
        self.add_processing_rule(
            "positive_feedback",
            lambda f: f.sentiment == FeedbackSentiment.POSITIVE and f.rating is not None and f.rating >= 4,
            self._handle_positive_feedback,
            priority=5
        )
        
        # Feature request rule
        self.add_processing_rule(
            "feature_request",
            lambda f: f.feedback_type == FeedbackType.FEATURE_REQUEST,
            self._handle_feature_request,
            priority=3
        )
    
    async def _handle_high_priority_feedback(self, feedback: FeedbackMessage):
        """Handle high-priority feedback"""
        # Escalate to supervisor
        await self.message_bus.send_message(
            message_type=MessageType.ESCALATION,
            sender_id="feedback_processor",
            recipient_id="supervisor",
            content=f"High-priority feedback received: {feedback.content}",
            data={
                "feedback_id": feedback.id,
                "feedback_type": feedback.feedback_type.value,
                "rating": feedback.rating,
                "sender": feedback.sender_id,
                "target": feedback.target_id
            },
            priority=MessagePriority.HIGH
        )
    
    async def _handle_positive_feedback(self, feedback: FeedbackMessage):
        """Handle positive feedback"""
        # Log positive feedback
        logger.info(f"Positive feedback received: {feedback.content[:100]}...")
        
        # Could send recognition to target agent
        if feedback.target_id:
            await self.message_bus.send_message(
                message_type=MessageType.DIRECT,
                sender_id="feedback_processor",
                recipient_id=feedback.target_id,
                content=f"You received positive feedback: {feedback.content}",
                data={
                    "feedback_id": feedback.id,
                    "rating": feedback.rating,
                    "sender": feedback.sender_id
                },
                priority=MessagePriority.NORMAL
            )
    
    async def _handle_feature_request(self, feedback: FeedbackMessage):
        """Handle feature request"""
        # Log feature request
        logger.info(f"Feature request received: {feedback.content[:100]}...")
        
        # Could forward to product team
        await self.message_bus.send_message(
            message_type=MessageType.DIRECT,
            sender_id="feedback_processor",
            recipient_id="product_team",
            content=f"Feature request: {feedback.content}",
            data={
                "feedback_id": feedback.id,
                "sender": feedback.sender_id,
                "tags": feedback.tags
            },
            priority=MessagePriority.NORMAL
        )

class MessageBus:
    """
    Central message bus for inter-agent communication
    Handles message routing, queuing, and delivery with advanced queuing system
    """
    
    def __init__(self):
        # Enhanced message queues
        self.message_queues: Dict[str, MessageQueue] = {}  # agent_id -> message queue
        self.pending_messages: Dict[str, Message] = {}  # message_id -> message
        self.message_history: List[Message] = []
        
        # Queue management
        self.default_queue_size = 1000
        self.queue_cleanup_interval = 300  # 5 minutes
        
        # Handlers and routing
        self.handlers: Dict[str, MessageHandler] = {}  # handler_id -> handler
        self.agent_handlers: Dict[str, List[str]] = defaultdict(list)  # agent_id -> handler_ids
        self.type_handlers: Dict[MessageType, List[str]] = defaultdict(list)  # type -> handler_ids
        
        # Channels and topics
        self.channels: Dict[str, Set[str]] = defaultdict(set)  # channel -> agent_ids
        self.team_channels: Dict[str, str] = {}  # team_id -> channel_name
        
        # Statistics and monitoring
        self.stats = {
            "messages_sent": 0,
            "messages_delivered": 0,
            "messages_failed": 0,
            "total_handlers": 0,
            "active_channels": 0
        }
        
        # Configuration
        self.max_queue_size = self.default_queue_size
        self.max_message_age = timedelta(hours=1)
        self.max_history_size = 10000
        self.delivery_timeout = 30  # seconds
        
        # Event loop and tasks
        self.cleanup_task: Optional[asyncio.Task] = None
        self.delivery_task: Optional[asyncio.Task] = None
        self.running = False
        
        # Message persistence
        self.persistence_enabled = True
        self.db_path = "message_history.db"
        self.init_database()
        
        # Advanced routing and filtering
        self.routing_rules: List[Dict[str, Any]] = []
        self.content_filters: List[Callable[[Message], bool]] = []
        self.global_filters: List[Callable[[Message], bool]] = []
        
        # Escalation management
        self.escalation_manager = EscalationManager(self)
        self.escalation_manager.create_basic_escalation_rules()
        
        # Feedback processing
        self.feedback_processor = FeedbackProcessor(self)
        self.feedback_processor.create_feedback_processing_rules()
        
        logger.info("MessageBus initialized")
    
    async def start(self):
        """Start the message bus"""
        if self.running:
            return
        
        self.running = True
        
        # Start background tasks
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
        self.delivery_task = asyncio.create_task(self._delivery_loop())
        
        logger.info("MessageBus started")
    
    async def stop(self):
        """Stop the message bus"""
        if not self.running:
            return
        
        self.running = False
        
        # Cancel background tasks
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
        
        if self.delivery_task:
            self.delivery_task.cancel()
            try:
                await self.delivery_task
            except asyncio.CancelledError:
                pass
        
        logger.info("MessageBus stopped")
    
    def init_database(self):
        """Initialize the message history database"""
        if not self.persistence_enabled:
            return
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create messages table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS messages (
                    id TEXT PRIMARY KEY,
                    type TEXT NOT NULL,
                    sender_id TEXT NOT NULL,
                    recipient_id TEXT,
                    team_id TEXT,
                    content TEXT,
                    data TEXT,
                    priority TEXT,
                    timestamp TEXT NOT NULL,
                    expires_at TEXT,
                    status TEXT,
                    retry_count INTEGER,
                    max_retries INTEGER,
                    correlation_id TEXT,
                    reply_to TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create indexes
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_sender ON messages(sender_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_recipient ON messages(recipient_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_team ON messages(team_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_timestamp ON messages(timestamp)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_type ON messages(type)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_status ON messages(status)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_correlation ON messages(correlation_id)")
            
            # Create conversation threads table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS conversation_threads (
                    id TEXT PRIMARY KEY,
                    correlation_id TEXT NOT NULL,
                    participants TEXT NOT NULL,
                    thread_type TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_thread_correlation ON conversation_threads(correlation_id)")
            
            # Create message statistics table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS message_statistics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT NOT NULL,
                    agent_id TEXT,
                    team_id TEXT,
                    message_type TEXT,
                    count INTEGER DEFAULT 0,
                    total_size INTEGER DEFAULT 0,
                    avg_response_time REAL DEFAULT 0,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_stats_date ON message_statistics(date)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_stats_agent ON message_statistics(agent_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_stats_team ON message_statistics(team_id)")
            
            conn.commit()
            conn.close()
            
            logger.info("Message history database initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            self.persistence_enabled = False
    
    def persist_message(self, message: Message):
        """Persist a message to the database"""
        if not self.persistence_enabled:
            return
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT OR REPLACE INTO messages (
                    id, type, sender_id, recipient_id, team_id, content, data,
                    priority, timestamp, expires_at, status, retry_count,
                    max_retries, correlation_id, reply_to
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                message.id,
                message.type.value,
                message.sender_id,
                message.recipient_id,
                message.team_id,
                message.content,
                json.dumps(message.data),
                message.priority.value,
                message.timestamp.isoformat(),
                message.expires_at.isoformat() if message.expires_at else None,
                message.status.value,
                message.retry_count,
                message.max_retries,
                message.correlation_id,
                message.reply_to
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Failed to persist message {message.id}: {e}")
    
    def get_message_history(
        self,
        agent_id: Optional[str] = None,
        team_id: Optional[str] = None,
        message_type: Optional[MessageType] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 100
    ) -> List[Message]:
        """Get message history from database"""
        if not self.persistence_enabled:
            return []
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            query = "SELECT * FROM messages WHERE 1=1"
            params = []
            
            if agent_id:
                query += " AND (sender_id = ? OR recipient_id = ?)"
                params.extend([agent_id, agent_id])
            
            if team_id:
                query += " AND team_id = ?"
                params.append(team_id)
            
            if message_type:
                query += " AND type = ?"
                params.append(message_type.value)
            
            if start_time:
                query += " AND timestamp >= ?"
                params.append(start_time.isoformat())
            
            if end_time:
                query += " AND timestamp <= ?"
                params.append(end_time.isoformat())
            
            query += " ORDER BY timestamp DESC LIMIT ?"
            params.append(limit)
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            messages = []
            for row in rows:
                message = Message(
                    id=row[0],
                    type=MessageType(row[1]),
                    sender_id=row[2],
                    recipient_id=row[3],
                    team_id=row[4],
                    content=row[5],
                    data=json.loads(row[6]) if row[6] else {},
                    priority=MessagePriority(row[7]),
                    timestamp=datetime.fromisoformat(row[8]),
                    expires_at=datetime.fromisoformat(row[9]) if row[9] else None,
                    status=MessageStatus(row[10]),
                    retry_count=row[11],
                    max_retries=row[12],
                    correlation_id=row[13],
                    reply_to=row[14]
                )
                messages.append(message)
            
            conn.close()
            return messages
            
        except Exception as e:
            logger.error(f"Failed to get message history: {e}")
            return []
    
    def get_conversation_history(
        self,
        correlation_id: str,
        include_related: bool = True
    ) -> List[Message]:
        """Get conversation history by correlation ID"""
        if not self.persistence_enabled:
            return []
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            query = """
                SELECT * FROM messages 
                WHERE correlation_id = ? OR reply_to = ?
                ORDER BY timestamp ASC
            """
            
            if include_related:
                # Also get messages that are replies to any message in this conversation
                query = """
                    SELECT * FROM messages 
                    WHERE correlation_id = ? 
                    OR reply_to = ?
                    OR correlation_id IN (
                        SELECT correlation_id FROM messages 
                        WHERE correlation_id = ? OR reply_to = ?
                    )
                    ORDER BY timestamp ASC
                """
                cursor.execute(query, [correlation_id, correlation_id, correlation_id, correlation_id])
            else:
                cursor.execute(query, [correlation_id, correlation_id])
            
            rows = cursor.fetchall()
            
            messages = []
            for row in rows:
                message = Message(
                    id=row[0],
                    type=MessageType(row[1]),
                    sender_id=row[2],
                    recipient_id=row[3],
                    team_id=row[4],
                    content=row[5],
                    data=json.loads(row[6]) if row[6] else {},
                    priority=MessagePriority(row[7]),
                    timestamp=datetime.fromisoformat(row[8]),
                    expires_at=datetime.fromisoformat(row[9]) if row[9] else None,
                    status=MessageStatus(row[10]),
                    retry_count=row[11],
                    max_retries=row[12],
                    correlation_id=row[13],
                    reply_to=row[14]
                )
                messages.append(message)
            
            conn.close()
            return messages
            
        except Exception as e:
            logger.error(f"Failed to get conversation history: {e}")
            return []
    
    def get_message_statistics(
        self,
        agent_id: Optional[str] = None,
        team_id: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get message statistics"""
        if not self.persistence_enabled:
            return {}
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Base query conditions
            conditions = []
            params = []
            
            if agent_id:
                conditions.append("(sender_id = ? OR recipient_id = ?)")
                params.extend([agent_id, agent_id])
            
            if team_id:
                conditions.append("team_id = ?")
                params.append(team_id)
            
            if start_date:
                conditions.append("timestamp >= ?")
                params.append(start_date.isoformat())
            
            if end_date:
                conditions.append("timestamp <= ?")
                params.append(end_date.isoformat())
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            # Total messages
            cursor.execute(f"SELECT COUNT(*) FROM messages WHERE {where_clause}", params)
            total_messages = cursor.fetchone()[0]
            
            # Messages by type
            cursor.execute(f"""
                SELECT type, COUNT(*) FROM messages 
                WHERE {where_clause} 
                GROUP BY type
            """, params)
            messages_by_type = dict(cursor.fetchall())
            
            # Messages by status
            cursor.execute(f"""
                SELECT status, COUNT(*) FROM messages 
                WHERE {where_clause} 
                GROUP BY status
            """, params)
            messages_by_status = dict(cursor.fetchall())
            
            # Messages by priority
            cursor.execute(f"""
                SELECT priority, COUNT(*) FROM messages 
                WHERE {where_clause} 
                GROUP BY priority
            """, params)
            messages_by_priority = dict(cursor.fetchall())
            
            # Top senders
            cursor.execute(f"""
                SELECT sender_id, COUNT(*) FROM messages 
                WHERE {where_clause} 
                GROUP BY sender_id 
                ORDER BY COUNT(*) DESC 
                LIMIT 10
            """, params)
            top_senders = dict(cursor.fetchall())
            
            # Top recipients
            cursor.execute(f"""
                SELECT recipient_id, COUNT(*) FROM messages 
                WHERE {where_clause} AND recipient_id IS NOT NULL 
                GROUP BY recipient_id 
                ORDER BY COUNT(*) DESC 
                LIMIT 10
            """, params)
            top_recipients = dict(cursor.fetchall())
            
            conn.close()
            
            return {
                "total_messages": total_messages,
                "messages_by_type": messages_by_type,
                "messages_by_status": messages_by_status,
                "messages_by_priority": messages_by_priority,
                "top_senders": top_senders,
                "top_recipients": top_recipients
            }
            
        except Exception as e:
            logger.error(f"Failed to get message statistics: {e}")
            return {}
    
    def cleanup_old_messages(self, older_than: timedelta):
        """Clean up old messages from the database"""
        if not self.persistence_enabled:
            return
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cutoff_time = datetime.utcnow() - older_than
            
            cursor.execute("""
                DELETE FROM messages 
                WHERE timestamp < ?
            """, (cutoff_time.isoformat(),))
            
            deleted_count = cursor.rowcount
            
            conn.commit()
            conn.close()
            
            logger.info(f"Cleaned up {deleted_count} old messages")
            
        except Exception as e:
            logger.error(f"Failed to cleanup old messages: {e}")
    
    def export_message_history(
        self,
        output_file: str,
        format: str = "json",
        agent_id: Optional[str] = None,
        team_id: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> bool:
        """Export message history to file"""
        if not self.persistence_enabled:
            return False
        
        try:
            messages = self.get_message_history(
                agent_id=agent_id,
                team_id=team_id,
                start_time=start_time,
                end_time=end_time,
                limit=10000  # Large limit for export
            )
            
            if format.lower() == "json":
                with open(output_file, 'w') as f:
                    json.dump([msg.to_dict() for msg in messages], f, indent=2)
            elif format.lower() == "csv":
                import csv
                with open(output_file, 'w', newline='') as f:
                    writer = csv.writer(f)
                    writer.writerow([
                        'id', 'type', 'sender_id', 'recipient_id', 'team_id', 
                        'content', 'priority', 'timestamp', 'status'
                    ])
                    for msg in messages:
                        writer.writerow([
                            msg.id, msg.type.value, msg.sender_id, msg.recipient_id,
                            msg.team_id, msg.content, msg.priority.value,
                            msg.timestamp.isoformat(), msg.status.value
                        ])
            else:
                logger.error(f"Unsupported export format: {format}")
                return False
            
            logger.info(f"Exported {len(messages)} messages to {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to export message history: {e}")
            return False
    
    async def send_message(
        self,
        message_type: MessageType,
        sender_id: str,
        content: str,
        recipient_id: Optional[str] = None,
        team_id: Optional[str] = None,
        data: Optional[Dict[str, Any]] = None,
        priority: MessagePriority = MessagePriority.NORMAL,
        expires_in: Optional[timedelta] = None,
        correlation_id: Optional[str] = None,
        reply_to: Optional[str] = None
    ) -> str:
        """
        Send a message through the message bus
        
        Returns:
            Message ID
        """
        message_id = str(uuid.uuid4())
        
        message = Message(
            id=message_id,
            type=message_type,
            sender_id=sender_id,
            recipient_id=recipient_id,
            team_id=team_id,
            content=content,
            data=data or {},
            priority=priority,
            expires_at=datetime.utcnow() + expires_in if expires_in else None,
            correlation_id=correlation_id,
            reply_to=reply_to
        )
        
        # Store message
        self.pending_messages[message_id] = message
        
        # Apply global filters
        if not self._apply_global_filters(message):
            logger.debug(f"Message {message_id} filtered out by global filters")
            return message_id
        
        # Route message
        await self._route_message(message)
        
        # Update statistics
        self.stats["messages_sent"] += 1
        
        # Add to history
        self.message_history.append(message)
        if len(self.message_history) > self.max_history_size:
            self.message_history.pop(0)
        
        # Persist message to database
        self.persist_message(message)
        
        # Check for escalation
        await self.escalation_manager.check_escalation(message)
        
        # Process feedback if this is a feedback message
        if message.type == MessageType.FEEDBACK:
            await self.feedback_processor.process_feedback(message)
        
        logger.debug(f"Message sent: {message_id} from {sender_id} to {recipient_id or 'broadcast'}")
        
        return message_id
    
    def _apply_global_filters(self, message: Message) -> bool:
        """Apply global message filters"""
        for filter_func in self.global_filters:
            try:
                if not filter_func(message):
                    return False
            except Exception as e:
                logger.error(f"Global filter error: {e}")
                # Continue processing if filter fails
        return True
    
    def add_global_filter(self, filter_func: Callable[[Message], bool]):
        """Add a global message filter"""
        self.global_filters.append(filter_func)
        logger.info("Added global message filter")
    
    def remove_global_filter(self, filter_func: Callable[[Message], bool]):
        """Remove a global message filter"""
        if filter_func in self.global_filters:
            self.global_filters.remove(filter_func)
            logger.info("Removed global message filter")
    
    def add_content_filter(self, filter_func: Callable[[Message], bool]):
        """Add a content-based message filter"""
        self.content_filters.append(filter_func)
        logger.info("Added content message filter")
    
    def remove_content_filter(self, filter_func: Callable[[Message], bool]):
        """Remove a content-based message filter"""
        if filter_func in self.content_filters:
            self.content_filters.remove(filter_func)
            logger.info("Removed content message filter")
    
    def add_routing_rule(
        self,
        rule_id: str,
        condition: Callable[[Message], bool],
        action: Callable[[Message], None],
        priority: int = 0,
        description: str = ""
    ):
        """Add a routing rule"""
        rule = {
            "id": rule_id,
            "condition": condition,
            "action": action,
            "priority": priority,
            "description": description,
            "created_at": datetime.utcnow(),
            "hit_count": 0
        }
        
        self.routing_rules.append(rule)
        self.routing_rules.sort(key=lambda x: x["priority"], reverse=True)
        
        logger.info(f"Added routing rule {rule_id} with priority {priority}")
    
    def remove_routing_rule(self, rule_id: str) -> bool:
        """Remove a routing rule"""
        for i, rule in enumerate(self.routing_rules):
            if rule["id"] == rule_id:
                del self.routing_rules[i]
                logger.info(f"Removed routing rule {rule_id}")
                return True
        return False
    
    def get_routing_rules(self) -> List[Dict[str, Any]]:
        """Get all routing rules"""
        return [
            {
                "id": rule["id"],
                "priority": rule["priority"],
                "description": rule["description"],
                "created_at": rule["created_at"].isoformat(),
                "hit_count": rule["hit_count"]
            }
            for rule in self.routing_rules
        ]
    
    async def _apply_routing_rules(self, message: Message):
        """Apply routing rules to a message"""
        for rule in self.routing_rules:
            try:
                if rule["condition"](message):
                    rule["hit_count"] += 1
                    await rule["action"](message)
                    logger.debug(f"Applied routing rule {rule['id']} to message {message.id}")
            except Exception as e:
                logger.error(f"Routing rule {rule['id']} error: {e}")
    
    def create_spam_filter(self, keywords: List[str], threshold: int = 5) -> Callable[[Message], bool]:
        """Create a spam filter based on keywords"""
        def spam_filter(message: Message) -> bool:
            content_lower = message.content.lower()
            keyword_count = sum(1 for keyword in keywords if keyword.lower() in content_lower)
            return keyword_count < threshold
        
        return spam_filter
    
    def create_priority_filter(self, min_priority: MessagePriority) -> Callable[[Message], bool]:
        """Create a priority filter"""
        priority_order = {
            MessagePriority.LOW: 0,
            MessagePriority.NORMAL: 1,
            MessagePriority.HIGH: 2,
            MessagePriority.URGENT: 3,
            MessagePriority.CRITICAL: 4
        }
        
        def priority_filter(message: Message) -> bool:
            return priority_order.get(message.priority, 0) >= priority_order.get(min_priority, 0)
        
        return priority_filter
    
    def create_sender_filter(self, allowed_senders: List[str]) -> Callable[[Message], bool]:
        """Create a sender filter"""
        def sender_filter(message: Message) -> bool:
            return message.sender_id in allowed_senders
        
        return sender_filter
    
    def create_content_length_filter(self, max_length: int) -> Callable[[Message], bool]:
        """Create a content length filter"""
        def length_filter(message: Message) -> bool:
            return len(message.content) <= max_length
        
        return length_filter
    
    def create_rate_limit_filter(self, sender_id: str, max_messages: int, time_window: timedelta) -> Callable[[Message], bool]:
        """Create a rate limiting filter for a specific sender"""
        message_times = []
        
        def rate_limit_filter(message: Message) -> bool:
            if message.sender_id != sender_id:
                return True
            
            now = datetime.utcnow()
            # Remove old messages outside the time window
            cutoff = now - time_window
            message_times[:] = [t for t in message_times if t > cutoff]
            
            if len(message_times) >= max_messages:
                return False
            
            message_times.append(now)
            return True
        
        return rate_limit_filter
    
    def create_duplicate_filter(self, time_window: timedelta = timedelta(minutes=1)) -> Callable[[Message], bool]:
        """Create a duplicate message filter"""
        recent_messages = {}
        
        def duplicate_filter(message: Message) -> bool:
            now = datetime.utcnow()
            key = f"{message.sender_id}:{message.content}"
            
            if key in recent_messages:
                last_time = recent_messages[key]
                if now - last_time < time_window:
                    return False
            
            recent_messages[key] = now
            
            # Clean up old entries
            cutoff = now - time_window
            recent_messages.clear()
            for k, v in list(recent_messages.items()):
                if v < cutoff:
                    del recent_messages[k]
            
            return True
        
        return duplicate_filter
    
    def create_regex_filter(self, pattern: str, allow_match: bool = True) -> Callable[[Message], bool]:
        """Create a regex-based content filter"""
        import re
        compiled_pattern = re.compile(pattern, re.IGNORECASE)
        
        def regex_filter(message: Message) -> bool:
            match = compiled_pattern.search(message.content)
            return bool(match) == allow_match
        
        return regex_filter
    
    async def _route_message(self, message: Message):
        """Route message to appropriate handlers with filtering and rules"""
        try:
            # Apply routing rules first
            await self._apply_routing_rules(message)
            
            # Apply content filters
            for filter_func in self.content_filters:
                try:
                    if not filter_func(message):
                        logger.debug(f"Message {message.id} filtered out by content filter")
                        return
                except Exception as e:
                    logger.error(f"Content filter error: {e}")
            
            # Route based on message type
            if message.type == MessageType.DIRECT:
                await self._route_direct_message(message)
            elif message.type == MessageType.BROADCAST:
                await self._route_broadcast_message(message)
            elif message.type == MessageType.TEAM:
                await self._route_team_message(message)
            else:
                await self._route_generic_message(message)
            
            message.status = MessageStatus.SENT
            
        except Exception as e:
            logger.error(f"Failed to route message {message.id}: {e}")
            message.status = MessageStatus.FAILED
            self.stats["messages_failed"] += 1
    
    async def _route_direct_message(self, message: Message):
        """Route direct message to specific recipient"""
        if not message.recipient_id:
            raise ValueError("Direct message requires recipient_id")
        
        # Add to recipient's queue
        self._add_to_queue(message.recipient_id, message)
        
        # Notify handlers
        await self._notify_handlers(message)
    
    async def _route_broadcast_message(self, message: Message):
        """Route broadcast message to all agents"""
        # Add to all agent queues
        for agent_id in self.agent_handlers:
            if agent_id != message.sender_id:  # Don't send to sender
                self._add_to_queue(agent_id, message)
        
        # Notify handlers
        await self._notify_handlers(message)
    
    async def _route_team_message(self, message: Message):
        """Route team message to team members"""
        if not message.team_id:
            raise ValueError("Team message requires team_id")
        
        # Get team channel
        channel_name = self.team_channels.get(message.team_id)
        if not channel_name:
            raise ValueError(f"No channel found for team {message.team_id}")
        
        # Get team members
        team_members = self.channels.get(channel_name, set())
        
        # Add to team member queues
        for agent_id in team_members:
            if agent_id != message.sender_id:  # Don't send to sender
                self._add_to_queue(agent_id, message)
        
        # Notify handlers
        await self._notify_handlers(message)
    
    async def _route_generic_message(self, message: Message):
        """Route generic message to appropriate handlers"""
        # Find handlers for this message type
        handler_ids = self.type_handlers.get(message.type, [])
        
        for handler_id in handler_ids:
            handler = self.handlers.get(handler_id)
            if handler and handler.active:
                # Check filter
                if handler.filter_func and not handler.filter_func(message):
                    continue
                
                # Add to handler's agent queue
                self._add_to_queue(handler.agent_id, message)
        
        # Notify handlers
        await self._notify_handlers(message)
    
    def _add_to_queue(self, agent_id: str, message: Message):
        """Add message to agent's queue"""
        # Create queue if it doesn't exist
        if agent_id not in self.message_queues:
            self.message_queues[agent_id] = MessageQueue(agent_id, self.max_queue_size)
        
        queue = self.message_queues[agent_id]
        
        # Add message to queue
        if not queue.enqueue(message):
            logger.warning(f"Failed to enqueue message for agent {agent_id}")
        
        # Add agent as subscriber
        queue.add_subscriber(agent_id)
    
    async def _notify_handlers(self, message: Message):
        """Notify appropriate handlers about the message"""
        # Find handlers for this message type
        handler_ids = self.type_handlers.get(message.type, [])
        
        for handler_id in handler_ids:
            handler = self.handlers.get(handler_id)
            if handler and handler.active:
                # Check filter
                if handler.filter_func and not handler.filter_func(message):
                    continue
                
                try:
                    # Call handler callback
                    if asyncio.iscoroutinefunction(handler.callback):
                        await handler.callback(message)
                    else:
                        handler.callback(message)
                except Exception as e:
                    logger.error(f"Handler {handler_id} failed: {e}")
    
    async def get_messages(self, agent_id: str, limit: int = 50) -> List[Message]:
        """Get messages for an agent"""
        if agent_id not in self.message_queues:
            return []
        
        queue = self.message_queues[agent_id]
        messages = []
        
        # Get messages up to limit
        for _ in range(min(limit, queue.size())):
            message = queue.dequeue()
            if message:
                # Mark message as delivered
                if message.status == MessageStatus.SENT:
                    message.status = MessageStatus.DELIVERED
                    self.stats["messages_delivered"] += 1
                messages.append(message)
        
        return messages
    
    async def mark_message_read(self, message_id: str, agent_id: str) -> bool:
        """Mark a message as read by an agent"""
        message = self.pending_messages.get(message_id)
        if not message:
            return False
        
        if message.recipient_id == agent_id or message.type in [MessageType.BROADCAST, MessageType.TEAM]:
            message.status = MessageStatus.READ
            return True
        
        return False
    
    def register_handler(
        self,
        handler_id: str,
        agent_id: str,
        message_types: List[MessageType],
        callback: Callable[[Message], None],
        filter_func: Optional[Callable[[Message], bool]] = None,
        priority: int = 0
    ) -> bool:
        """Register a message handler"""
        try:
            handler = MessageHandler(
                handler_id=handler_id,
                agent_id=agent_id,
                message_types=set(message_types),
                callback=callback,
                filter_func=filter_func,
                priority=priority
            )
            
            self.handlers[handler_id] = handler
            self.agent_handlers[agent_id].append(handler_id)
            
            # Add to type handlers
            for msg_type in message_types:
                self.type_handlers[msg_type].append(handler_id)
            
            self.stats["total_handlers"] += 1
            
            logger.info(f"Registered handler {handler_id} for agent {agent_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to register handler {handler_id}: {e}")
            return False
    
    def unregister_handler(self, handler_id: str) -> bool:
        """Unregister a message handler"""
        try:
            handler = self.handlers.pop(handler_id, None)
            if not handler:
                return False
            
            # Remove from agent handlers
            if handler.agent_id in self.agent_handlers:
                self.agent_handlers[handler.agent_id].remove(handler_id)
            
            # Remove from type handlers
            for msg_type in handler.message_types:
                if handler_id in self.type_handlers[msg_type]:
                    self.type_handlers[msg_type].remove(handler_id)
            
            self.stats["total_handlers"] -= 1
            
            logger.info(f"Unregistered handler {handler_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unregister handler {handler_id}: {e}")
            return False
    
    def create_channel(self, channel_name: str, agent_ids: List[str]) -> bool:
        """Create a communication channel"""
        try:
            self.channels[channel_name] = set(agent_ids)
            self.stats["active_channels"] += 1
            
            logger.info(f"Created channel {channel_name} with {len(agent_ids)} members")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create channel {channel_name}: {e}")
            return False
    
    def join_channel(self, channel_name: str, agent_id: str) -> bool:
        """Add agent to a channel"""
        try:
            if channel_name not in self.channels:
                self.channels[channel_name] = set()
            
            self.channels[channel_name].add(agent_id)
            
            logger.info(f"Agent {agent_id} joined channel {channel_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to join channel {channel_name}: {e}")
            return False
    
    def leave_channel(self, channel_name: str, agent_id: str) -> bool:
        """Remove agent from a channel"""
        try:
            if channel_name in self.channels:
                self.channels[channel_name].discard(agent_id)
                
                # Remove empty channels
                if not self.channels[channel_name]:
                    del self.channels[channel_name]
                    self.stats["active_channels"] -= 1
            
            logger.info(f"Agent {agent_id} left channel {channel_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to leave channel {channel_name}: {e}")
            return False
    
    def set_team_channel(self, team_id: str, channel_name: str) -> bool:
        """Set the channel for a team"""
        try:
            self.team_channels[team_id] = channel_name
            
            logger.info(f"Set team {team_id} channel to {channel_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to set team channel: {e}")
            return False
    
    async def clear_messages(self, agent_id: str) -> bool:
        """Clear all messages for an agent"""
        try:
            if agent_id in self.message_queues:
                self.message_queues[agent_id].clear()
            
            logger.info(f"Cleared messages for agent {agent_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to clear messages for agent {agent_id}: {e}")
            return False
    
    def create_queue(self, queue_id: str, max_size: int = None) -> MessageQueue:
        """Create a new message queue"""
        if queue_id in self.message_queues:
            return self.message_queues[queue_id]
        
        queue = MessageQueue(queue_id, max_size or self.default_queue_size)
        self.message_queues[queue_id] = queue
        
        logger.info(f"Created message queue {queue_id}")
        return queue
    
    def delete_queue(self, queue_id: str) -> bool:
        """Delete a message queue"""
        if queue_id in self.message_queues:
            del self.message_queues[queue_id]
            logger.info(f"Deleted message queue {queue_id}")
            return True
        return False
    
    def get_queue(self, queue_id: str) -> Optional[MessageQueue]:
        """Get a message queue by ID"""
        return self.message_queues.get(queue_id)
    
    def add_queue_filter(self, queue_id: str, filter_func: Callable[[Message], bool]) -> bool:
        """Add a filter to a message queue"""
        if queue_id in self.message_queues:
            self.message_queues[queue_id].add_filter(filter_func)
            return True
        return False
    
    def remove_queue_filter(self, queue_id: str, filter_func: Callable[[Message], bool]) -> bool:
        """Remove a filter from a message queue"""
        if queue_id in self.message_queues:
            self.message_queues[queue_id].remove_filter(filter_func)
            return True
        return False
    
    def get_queue_stats(self, queue_id: str) -> Optional[Dict[str, Any]]:
        """Get statistics for a specific queue"""
        if queue_id in self.message_queues:
            return self.message_queues[queue_id].get_stats()
        return None
    
    def list_queues(self) -> List[str]:
        """List all queue IDs"""
        return list(self.message_queues.keys())
    
    def peek_queue(self, queue_id: str) -> Optional[Message]:
        """Peek at the next message in a queue without removing it"""
        if queue_id in self.message_queues:
            return self.message_queues[queue_id].peek()
        return None
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get message bus statistics"""
        queue_stats = {}
        total_queue_size = 0
        
        for queue_id, queue in self.message_queues.items():
            stats = queue.get_stats()
            queue_stats[queue_id] = stats
            total_queue_size += stats["size"]
        
        return {
            **self.stats,
            "pending_messages": len(self.pending_messages),
            "history_size": len(self.message_history),
            "active_agents": len(self.agent_handlers),
            "total_queues": len(self.message_queues),
            "total_queue_size": total_queue_size,
            "queue_statistics": queue_stats
        }
    
    async def _cleanup_loop(self):
        """Background task for cleanup"""
        while self.running:
            try:
                await self._cleanup_expired_messages()
                await asyncio.sleep(60)  # Run every minute
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Cleanup loop error: {e}")
                await asyncio.sleep(5)
    
    async def _cleanup_expired_messages(self):
        """Clean up expired messages"""
        now = datetime.utcnow()
        expired_messages = []
        
        # Find expired messages
        for message_id, message in self.pending_messages.items():
            if message.expires_at and message.expires_at < now:
                expired_messages.append(message_id)
            elif now - message.timestamp > self.max_message_age:
                expired_messages.append(message_id)
        
        # Remove expired messages
        for message_id in expired_messages:
            message = self.pending_messages.pop(message_id, None)
            if message:
                message.status = MessageStatus.EXPIRED
                logger.debug(f"Expired message {message_id}")
    
    async def _delivery_loop(self):
        """Background task for message delivery"""
        while self.running:
            try:
                await self._retry_failed_messages()
                await asyncio.sleep(30)  # Run every 30 seconds
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Delivery loop error: {e}")
                await asyncio.sleep(5)
    
    async def _retry_failed_messages(self):
        """Retry failed messages"""
        failed_messages = [
            msg for msg in self.pending_messages.values()
            if msg.status == MessageStatus.FAILED and msg.retry_count < msg.max_retries
        ]
        
        for message in failed_messages:
            message.retry_count += 1
            message.status = MessageStatus.PENDING
            
            try:
                await self._route_message(message)
                logger.debug(f"Retried message {message.id} (attempt {message.retry_count})")
            except Exception as e:
                logger.error(f"Failed to retry message {message.id}: {e}")
                message.status = MessageStatus.FAILED

# Global message bus instance
message_bus = MessageBus()