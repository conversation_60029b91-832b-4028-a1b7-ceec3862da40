# Core AutoGen dependencies
pyautogen>=0.2.0
openai>=1.0.0
google-generativeai>=0.3.0
anthropic>=0.7.0

# Web framework
fastapi>=0.104.0
uvicorn>=0.24.0
pydantic>=2.5.0

# HTTP client
httpx>=0.25.0
requests>=2.31.0

# Async support
asyncio-mqtt>=0.13.0

# Security & validation
passlib[bcrypt]>=1.7.4

# Configuration
python-dotenv>=1.0.0
pydantic-settings>=2.1.0

# Logging & monitoring
structlog>=23.2.0
prometheus-client>=0.19.0

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-mock>=3.12.0
httpx-mock>=0.7.0

# Development
black>=23.11.0
isort>=5.12.0
mypy>=1.7.0
pre-commit>=3.6.0

# Rate limiting & caching
slowapi>=0.1.9
redis>=5.0.0

# Data handling
pydantic-core>=2.14.0
typing-extensions>=4.8.0

# Database & persistence
aiosqlite>=0.19.0

# Cryptography & security
cryptography>=41.0.0
python-jose[cryptography]>=3.3.0