"""
Additional Agent implementation for Metamorphic Reactor
Provides supplementary analysis and alternative perspectives
"""

import logging
from typing import List
from datetime import datetime

from .base_agent import BaseAgent
from .agent_types import AgentConfig, AgentResponse, TaskContext, ConversationHistory

logger = logging.getLogger(__name__)

class AdditionalAgent(BaseAgent):
    """
    Additional Agent - Provides supplementary analysis and alternative perspectives
    
    Responsibilities:
    - Offer alternative approaches and solutions
    - Provide domain-specific expertise and insights
    - Identify overlooked considerations and opportunities
    - Support consensus building through mediation
    - Enhance decision-making with diverse perspectives
    """
    
    def __init__(self, config: AgentConfig):
        super().__init__(config)
        self.analysis_perspectives = {
            "alternative_approaches": self._analyze_alternatives,
            "domain_expertise": self._provide_domain_insights,
            "opportunity_identification": self._identify_opportunities,
            "consensus_mediation": self._mediate_consensus,
            "risk_mitigation": self._additional_risk_analysis
        }
    
    async def generate_response(
        self, 
        task_context: TaskContext, 
        conversation_history: ConversationHistory,
        current_proposals: List[AgentResponse]
    ) -> AgentResponse:
        """Generate supplementary analysis and alternative perspectives"""
        
        logger.info(f"Additional agent {self.agent_id} providing supplementary analysis")
        
        # Analyze current conversation state
        planner_proposals = [p for p in current_proposals if p.role.value == "planner"]
        critic_proposals = [p for p in current_proposals if p.role.value == "critic"]
        
        # Generate response based on conversation state
        if not planner_proposals:
            # No plan yet - provide initial perspectives
            analysis_content = self._generate_initial_perspective(task_context)
            consensus_score = 1.0  # Neutral agreement when no proposals exist
            agrees = True
        elif not critic_proposals:
            # Plan exists but no critique - provide additional analysis
            analysis_content = await self._analyze_plan_alternatives(
                planner_proposals[-1], task_context, current_proposals
            )
            consensus_score = self._assess_plan_viability(planner_proposals[-1])
            agrees = consensus_score > 0.6
        else:
            # Both plan and critique exist - provide mediation and synthesis
            analysis_content = await self._synthesize_and_mediate(
                planner_proposals[-1], critic_proposals[-1], task_context
            )
            consensus_score = self._calculate_mediation_consensus(planner_proposals, critic_proposals)
            agrees = consensus_score > 0.5
        
        response = AgentResponse(
            agent_id=self.agent_id,
            session_id=self.session_id,
            role=self.config.role,
            message_id=f"additional_{int(datetime.utcnow().timestamp())}",
            content=analysis_content,
            reasoning=self._get_analysis_reasoning(current_proposals),
            confidence=self._calculate_analysis_confidence(current_proposals),
            agrees_with_proposal=agrees,
            consensus_score=consensus_score,
            suggested_changes=self._generate_synthesis_suggestions(current_proposals),
            token_count=len(analysis_content.split()),
            response_time_ms=0  # Will be set by base class
        )
        
        return response
    
    def get_role_prompt(self, task_context: TaskContext) -> str:
        """Get additional agent-specific system prompt"""
        
        return f"""You are an expert Additional Agent in the Metamorphic Reactor system.

Your role is to provide supplementary analysis, alternative perspectives, and consensus mediation.

TASK: {task_context.description}

RESPONSIBILITIES:
1. Offer alternative approaches and creative solutions
2. Provide domain-specific insights and expertise
3. Identify overlooked opportunities and considerations
4. Mediate between different perspectives for consensus
5. Enhance analysis quality through diverse viewpoints

ANALYSIS FOCUS AREAS:
- Alternative implementation strategies and methodologies
- Domain-specific best practices and innovations
- Scalability, maintainability, and future-proofing
- Cost-benefit analysis and optimization opportunities
- Integration possibilities and ecosystem considerations
- User experience and stakeholder impact

MEDIATION PRINCIPLES:
- Find common ground between different perspectives
- Propose compromises and hybrid solutions
- Highlight synergies and complementary approaches
- Support evidence-based decision making
- Foster collaborative consensus building

CONTEXT FILES: {', '.join(task_context.context_files) if task_context.context_files else 'None'}

Current round: {task_context.current_round}/{task_context.max_rounds}

Provide valuable supplementary insights and support consensus building."""
    
    def _generate_initial_perspective(self, task_context: TaskContext) -> str:
        """Generate initial perspective when no proposals exist"""
        
        return f"""# ADDITIONAL ANALYSIS - INITIAL PERSPECTIVE

## Task Overview
**Task**: {task_context.description}

## Alternative Approach Considerations

### Methodology Alternatives
Based on the task description, I identify several potential implementation approaches worth considering:

#### Approach 1: Incremental Development
- Start with minimal viable solution
- Iteratively add features and complexity
- Benefits: Early validation, reduced risk, faster feedback
- Considerations: May require refactoring, potential technical debt

#### Approach 2: Comprehensive Planning
- Thorough upfront analysis and design
- Complete specification before implementation
- Benefits: Reduced rework, clearer scope, better quality
- Considerations: Longer planning phase, potential over-engineering

#### Approach 3: Hybrid Methodology
- Combine planning and incremental approaches
- Plan core architecture, implement incrementally
- Benefits: Balance of structure and flexibility
- Considerations: Requires careful coordination

### Domain-Specific Considerations

#### Technology Perspective
- Evaluate emerging technologies and frameworks
- Consider compatibility and integration requirements
- Assess long-term viability and support

#### User Experience Perspective
- Focus on end-user needs and workflows
- Consider accessibility and usability requirements
- Plan for user feedback and iteration

#### Business Perspective
- Analyze cost-benefit implications
- Consider market timing and competitive factors
- Evaluate resource allocation and ROI

## Opportunity Areas
- Innovation potential and differentiation
- Scalability and growth opportunities
- Integration and ecosystem benefits
- Knowledge transfer and capability building

## Questions for Consideration
1. What are the primary success criteria and constraints?
2. Are there existing solutions or patterns to leverage?
3. What are the long-term maintenance and evolution plans?
4. How will success be measured and validated?

**Awaiting planner proposal for detailed alternative analysis...**"""
    
    async def _analyze_plan_alternatives(
        self, 
        plan: AgentResponse, 
        task_context: TaskContext,
        all_proposals: List[AgentResponse]
    ) -> str:
        """Analyze plan and provide alternative approaches"""
        
        # Extract key elements from the plan
        plan_content = plan.content
        
        # Run analysis perspectives
        analyses = {}
        for perspective_name, analysis_func in self.analysis_perspectives.items():
            if perspective_name in ["alternative_approaches", "domain_expertise", "opportunity_identification"]:
                analyses[perspective_name] = analysis_func(plan_content, task_context)
        
        return self._format_alternative_analysis(analyses, plan, task_context)
    
    async def _synthesize_and_mediate(
        self, 
        plan: AgentResponse, 
        critique: AgentResponse, 
        task_context: TaskContext
    ) -> str:
        """Synthesize plan and critique to build consensus"""
        
        # Analyze both perspectives
        synthesis = self._analyze_plan_critique_synthesis(plan.content, critique.content, task_context)
        
        return self._format_synthesis_response(synthesis, plan, critique, task_context)
    
    def _analyze_alternatives(self, plan_content: str, task_context: TaskContext) -> dict:
        """Analyze alternative approaches to the proposed plan"""
        
        alternatives = []
        benefits = []
        considerations = []
        
        # Identify plan type and suggest alternatives
        if "phase" in plan_content.lower():
            alternatives.append("Parallel execution of independent phases")
            benefits.append("Reduced overall timeline through concurrent work")
            considerations.append("Requires careful coordination and resource management")
        
        if "implementation" in plan_content.lower():
            alternatives.append("Prototype-first approach")
            benefits.append("Early validation and risk reduction")
            considerations.append("May require additional refactoring effort")
        
        if "testing" in plan_content.lower():
            alternatives.append("Test-driven development methodology")
            benefits.append("Higher quality and better design validation")
            considerations.append("Initial development may be slower")
        
        # Add domain-specific alternatives
        if any(tech in task_context.description.lower() for tech in ["api", "web", "service"]):
            alternatives.append("Microservices architecture")
            benefits.append("Improved scalability and maintainability")
            considerations.append("Increased complexity in deployment and monitoring")
        
        return {
            "alternatives": alternatives,
            "benefits": benefits,
            "considerations": considerations
        }
    
    def _provide_domain_insights(self, plan_content: str, task_context: TaskContext) -> dict:
        """Provide domain-specific insights and expertise"""
        
        insights = []
        best_practices = []
        tools_frameworks = []
        
        # Analyze task domain
        task_lower = task_context.description.lower()
        
        if any(keyword in task_lower for keyword in ["software", "code", "develop", "program"]):
            insights.append("Consider code quality metrics and automated testing")
            best_practices.append("Implement continuous integration and deployment")
            tools_frameworks.append("Static analysis tools, linting, and code formatters")
        
        if any(keyword in task_lower for keyword in ["data", "analysis", "research"]):
            insights.append("Plan for data validation and quality assurance")
            best_practices.append("Document data sources and transformation logic")
            tools_frameworks.append("Data visualization and statistical analysis tools")
        
        if any(keyword in task_lower for keyword in ["user", "interface", "experience"]):
            insights.append("Consider accessibility and responsive design principles")
            best_practices.append("Implement user testing and feedback collection")
            tools_frameworks.append("Design systems and usability testing tools")
        
        return {
            "insights": insights,
            "best_practices": best_practices,
            "tools_frameworks": tools_frameworks
        }
    
    def _identify_opportunities(self, plan_content: str, task_context: TaskContext) -> dict:
        """Identify optimization and enhancement opportunities"""
        
        opportunities = []
        optimizations = []
        enhancements = []
        
        # Identify scalability opportunities
        if "scale" not in plan_content.lower():
            opportunities.append("Future scalability planning")
            optimizations.append("Design for horizontal and vertical scaling")
            enhancements.append("Implement performance monitoring and optimization")
        
        # Identify automation opportunities
        if "automat" not in plan_content.lower():
            opportunities.append("Process automation potential")
            optimizations.append("Automate repetitive tasks and workflows")
            enhancements.append("Implement CI/CD and automated quality checks")
        
        # Identify integration opportunities
        if "integrat" not in plan_content.lower():
            opportunities.append("System integration possibilities")
            optimizations.append("Leverage existing systems and APIs")
            enhancements.append("Design for interoperability and extensibility")
        
        return {
            "opportunities": opportunities,
            "optimizations": optimizations,
            "enhancements": enhancements
        }
    
    def _mediate_consensus(self, plan_content: str, task_context: TaskContext) -> dict:
        """Provide consensus mediation strategies"""
        
        mediation_strategies = [
            "Focus on shared objectives and success criteria",
            "Identify areas of agreement and build from there",
            "Propose hybrid solutions that incorporate multiple perspectives",
            "Suggest phased implementation to validate approaches"
        ]
        
        compromise_suggestions = [
            "Implement pilot phase to test multiple approaches",
            "Create decision framework with clear evaluation criteria",
            "Plan for iterative refinement based on early results",
            "Establish regular review points for course correction"
        ]
        
        return {
            "strategies": mediation_strategies,
            "compromises": compromise_suggestions
        }
    
    def _additional_risk_analysis(self, plan_content: str, task_context: TaskContext) -> dict:
        """Provide additional risk analysis and mitigation"""
        
        additional_risks = []
        mitigation_strategies = []
        
        # Identify overlooked risks
        if "external" not in plan_content.lower():
            additional_risks.append("External dependency risks")
            mitigation_strategies.append("Identify backup options for critical dependencies")
        
        if "performance" not in plan_content.lower():
            additional_risks.append("Performance and scalability risks")
            mitigation_strategies.append("Implement performance testing and monitoring")
        
        if "security" not in plan_content.lower():
            additional_risks.append("Security and compliance risks")
            mitigation_strategies.append("Conduct security review and implement safeguards")
        
        return {
            "additional_risks": additional_risks,
            "mitigation_strategies": mitigation_strategies
        }
    
    def _format_alternative_analysis(
        self, 
        analyses: dict, 
        plan: AgentResponse, 
        task_context: TaskContext
    ) -> str:
        """Format alternative analysis response"""
        
        response = f"""# ADDITIONAL ANALYSIS - ALTERNATIVE PERSPECTIVES

## Plan Assessment
Analyzing the proposed implementation plan for alternative approaches and enhancements.

## ALTERNATIVE APPROACHES
"""
        
        if "alternative_approaches" in analyses:
            alt_data = analyses["alternative_approaches"]
            for i, alt in enumerate(alt_data["alternatives"], 1):
                response += f"### Alternative {i}: {alt}\n"
                if i-1 < len(alt_data["benefits"]):
                    response += f"**Benefits**: {alt_data['benefits'][i-1]}\n"
                if i-1 < len(alt_data["considerations"]):
                    response += f"**Considerations**: {alt_data['considerations'][i-1]}\n\n"
        
        if "domain_expertise" in analyses:
            domain_data = analyses["domain_expertise"]
            response += "## DOMAIN-SPECIFIC INSIGHTS\n"
            for insight in domain_data["insights"]:
                response += f"- {insight}\n"
            
            response += "\n**Best Practices**:\n"
            for practice in domain_data["best_practices"]:
                response += f"- {practice}\n"
            
            response += "\n**Recommended Tools/Frameworks**:\n"
            for tool in domain_data["tools_frameworks"]:
                response += f"- {tool}\n"
            response += "\n"
        
        if "opportunity_identification" in analyses:
            opp_data = analyses["opportunity_identification"]
            response += "## OPTIMIZATION OPPORTUNITIES\n"
            for opp in opp_data["opportunities"]:
                response += f"- {opp}\n"
            response += "\n"
        
        response += """## RECOMMENDATION
The proposed plan provides a solid foundation. Consider incorporating the alternative approaches and domain insights above to enhance robustness and future-proofing.

**Next Steps**: Await critic feedback to synthesize all perspectives into an optimal solution."""
        
        return response
    
    def _analyze_plan_critique_synthesis(
        self, 
        plan_content: str, 
        critique_content: str, 
        task_context: TaskContext
    ) -> dict:
        """Analyze plan and critique for synthesis opportunities"""
        
        # Extract key points from critique
        critique_issues = []
        critique_suggestions = []
        
        if "issues identified" in critique_content.lower():
            # Parse critique for specific issues and suggestions
            lines = critique_content.split('\n')
            in_issues = False
            in_suggestions = False
            
            for line in lines:
                line = line.strip()
                if "issues identified" in line.lower():
                    in_issues = True
                    in_suggestions = False
                elif "suggestions" in line.lower():
                    in_issues = False
                    in_suggestions = True
                elif line.startswith('- ') or line.startswith('* '):
                    if in_issues:
                        critique_issues.append(line[2:])
                    elif in_suggestions:
                        critique_suggestions.append(line[2:])
                elif line.startswith('#'):
                    in_issues = False
                    in_suggestions = False
        
        # Identify synthesis opportunities
        synthesis_points = []
        compromise_solutions = []
        
        if critique_issues:
            synthesis_points.append("Address critic concerns while maintaining plan strengths")
            compromise_solutions.append("Implement phased approach to validate critic concerns")
        
        if critique_suggestions:
            synthesis_points.append("Integrate valuable suggestions into plan refinement")
            compromise_solutions.append("Prioritize high-impact suggestions for immediate implementation")
        
        return {
            "critique_issues": critique_issues,
            "critique_suggestions": critique_suggestions,
            "synthesis_points": synthesis_points,
            "compromise_solutions": compromise_solutions
        }
    
    def _format_synthesis_response(
        self, 
        synthesis: dict, 
        plan: AgentResponse, 
        critique: AgentResponse, 
        task_context: TaskContext
    ) -> str:
        """Format synthesis and mediation response"""
        
        response = f"""# ADDITIONAL ANALYSIS - CONSENSUS MEDIATION

## Synthesis Overview
Analyzing both the implementation plan and critic feedback to build consensus and optimize the solution.

## PLAN STRENGTHS
The proposed plan demonstrates:
- Structured approach to task breakdown
- Clear phases and implementation strategy  
- Consideration of key requirements and constraints

## CRITIC INSIGHTS
The critique provides valuable feedback on:
"""
        
        for issue in synthesis["critique_issues"][:3]:  # Top 3 issues
            response += f"- {issue}\n"
        
        response += "\n## SYNTHESIS OPPORTUNITIES\n"
        for point in synthesis["synthesis_points"]:
            response += f"- {point}\n"
        
        response += "\n## CONSENSUS BUILDING RECOMMENDATIONS\n"
        
        if synthesis["compromise_solutions"]:
            response += "### Compromise Solutions\n"
            for solution in synthesis["compromise_solutions"]:
                response += f"- {solution}\n"
            response += "\n"
        
        response += """### Integration Strategy
1. **Incorporate High-Value Suggestions**: Prioritize critic suggestions that enhance plan quality without major restructuring
2. **Address Critical Issues**: Focus on resolving fundamental concerns that could impact success
3. **Phased Validation**: Implement uncertain elements in early phases for validation
4. **Iterative Refinement**: Plan for continuous improvement based on feedback and results

## CONSENSUS ASSESSMENT
**Current State**: Plan provides solid foundation, critique identifies important improvements
**Path Forward**: Integrate key suggestions while maintaining plan structure
**Confidence Level**: HIGH - Clear path to consensus through collaborative refinement

## RECOMMENDED NEXT STEPS
1. Planner incorporates high-priority critic suggestions
2. Address fundamental issues identified in critique
3. Maintain plan structure while enhancing weak areas
4. Focus on areas of agreement to build momentum

**Ready for consensus convergence in next iteration.**"""
        
        return response
    
    def _assess_plan_viability(self, plan: AgentResponse) -> float:
        """Assess plan viability for consensus scoring"""
        
        plan_content = plan.content
        
        # Basic viability metrics
        structure_score = min(1.0, plan_content.count("#") / 6)  # Headers indicate structure
        detail_score = min(1.0, len(plan_content.split()) / 400)  # Word count indicates detail
        actionability_score = min(1.0, plan_content.count("-") / 10)  # Bullet points indicate actionability
        
        return (structure_score + detail_score + actionability_score) / 3
    
    def _calculate_mediation_consensus(
        self, 
        planner_proposals: List[AgentResponse], 
        critic_proposals: List[AgentResponse]
    ) -> float:
        """Calculate consensus score based on mediation analysis"""
        
        if not planner_proposals or not critic_proposals:
            return 0.5  # Neutral when missing perspectives
        
        latest_critic = critic_proposals[-1]
        
        # Extract consensus level from critic response
        if "high" in latest_critic.content.lower() and "consensus" in latest_critic.content.lower():
            return 0.8
        elif "medium" in latest_critic.content.lower() and "consensus" in latest_critic.content.lower():
            return 0.6
        elif "low" in latest_critic.content.lower() and "consensus" in latest_critic.content.lower():
            return 0.4
        else:
            return latest_critic.consensus_score if latest_critic.consensus_score > 0 else 0.5
    
    def _get_analysis_reasoning(self, current_proposals: List[AgentResponse]) -> str:
        """Generate reasoning for analysis approach"""
        
        planner_count = len([p for p in current_proposals if p.role.value == "planner"])
        critic_count = len([p for p in current_proposals if p.role.value == "critic"])
        
        if planner_count == 0:
            return "Providing initial alternative perspectives and considerations"
        elif critic_count == 0:
            return "Analyzing plan alternatives and providing supplementary insights"
        else:
            return "Synthesizing plan and critique perspectives for consensus building"
    
    def _calculate_analysis_confidence(self, current_proposals: List[AgentResponse]) -> float:
        """Calculate confidence in analysis quality"""
        
        base_confidence = 0.75
        
        # Higher confidence when there's more context to analyze
        if len(current_proposals) > 1:
            base_confidence += 0.1
        
        # Adjust based on proposal quality
        for proposal in current_proposals:
            if len(proposal.content.split()) > 100:
                base_confidence += 0.02
        
        return min(0.9, max(0.6, base_confidence))
    
    def _generate_synthesis_suggestions(self, current_proposals: List[AgentResponse]) -> List[str]:
        """Generate synthesis-oriented suggestions"""
        
        suggestions = []
        
        planner_proposals = [p for p in current_proposals if p.role.value == "planner"]
        critic_proposals = [p for p in current_proposals if p.role.value == "critic"]
        
        if not planner_proposals:
            suggestions = [
                "Consider multiple implementation approaches from the start",
                "Plan for iterative refinement based on feedback",
                "Include alternative strategies for key decisions"
            ]
        elif not critic_proposals:
            suggestions = [
                "Enhance plan with alternative approaches and optimizations",
                "Consider domain-specific best practices and tools",
                "Plan for scalability and future enhancements"
            ]
        else:
            suggestions = [
                "Integrate high-value critic suggestions into plan refinement",
                "Focus on areas of agreement to build consensus momentum",
                "Implement phased approach to validate uncertain elements"
            ]
        
        return suggestions[:3]