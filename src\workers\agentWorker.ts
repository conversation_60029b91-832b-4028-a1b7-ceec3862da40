import { parentPort, workerData } from 'worker_threads';
import axios from 'axios';
import { WorkerResponse } from '../services/messageProtocol';

interface WorkerMessage {
  type: 'task' | 'stop' | 'status' | 'ping';
  payload?: any;
  messageId?: string;
}

// Use WorkerResponse from messageProtocol instead of local interface

class AgentWorker {
  private agentId: string;
  private role: 'planner' | 'critic' | 'additional';
  private isRunning: boolean = false;
  private autoGenServiceUrl: string = 'http://127.0.0.1:8000';

  constructor() {
    this.agentId = workerData.agentId;
    this.role = workerData.role;
    
    this.setupMessageHandlers();
    this.sendStatus('idle');
    
    console.log(`Agent worker ${this.agentId} (${this.role}) initialized`);
  }

  private setupMessageHandlers(): void {
    if (!parentPort) {
      throw new Error('Worker must be run in a worker thread');
    }

    parentPort.on('message', async (message: WorkerMessage) => {
      try {
        await this.handleMessage(message);
      } catch (error) {
        this.sendError(`Error handling message: ${error}`, message.messageId);
      }
    });

    // Handle uncaught errors
    process.on('uncaughtException', (error) => {
      this.sendError(`Uncaught exception: ${error.message}`);
    });

    process.on('unhandledRejection', (reason) => {
      this.sendError(`Unhandled rejection: ${reason}`);
    });
  }

  private async handleMessage(message: WorkerMessage): Promise<void> {
    switch (message.type) {
      case 'task':
        await this.handleTask(message.payload, message.messageId);
        break;
      case 'stop':
        await this.handleStop(message.messageId);
        break;
      case 'status':
        this.sendStatus(this.isRunning ? 'running' : 'idle', message.messageId);
        break;
      case 'ping':
        this.sendResponse({
          type: 'pong',
          agentId: this.agentId,
          messageId: message.messageId,
          timestamp: Date.now()
        });
        break;
      default:
        this.sendError(`Unknown message type: ${message.type}`, message.messageId);
    }
  }

  private async handleTask(taskData: any, messageId?: string): Promise<void> {
    if (this.isRunning) {
      this.sendError('Agent is already running a task', messageId);
      return;
    }

    try {
      this.isRunning = true;
      this.sendStatus('running', messageId);

      // Connect to AutoGen service
      const result = await this.executeTask(taskData);
      
      this.sendResponse({
        type: 'result',
        payload: result,
        agentId: this.agentId,
        messageId,
        timestamp: Date.now()
      });

    } catch (error) {
      this.sendError(`Task execution failed: ${error}`, messageId);
    } finally {
      this.isRunning = false;
      this.sendStatus('idle');
    }
  }

  private async executeTask(taskData: any): Promise<any> {
    // Simulate task execution with AutoGen service
    const { taskDescription, contextFiles } = taskData;
    
    try {
      // Check if AutoGen service is available
      await this.checkAutoGenService();
      
      // Create agent session
      const sessionResponse = await axios.post(`${this.autoGenServiceUrl}/agents/create`, {
        agentId: this.agentId,
        role: this.role,
        taskDescription,
        contextFiles
      });

      const sessionId = sessionResponse.data.sessionId;
      
      // Start agent conversation
      const conversationResponse = await axios.post(`${this.autoGenServiceUrl}/agents/${sessionId}/start`, {
        message: taskDescription
      });

      // Stream tokens back to main thread
      await this.streamConversation();
      
      return conversationResponse.data;
      
    } catch (error) {
      // Fallback: simulate agent work for now
      console.log(`Agent ${this.agentId} falling back to simulation mode`);
      return await this.simulateAgentWork(taskData);
    }
  }

  private async checkAutoGenService(): Promise<void> {
    try {
      await axios.get(`${this.autoGenServiceUrl}/health`, { timeout: 1000 });
    } catch (error) {
      throw new Error('AutoGen service not available');
    }
  }

  private async streamConversation(): Promise<void> {
    // Simulate streaming tokens from AutoGen service
    for (let i = 0; i < 10; i++) {
      await this.delay(500);
      
      this.sendResponse({
        type: 'token',
        payload: `Token ${i + 1} from ${this.role} agent... `,
        agentId: this.agentId,
        messageId: undefined,
        timestamp: Date.now()
      });
    }
  }

  private async simulateAgentWork(taskData: any): Promise<any> {
    const { taskDescription } = taskData;
    
    // Simulate different agent behaviors based on role
    const responses = {
      planner: `## Planning Phase\n\nI've analyzed the task: "${taskDescription}"\n\nProposed approach:\n1. Break down requirements\n2. Design architecture\n3. Create implementation plan\n\nReady for critique.`,
      critic: `## Critique Phase\n\nReviewing the plan for: "${taskDescription}"\n\nFeedback:\n- Plan looks solid\n- Consider edge cases\n- Add error handling\n\nSuggested improvements:\n- More detailed steps\n- Risk assessment`,
      additional: `## Additional Analysis\n\nSupplementary thoughts on: "${taskDescription}"\n\n- Alternative approaches considered\n- Performance implications\n- Scalability factors\n\nConcur with current direction.`
    };

    // Stream simulated tokens
    const response = responses[this.role];
    const tokens = response.split(' ');
    
    for (const token of tokens) {
      await this.delay(100);
      this.sendResponse({
        type: 'token',
        payload: token + ' ',
        agentId: this.agentId,
        messageId: undefined,
        timestamp: Date.now()
      });
    }

    return {
      role: this.role,
      response,
      consensus: true,
      timestamp: new Date().toISOString()
    };
  }

  private async handleStop(messageId?: string): Promise<void> {
    this.isRunning = false;
    this.sendStatus('stopped', messageId);
  }

  private sendStatus(status: string, messageId?: string): void {
    this.sendResponse({
      type: 'status',
      payload: { status, role: this.role },
      agentId: this.agentId,
      messageId,
      timestamp: Date.now()
    });
  }

  private sendError(error: string, messageId?: string): void {
    this.sendResponse({
      type: 'error',
      payload: error,
      agentId: this.agentId,
      messageId,
      timestamp: Date.now()
    });
  }

  private sendResponse(response: WorkerResponse): void {
    if (parentPort) {
      parentPort.postMessage(response);
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Initialize worker
new AgentWorker();