"""
Agent Factory for Metamorphic Reactor
Creates and manages agent instances based on role and configuration
"""

import logging
from typing import Dict, Any, Optional, List, Set, Union
import uuid
import json
from datetime import datetime
import re
from enum import Enum
from dataclasses import dataclass, field
from copy import deepcopy

from .agent_types import AgentConfig, AgentRole, AgentCreationRequest, AgentProfile, AgentStatus, TeamCreationRequest, Team, TeamStatus, TeamRole
from .base_agent import BaseAgent
from .planner_agent import PlannerAgent
from .critic_agent import CriticAgent
from .additional_agent import AdditionalAgent

logger = logging.getLogger(__name__)

# Template Management System Classes and Enums

class TemplateCategory(str, Enum):
    """Template categories for organization"""
    ROLE_SPECIFIC = "role_specific"
    TASK_SPECIFIC = "task_specific"
    DOMAIN_SPECIFIC = "domain_specific"
    GENERAL_PURPOSE = "general_purpose"
    SPECIALIZED = "specialized"
    COMMUNITY = "community"
    CUSTOM = "custom"

class TemplateStatus(str, Enum):
    """Template status for lifecycle management"""
    DRAFT = "draft"
    ACTIVE = "active"
    DEPRECATED = "deprecated"
    ARCHIVED = "archived"

class TemplateValidationLevel(str, Enum):
    """Template validation levels"""
    BASIC = "basic"
    COMPREHENSIVE = "comprehensive"
    STRICT = "strict"

@dataclass
class TemplateMetadata:
    """Template metadata for comprehensive tracking"""
    author: str
    version: str = "1.0.0"
    description: str = ""
    category: TemplateCategory = TemplateCategory.GENERAL_PURPOSE
    tags: Set[str] = field(default_factory=set)
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    status: TemplateStatus = TemplateStatus.DRAFT
    is_public: bool = False
    parent_template_id: Optional[str] = None
    derived_from: Optional[str] = None
    compatibility_version: str = "1.0.0"
    min_requirements: Dict[str, Any] = field(default_factory=dict)
    license: str = "MIT"
    
@dataclass
class TemplateUsageStats:
    """Template usage statistics"""
    template_id: str
    usage_count: int = 0
    success_count: int = 0
    failure_count: int = 0
    avg_performance_score: float = 0.0
    last_used: Optional[datetime] = None
    created_agents: List[str] = field(default_factory=list)
    popular_configurations: Dict[str, int] = field(default_factory=dict)
    
@dataclass
class TemplateRating:
    """Template rating from users"""
    template_id: str
    user_id: str
    rating: int  # 1-5 stars
    comment: str = ""
    created_at: datetime = field(default_factory=datetime.utcnow)
    helpful_votes: int = 0
    
@dataclass
class TemplateVersion:
    """Template version for history tracking"""
    template_id: str
    version: str
    content: Dict[str, Any]
    metadata: TemplateMetadata
    created_at: datetime = field(default_factory=datetime.utcnow)
    change_log: str = ""
    is_breaking_change: bool = False

# Agent Capability Scoring System

class CapabilityCategory(str, Enum):
    """Categories of agent capabilities"""
    TECHNICAL = "technical"
    COGNITIVE = "cognitive"
    COMMUNICATION = "communication"
    DOMAIN_SPECIFIC = "domain_specific"
    PERFORMANCE = "performance"
    RELIABILITY = "reliability"

class CapabilityLevel(str, Enum):
    """Capability proficiency levels"""
    NOVICE = "novice"          # 0-20%
    BEGINNER = "beginner"      # 21-40%
    INTERMEDIATE = "intermediate"  # 41-60%
    ADVANCED = "advanced"      # 61-80%
    EXPERT = "expert"          # 81-100%

class ScoringMetric(str, Enum):
    """Metrics for capability scoring"""
    ACCURACY = "accuracy"
    SPEED = "speed"
    CONSISTENCY = "consistency"
    COMPLEXITY_HANDLING = "complexity_handling"
    ERROR_RECOVERY = "error_recovery"
    RESOURCE_EFFICIENCY = "resource_efficiency"
    ADAPTABILITY = "adaptability"

@dataclass
class CapabilityScore:
    """Individual capability score"""
    capability: str
    category: CapabilityCategory
    score: float  # 0.0 to 1.0
    level: CapabilityLevel
    confidence: float = 0.0  # Confidence in the score
    evidence_count: int = 0  # Number of evaluations
    last_updated: datetime = field(default_factory=datetime.utcnow)
    metrics: Dict[ScoringMetric, float] = field(default_factory=dict)
    
    def __post_init__(self):
        """Set level based on score"""
        if self.score <= 0.2:
            self.level = CapabilityLevel.NOVICE
        elif self.score <= 0.4:
            self.level = CapabilityLevel.BEGINNER
        elif self.score <= 0.6:
            self.level = CapabilityLevel.INTERMEDIATE
        elif self.score <= 0.8:
            self.level = CapabilityLevel.ADVANCED
        else:
            self.level = CapabilityLevel.EXPERT

@dataclass
class CapabilityEvaluation:
    """Evaluation of an agent's capability performance"""
    agent_id: str
    capability: str
    task_id: str
    score: float  # 0.0 to 1.0
    metrics: Dict[ScoringMetric, float]
    evaluator: str  # human, automated, or agent_id
    context: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.utcnow)
    notes: str = ""

@dataclass
class AgentCapabilityProfile:
    """Complete capability profile for an agent"""
    agent_id: str
    overall_score: float = 0.0
    capability_scores: Dict[str, CapabilityScore] = field(default_factory=dict)
    evaluations: List[CapabilityEvaluation] = field(default_factory=list)
    strengths: List[str] = field(default_factory=list)  # Top capabilities
    weaknesses: List[str] = field(default_factory=list)  # Lowest capabilities
    recommendations: List[str] = field(default_factory=list)
    last_updated: datetime = field(default_factory=datetime.utcnow)
    
    def get_capability_level(self, capability: str) -> CapabilityLevel:
        """Get proficiency level for a capability"""
        if capability in self.capability_scores:
            return self.capability_scores[capability].level
        return CapabilityLevel.NOVICE
    
    def get_top_capabilities(self, limit: int = 5) -> List[str]:
        """Get agent's top capabilities"""
        sorted_caps = sorted(
            self.capability_scores.items(),
            key=lambda x: x[1].score,
            reverse=True
        )
        return [cap for cap, _ in sorted_caps[:limit]]
    
    def get_improvement_areas(self, limit: int = 3) -> List[str]:
        """Get capabilities that need improvement"""
        sorted_caps = sorted(
            self.capability_scores.items(),
            key=lambda x: x[1].score
        )
        return [cap for cap, _ in sorted_caps[:limit]]

@dataclass
class CapabilityBenchmark:
    """Benchmark for capability scoring"""
    capability: str
    category: CapabilityCategory
    test_cases: List[Dict[str, Any]]
    expected_outcomes: List[Dict[str, Any]]
    scoring_criteria: Dict[ScoringMetric, float]  # Weights for each metric
    difficulty_level: float = 0.5  # 0.0 to 1.0
    timeout_seconds: int = 60
    created_by: str = "system"
    created_at: datetime = field(default_factory=datetime.utcnow)

@dataclass
class CapabilityTemplate:
    """Template for capability definitions"""
    name: str
    category: CapabilityCategory
    description: str
    required_skills: List[str]
    related_capabilities: List[str]
    benchmarks: List[str]  # Benchmark IDs
    weight: float = 1.0  # Weight in overall scoring
    is_core: bool = False  # Core capability for the role

class CapabilityScorer:
    """Advanced capability scoring and evaluation system"""
    
    def __init__(self, factory):
        self.factory = factory
        self._initialize_default_capabilities()
        self._initialize_default_benchmarks()
    
    def _initialize_default_capabilities(self):
        """Initialize default capability templates"""
        default_capabilities = {
            # Technical capabilities
            "code_generation": CapabilityTemplate(
                name="code_generation",
                category=CapabilityCategory.TECHNICAL,
                description="Generate clean, functional code",
                required_skills=["programming", "syntax", "logic"],
                related_capabilities=["debugging", "testing"],
                benchmarks=["code_gen_basic", "code_gen_advanced"],
                weight=1.0,
                is_core=True
            ),
            "debugging": CapabilityTemplate(
                name="debugging",
                category=CapabilityCategory.TECHNICAL,
                description="Identify and fix code issues",
                required_skills=["analysis", "problem_solving", "testing"],
                related_capabilities=["code_generation", "error_analysis"],
                benchmarks=["debug_basic", "debug_complex"],
                weight=0.8,
                is_core=True
            ),
            "testing": CapabilityTemplate(
                name="testing",
                category=CapabilityCategory.TECHNICAL,
                description="Design and implement tests",
                required_skills=["test_design", "verification", "quality_assurance"],
                related_capabilities=["code_generation", "debugging"],
                benchmarks=["test_design", "test_implementation"],
                weight=0.7,
                is_core=False
            ),
            
            # Cognitive capabilities
            "task_decomposition": CapabilityTemplate(
                name="task_decomposition",
                category=CapabilityCategory.COGNITIVE,
                description="Break down complex tasks into manageable parts",
                required_skills=["analysis", "planning", "organization"],
                related_capabilities=["planning", "prioritization"],
                benchmarks=["task_breakdown", "dependency_analysis"],
                weight=0.9,
                is_core=True
            ),
            "problem_solving": CapabilityTemplate(
                name="problem_solving",
                category=CapabilityCategory.COGNITIVE,
                description="Find creative solutions to challenges",
                required_skills=["creativity", "logic", "analysis"],
                related_capabilities=["debugging", "optimization"],
                benchmarks=["problem_solving_basic", "problem_solving_complex"],
                weight=1.0,
                is_core=True
            ),
            
            # Communication capabilities
            "documentation": CapabilityTemplate(
                name="documentation",
                category=CapabilityCategory.COMMUNICATION,
                description="Create clear, comprehensive documentation",
                required_skills=["writing", "organization", "clarity"],
                related_capabilities=["explanation", "teaching"],
                benchmarks=["doc_quality", "doc_completeness"],
                weight=0.6,
                is_core=False
            ),
            "explanation": CapabilityTemplate(
                name="explanation",
                category=CapabilityCategory.COMMUNICATION,
                description="Explain complex concepts clearly",
                required_skills=["communication", "simplification", "teaching"],
                related_capabilities=["documentation", "mentoring"],
                benchmarks=["explanation_clarity", "explanation_accuracy"],
                weight=0.7,
                is_core=False
            ),
            
            # Performance capabilities
            "efficiency": CapabilityTemplate(
                name="efficiency",
                category=CapabilityCategory.PERFORMANCE,
                description="Optimize for speed and resource usage",
                required_skills=["optimization", "resource_management", "algorithms"],
                related_capabilities=["code_generation", "problem_solving"],
                benchmarks=["performance_optimization", "resource_efficiency"],
                weight=0.8,
                is_core=False
            ),
            "consistency": CapabilityTemplate(
                name="consistency",
                category=CapabilityCategory.RELIABILITY,
                description="Deliver consistent quality results",
                required_skills=["reliability", "attention_to_detail", "standards"],
                related_capabilities=["quality_assurance", "testing"],
                benchmarks=["consistency_test", "quality_maintenance"],
                weight=0.9,
                is_core=True
            )
        }
        
        self.factory.capability_templates.update(default_capabilities)
    
    def _initialize_default_benchmarks(self):
        """Initialize default capability benchmarks"""
        benchmarks = {
            "code_gen_basic": CapabilityBenchmark(
                capability="code_generation",
                category=CapabilityCategory.TECHNICAL,
                test_cases=[
                    {"task": "Create a simple function", "complexity": 0.3},
                    {"task": "Implement basic algorithm", "complexity": 0.4},
                    {"task": "Handle edge cases", "complexity": 0.5}
                ],
                expected_outcomes=[
                    {"metric": "syntax_correctness", "weight": 0.3},
                    {"metric": "functionality", "weight": 0.4},
                    {"metric": "readability", "weight": 0.3}
                ],
                scoring_criteria={
                    ScoringMetric.ACCURACY: 0.4,
                    ScoringMetric.SPEED: 0.2,
                    ScoringMetric.CONSISTENCY: 0.4
                },
                difficulty_level=0.3
            ),
            "debug_basic": CapabilityBenchmark(
                capability="debugging",
                category=CapabilityCategory.TECHNICAL,
                test_cases=[
                    {"task": "Find syntax error", "complexity": 0.2},
                    {"task": "Fix logic error", "complexity": 0.5},
                    {"task": "Resolve runtime error", "complexity": 0.6}
                ],
                expected_outcomes=[
                    {"metric": "error_identification", "weight": 0.4},
                    {"metric": "fix_correctness", "weight": 0.6}
                ],
                scoring_criteria={
                    ScoringMetric.ACCURACY: 0.5,
                    ScoringMetric.SPEED: 0.3,
                    ScoringMetric.ERROR_RECOVERY: 0.2
                },
                difficulty_level=0.4
            ),
            "task_breakdown": CapabilityBenchmark(
                capability="task_decomposition",
                category=CapabilityCategory.COGNITIVE,
                test_cases=[
                    {"task": "Break down simple project", "complexity": 0.3},
                    {"task": "Analyze dependencies", "complexity": 0.5},
                    {"task": "Prioritize subtasks", "complexity": 0.4}
                ],
                expected_outcomes=[
                    {"metric": "completeness", "weight": 0.3},
                    {"metric": "logical_order", "weight": 0.4},
                    {"metric": "feasibility", "weight": 0.3}
                ],
                scoring_criteria={
                    ScoringMetric.ACCURACY: 0.4,
                    ScoringMetric.COMPLEXITY_HANDLING: 0.4,
                    ScoringMetric.CONSISTENCY: 0.2
                },
                difficulty_level=0.5
            )
        }
        
        self.factory.capability_benchmarks.update(benchmarks)
    
    def evaluate_agent_capability(self, agent_id: str, capability: str, 
                                 task_context: Dict[str, Any], 
                                 evaluator: str = "automated") -> CapabilityEvaluation:
        """
        Evaluate an agent's capability performance
        
        Args:
            agent_id: Agent to evaluate
            capability: Capability to assess
            task_context: Context of the task performed
            evaluator: Who is doing the evaluation
            
        Returns:
            CapabilityEvaluation: Evaluation results
        """
        # Get or create capability profile
        if agent_id not in self.factory.capability_profiles:
            self.factory.capability_profiles[agent_id] = AgentCapabilityProfile(agent_id=agent_id)
        
        # Get benchmark for capability
        benchmark = self.factory.capability_benchmarks.get(capability)
        if not benchmark:
            # Create basic evaluation without benchmark
            return self._create_basic_evaluation(agent_id, capability, task_context, evaluator)
        
        # Perform benchmarked evaluation
        return self._perform_benchmarked_evaluation(agent_id, capability, benchmark, task_context, evaluator)
    
    def _create_basic_evaluation(self, agent_id: str, capability: str, 
                                task_context: Dict[str, Any], evaluator: str) -> CapabilityEvaluation:
        """Create basic evaluation without benchmark"""
        # Basic scoring based on task success
        success = task_context.get("success", False)
        completion_time = task_context.get("completion_time", 0)
        quality_score = task_context.get("quality_score", 0.5)
        
        # Calculate metrics
        metrics = {
            ScoringMetric.ACCURACY: 1.0 if success else 0.0,
            ScoringMetric.SPEED: max(0.0, 1.0 - (completion_time / 60.0)),  # Normalize by minute
            ScoringMetric.CONSISTENCY: quality_score
        }
        
        # Overall score
        overall_score = sum(metrics.values()) / len(metrics)
        
        evaluation = CapabilityEvaluation(
            agent_id=agent_id,
            capability=capability,
            task_id=task_context.get("task_id", "unknown"),
            score=overall_score,
            metrics=metrics,
            evaluator=evaluator,
            context=task_context
        )
        
        # Store evaluation
        self.factory.capability_evaluations.append(evaluation)
        
        return evaluation
    
    def _perform_benchmarked_evaluation(self, agent_id: str, capability: str, 
                                       benchmark: CapabilityBenchmark, 
                                       task_context: Dict[str, Any], 
                                       evaluator: str) -> CapabilityEvaluation:
        """Perform evaluation using benchmark"""
        # Extract performance metrics from task context
        metrics = {}
        
        for metric, weight in benchmark.scoring_criteria.items():
            if metric.value in task_context:
                metrics[metric] = task_context[metric.value]
            else:
                # Use default values if not provided
                metrics[metric] = 0.5
        
        # Calculate weighted score
        total_score = sum(score * benchmark.scoring_criteria.get(metric, 0.0) 
                         for metric, score in metrics.items())
        
        # Adjust for difficulty
        adjusted_score = total_score * (1.0 + benchmark.difficulty_level)
        adjusted_score = min(1.0, adjusted_score)  # Cap at 1.0
        
        evaluation = CapabilityEvaluation(
            agent_id=agent_id,
            capability=capability,
            task_id=task_context.get("task_id", "unknown"),
            score=adjusted_score,
            metrics=metrics,
            evaluator=evaluator,
            context=task_context
        )
        
        # Store evaluation
        self.factory.capability_evaluations.append(evaluation)
        
        return evaluation
    
    def update_capability_profile(self, agent_id: str, evaluation: CapabilityEvaluation):
        """Update agent's capability profile with new evaluation"""
        profile = self.factory.capability_profiles.get(agent_id)
        if not profile:
            profile = AgentCapabilityProfile(agent_id=agent_id)
            self.factory.capability_profiles[agent_id] = profile
        
        # Add evaluation to profile
        profile.evaluations.append(evaluation)
        
        # Update or create capability score
        capability = evaluation.capability
        if capability not in profile.capability_scores:
            template = self.factory.capability_templates.get(capability)
            category = template.category if template else CapabilityCategory.TECHNICAL
            
            profile.capability_scores[capability] = CapabilityScore(
                capability=capability,
                category=category,
                score=evaluation.score,
                level=CapabilityLevel.NOVICE,
                evidence_count=1,
                metrics=evaluation.metrics.copy()
            )
        else:
            # Update existing score with weighted average
            existing_score = profile.capability_scores[capability]
            new_evidence_count = existing_score.evidence_count + 1
            
            # Weight recent evaluations more heavily
            weight = min(0.3, 1.0 / new_evidence_count)
            new_score = existing_score.score * (1 - weight) + evaluation.score * weight
            
            existing_score.score = new_score
            existing_score.evidence_count = new_evidence_count
            existing_score.last_updated = datetime.utcnow()
            
            # Update metrics
            for metric, value in evaluation.metrics.items():
                if metric in existing_score.metrics:
                    existing_score.metrics[metric] = existing_score.metrics[metric] * (1 - weight) + value * weight
                else:
                    existing_score.metrics[metric] = value
        
        # Update overall score
        self._update_overall_score(profile)
        
        # Update strengths and weaknesses
        self._update_strengths_weaknesses(profile)
        
        # Update timestamp
        profile.last_updated = datetime.utcnow()
    
    def _update_overall_score(self, profile: AgentCapabilityProfile):
        """Update agent's overall capability score"""
        if not profile.capability_scores:
            profile.overall_score = 0.0
            return
        
        # Weight by capability importance
        weighted_sum = 0.0
        total_weight = 0.0
        
        for capability, score in profile.capability_scores.items():
            template = self.factory.capability_templates.get(capability)
            weight = template.weight if template else 1.0
            
            # Core capabilities get higher weight
            if template and template.is_core:
                weight *= 1.5
            
            weighted_sum += score.score * weight
            total_weight += weight
        
        profile.overall_score = weighted_sum / total_weight if total_weight > 0 else 0.0
    
    def _update_strengths_weaknesses(self, profile: AgentCapabilityProfile):
        """Update agent's strengths and weaknesses"""
        if not profile.capability_scores:
            return
        
        # Sort capabilities by score
        sorted_capabilities = sorted(
            profile.capability_scores.items(),
            key=lambda x: x[1].score,
            reverse=True
        )
        
        # Top 3 are strengths
        profile.strengths = [cap for cap, _ in sorted_capabilities[:3]]
        
        # Bottom 3 are weaknesses (only if score is below 0.6)
        weaknesses = [(cap, score) for cap, score in sorted_capabilities if score.score < 0.6]
        profile.weaknesses = [cap for cap, _ in weaknesses[-3:]]
        
        # Generate recommendations
        profile.recommendations = self._generate_recommendations(profile)
    
    def _generate_recommendations(self, profile: AgentCapabilityProfile) -> List[str]:
        """Generate improvement recommendations"""
        recommendations = []
        
        # Recommend improving weak core capabilities
        for capability, score in profile.capability_scores.items():
            template = self.factory.capability_templates.get(capability)
            if template and template.is_core and score.score < 0.6:
                recommendations.append(f"Focus on improving {capability} - it's a core capability")
        
        # Recommend related capabilities
        for weakness in profile.weaknesses:
            template = self.factory.capability_templates.get(weakness)
            if template:
                for related in template.related_capabilities:
                    if related not in profile.capability_scores:
                        recommendations.append(f"Consider developing {related} to support {weakness}")
        
        return recommendations[:5]  # Top 5 recommendations
    
    def get_capability_profile(self, agent_id: str) -> Optional[AgentCapabilityProfile]:
        """Get agent's capability profile"""
        return self.factory.capability_profiles.get(agent_id)
    
    def get_agent_fitness_score(self, agent_id: str, required_capabilities: List[str]) -> float:
        """
        Calculate how well an agent fits a set of required capabilities
        
        Args:
            agent_id: Agent to evaluate
            required_capabilities: List of required capabilities
            
        Returns:
            Fitness score (0.0 to 1.0)
        """
        profile = self.factory.capability_profiles.get(agent_id)
        if not profile:
            return 0.0
        
        if not required_capabilities:
            return profile.overall_score
        
        # Calculate average score for required capabilities
        total_score = 0.0
        found_capabilities = 0
        
        for capability in required_capabilities:
            if capability in profile.capability_scores:
                total_score += profile.capability_scores[capability].score
                found_capabilities += 1
        
        if found_capabilities == 0:
            return 0.0
        
        # Factor in coverage (how many required capabilities the agent has)
        coverage = found_capabilities / len(required_capabilities)
        avg_score = total_score / found_capabilities
        
        # Fitness is weighted average of coverage and performance
        return (coverage * 0.6) + (avg_score * 0.4)
    
    def find_best_agent_for_task(self, required_capabilities: List[str], 
                                min_score: float = 0.6) -> Optional[str]:
        """
        Find the best agent for a task based on required capabilities
        
        Args:
            required_capabilities: List of required capabilities
            min_score: Minimum fitness score threshold
            
        Returns:
            Agent ID of best fit, or None if no suitable agent found
        """
        best_agent = None
        best_score = min_score
        
        for agent_id in self.factory.capability_profiles.keys():
            score = self.get_agent_fitness_score(agent_id, required_capabilities)
            if score > best_score:
                best_score = score
                best_agent = agent_id
        
        return best_agent
    
    def get_capability_leaderboard(self, capability: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get top agents for a specific capability"""
        rankings = []
        
        for agent_id, profile in self.factory.capability_profiles.items():
            if capability in profile.capability_scores:
                score = profile.capability_scores[capability]
                rankings.append({
                    "agent_id": agent_id,
                    "score": score.score,
                    "level": score.level.value,
                    "evidence_count": score.evidence_count,
                    "last_updated": score.last_updated.isoformat()
                })
        
        # Sort by score descending
        rankings.sort(key=lambda x: x["score"], reverse=True)
        
        return rankings[:limit]
    
    def get_capability_statistics(self) -> Dict[str, Any]:
        """Get comprehensive capability statistics"""
        total_profiles = len(self.factory.capability_profiles)
        total_evaluations = len(self.factory.capability_evaluations)
        
        # Capability distribution
        capability_counts = {}
        for profile in self.factory.capability_profiles.values():
            for capability in profile.capability_scores.keys():
                capability_counts[capability] = capability_counts.get(capability, 0) + 1
        
        # Average scores by capability
        capability_averages = {}
        for capability in capability_counts.keys():
            scores = []
            for profile in self.factory.capability_profiles.values():
                if capability in profile.capability_scores:
                    scores.append(profile.capability_scores[capability].score)
            
            if scores:
                capability_averages[capability] = sum(scores) / len(scores)
        
        return {
            "total_profiles": total_profiles,
            "total_evaluations": total_evaluations,
            "capability_distribution": capability_counts,
            "capability_averages": capability_averages,
            "available_benchmarks": list(self.factory.capability_benchmarks.keys()),
            "capability_templates": list(self.factory.capability_templates.keys())
        }
    
@dataclass
class AgentTemplate:
    """Comprehensive agent template with advanced features"""
    id: str
    name: str
    metadata: TemplateMetadata
    
    # Template content
    role: AgentRole
    base_prompt: str
    capabilities: List[str] = field(default_factory=list)
    
    # Model configuration
    preferred_models: List[str] = field(default_factory=list)
    model_config: Dict[str, Any] = field(default_factory=dict)
    
    # Advanced features
    variables: Dict[str, Any] = field(default_factory=dict)  # Template variables
    conditions: Dict[str, Any] = field(default_factory=dict)  # Conditional logic
    inheritance: Dict[str, Any] = field(default_factory=dict)  # Inheritance settings
    composition: List[str] = field(default_factory=list)  # Other templates to compose
    
    # Validation and quality
    validation_rules: Dict[str, Any] = field(default_factory=dict)
    quality_metrics: Dict[str, float] = field(default_factory=dict)
    
    # Usage tracking
    usage_stats: Optional[TemplateUsageStats] = None
    ratings: List[TemplateRating] = field(default_factory=list)
    
    def __post_init__(self):
        """Initialize template after creation"""
        if not self.usage_stats:
            self.usage_stats = TemplateUsageStats(template_id=self.id)
    
    @property
    def average_rating(self) -> float:
        """Calculate average rating"""
        if not self.ratings:
            return 0.0
        return sum(r.rating for r in self.ratings) / len(self.ratings)
    
    @property
    def is_deprecated(self) -> bool:
        """Check if template is deprecated"""
        return self.metadata.status == TemplateStatus.DEPRECATED
    
    @property
    def is_public(self) -> bool:
        """Check if template is public"""
        return self.metadata.is_public
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate"""
        if not self.usage_stats or self.usage_stats.usage_count == 0:
            return 0.0
        return self.usage_stats.success_count / self.usage_stats.usage_count

class TemplateManager:
    """Comprehensive template management system"""
    
    def __init__(self, factory: 'AgentFactory'):
        self.factory = factory
        self._template_validators = {}
        self._template_processors = {}
        self._template_search_index = {}
        self._initialize_default_templates()
    
    def _initialize_default_templates(self):
        """Initialize default templates for common use cases"""
        # This will be populated with standard templates
        default_templates = self._get_default_templates()
        for template_data in default_templates:
            try:
                self.create_template(**template_data)
            except Exception as e:
                logger.warning(f"Failed to create default template: {e}")
    
    def _get_default_templates(self) -> List[Dict[str, Any]]:
        """Get default template configurations"""
        return [
            {
                "name": "Code Review Specialist",
                "role": AgentRole.CRITIC,
                "base_prompt": "You are a skilled code reviewer with expertise in software quality assurance. Analyze code for bugs, security issues, performance problems, and maintainability concerns. Provide specific, actionable feedback.",
                "capabilities": ["code_review", "security_analysis", "performance_review", "bug_detection"],
                "category": TemplateCategory.ROLE_SPECIFIC,
                "tags": {"code", "review", "quality", "security"},
                "author": "system",
                "description": "Specialized template for comprehensive code review tasks",
                "preferred_models": ["gpt-4", "claude-3-opus"],
                "model_config": {"temperature": 0.3, "max_tokens": 3000}
            },
            {
                "name": "Task Planner",
                "role": AgentRole.PLANNER,
                "base_prompt": "You are an expert project planner specializing in breaking down complex tasks into manageable steps. Create detailed, actionable plans with clear dependencies and timelines.",
                "capabilities": ["task_decomposition", "resource_allocation", "timeline_management", "dependency_analysis"],
                "category": TemplateCategory.ROLE_SPECIFIC,
                "tags": {"planning", "project", "organization", "strategy"},
                "author": "system",
                "description": "Professional template for project planning and task breakdown",
                "preferred_models": ["gpt-4", "claude-3-sonnet"],
                "model_config": {"temperature": 0.5, "max_tokens": 4000}
            },
            {
                "name": "Python Developer",
                "role": AgentRole.CODER,
                "base_prompt": "You are a senior Python developer with deep expertise in Python best practices, frameworks, and libraries. Write clean, efficient, and well-documented Python code.",
                "capabilities": ["code_generation", "refactoring", "debugging", "testing", "documentation"],
                "category": TemplateCategory.DOMAIN_SPECIFIC,
                "tags": {"python", "development", "coding", "backend"},
                "author": "system",
                "description": "Specialized template for Python development tasks",
                "preferred_models": ["gpt-4", "claude-3-opus"],
                "model_config": {"temperature": 0.2, "max_tokens": 4000}
            }
        ]
    
    # Template CRUD Operations
    
    def create_template(
        self,
        name: str,
        role: AgentRole,
        base_prompt: str,
        author: str,
        capabilities: List[str] = None,
        category: TemplateCategory = TemplateCategory.GENERAL_PURPOSE,
        tags: Set[str] = None,
        description: str = "",
        preferred_models: List[str] = None,
        model_config: Dict[str, Any] = None,
        variables: Dict[str, Any] = None,
        conditions: Dict[str, Any] = None,
        inheritance: Dict[str, Any] = None,
        composition: List[str] = None,
        validation_rules: Dict[str, Any] = None,
        is_public: bool = False,
        parent_template_id: Optional[str] = None,
        **kwargs
    ) -> AgentTemplate:
        """
        Create a new agent template
        
        Args:
            name: Template name
            role: Agent role
            base_prompt: Base prompt template
            author: Template author
            capabilities: List of capabilities
            category: Template category
            tags: Set of tags
            description: Template description
            preferred_models: Preferred models for this template
            model_config: Model configuration
            variables: Template variables
            conditions: Conditional logic
            inheritance: Inheritance settings
            composition: Templates to compose
            validation_rules: Validation rules
            is_public: Whether template is public
            parent_template_id: Parent template ID for inheritance
            **kwargs: Additional arguments
            
        Returns:
            AgentTemplate: Created template
            
        Raises:
            ValueError: If template validation fails
        """
        # Generate unique template ID
        template_id = f"template_{uuid.uuid4().hex[:12]}"
        
        # Set defaults
        capabilities = capabilities or []
        tags = tags or set()
        preferred_models = preferred_models or []
        model_config = model_config or {}
        variables = variables or {}
        conditions = conditions or {}
        inheritance = inheritance or {}
        composition = composition or []
        validation_rules = validation_rules or {}
        
        # Create metadata
        metadata = TemplateMetadata(
            author=author,
            description=description,
            category=category,
            tags=tags,
            is_public=is_public,
            parent_template_id=parent_template_id,
            **{k: v for k, v in kwargs.items() if k in TemplateMetadata.__annotations__}
        )
        
        # Create template
        template = AgentTemplate(
            id=template_id,
            name=name,
            metadata=metadata,
            role=role,
            base_prompt=base_prompt,
            capabilities=capabilities,
            preferred_models=preferred_models,
            model_config=model_config,
            variables=variables,
            conditions=conditions,
            inheritance=inheritance,
            composition=composition,
            validation_rules=validation_rules
        )
        
        # Validate template
        validation_result = self.validate_template(template)
        if not validation_result["valid"]:
            raise ValueError(f"Template validation failed: {validation_result['errors']}")
        
        # Process template (inheritance, composition, etc.)
        processed_template = self._process_template(template)
        
        # Store template
        self.factory.templates[template_id] = processed_template
        
        # Update search index
        self._update_search_index(processed_template)
        
        # Update tag mapping
        for tag in tags:
            if tag not in self.factory.template_tags:
                self.factory.template_tags[tag] = set()
            self.factory.template_tags[tag].add(template_id)
        
        # Initialize usage stats
        self.factory.template_usage_stats[template_id] = TemplateUsageStats(template_id=template_id)
        
        # Initialize version history
        self.factory.template_history[template_id] = [
            TemplateVersion(
                template_id=template_id,
                version=metadata.version,
                content=self._template_to_dict(processed_template),
                metadata=metadata,
                change_log="Initial template creation"
            )
        ]
        
        logger.info(f"Created template '{name}' with ID {template_id}")
        return processed_template
    
    def get_template(self, template_id: str) -> Optional[AgentTemplate]:
        """Get template by ID"""
        return self.factory.templates.get(template_id)
    
    def get_template_by_name(self, name: str) -> Optional[AgentTemplate]:
        """Get template by name"""
        for template in self.factory.templates.values():
            if template.name == name:
                return template
        return None
    
    def update_template(
        self,
        template_id: str,
        updates: Dict[str, Any],
        author: str,
        change_log: str = "",
        is_breaking_change: bool = False
    ) -> Optional[AgentTemplate]:
        """
        Update existing template
        
        Args:
            template_id: Template ID to update
            updates: Dictionary of updates to apply
            author: Author of the update
            change_log: Description of changes
            is_breaking_change: Whether this is a breaking change
            
        Returns:
            Updated template or None if not found
        """
        template = self.factory.templates.get(template_id)
        if not template:
            return None
        
        # Create new version
        old_version = template.metadata.version
        new_version = self._increment_version(old_version, is_breaking_change)
        
        # Apply updates
        updated_template = deepcopy(template)
        
        # Update template fields
        for key, value in updates.items():
            if hasattr(updated_template, key):
                setattr(updated_template, key, value)
            elif hasattr(updated_template.metadata, key):
                setattr(updated_template.metadata, key, value)
        
        # Update metadata
        updated_template.metadata.version = new_version
        updated_template.metadata.updated_at = datetime.utcnow()
        
        # Validate updated template
        validation_result = self.validate_template(updated_template)
        if not validation_result["valid"]:
            raise ValueError(f"Template validation failed: {validation_result['errors']}")
        
        # Process template
        processed_template = self._process_template(updated_template)
        
        # Store updated template
        self.factory.templates[template_id] = processed_template
        
        # Update search index
        self._update_search_index(processed_template)
        
        # Update tag mapping
        old_tags = template.metadata.tags
        new_tags = processed_template.metadata.tags
        
        # Remove old tags
        for tag in old_tags - new_tags:
            if tag in self.factory.template_tags:
                self.factory.template_tags[tag].discard(template_id)
                if not self.factory.template_tags[tag]:
                    del self.factory.template_tags[tag]
        
        # Add new tags
        for tag in new_tags - old_tags:
            if tag not in self.factory.template_tags:
                self.factory.template_tags[tag] = set()
            self.factory.template_tags[tag].add(template_id)
        
        # Add to version history
        if template_id in self.factory.template_history:
            self.factory.template_history[template_id].append(
                TemplateVersion(
                    template_id=template_id,
                    version=new_version,
                    content=self._template_to_dict(processed_template),
                    metadata=processed_template.metadata,
                    change_log=change_log,
                    is_breaking_change=is_breaking_change
                )
            )
        
        logger.info(f"Updated template '{template.name}' to version {new_version}")
        return processed_template
    
    def delete_template(self, template_id: str) -> bool:
        """
        Delete template (soft delete - mark as archived)
        
        Args:
            template_id: Template ID to delete
            
        Returns:
            True if template was deleted, False if not found
        """
        template = self.factory.templates.get(template_id)
        if not template:
            return False
        
        # Mark as archived instead of hard delete
        template.metadata.status = TemplateStatus.ARCHIVED
        template.metadata.updated_at = datetime.utcnow()
        
        # Remove from search index
        self._remove_from_search_index(template_id)
        
        logger.info(f"Archived template '{template.name}' (ID: {template_id})")
        return True
    
    def hard_delete_template(self, template_id: str) -> bool:
        """
        Permanently delete template
        
        Args:
            template_id: Template ID to delete
            
        Returns:
            True if template was deleted, False if not found
        """
        template = self.factory.templates.get(template_id)
        if not template:
            return False
        
        # Remove from all collections
        del self.factory.templates[template_id]
        
        # Remove from tag mappings
        for tag in template.metadata.tags:
            if tag in self.factory.template_tags:
                self.factory.template_tags[tag].discard(template_id)
                if not self.factory.template_tags[tag]:
                    del self.factory.template_tags[tag]
        
        # Remove usage stats
        if template_id in self.factory.template_usage_stats:
            del self.factory.template_usage_stats[template_id]
        
        # Remove ratings
        if template_id in self.factory.template_ratings:
            del self.factory.template_ratings[template_id]
        
        # Remove version history
        if template_id in self.factory.template_history:
            del self.factory.template_history[template_id]
        
        # Remove from search index
        self._remove_from_search_index(template_id)
        
        logger.info(f"Permanently deleted template '{template.name}' (ID: {template_id})")
        return True
    
    # Template Processing and Validation Methods
    
    def validate_template(self, template: AgentTemplate) -> Dict[str, Any]:
        """
        Validate template configuration
        
        Args:
            template: Template to validate
            
        Returns:
            Dict containing validation results
        """
        errors = []
        warnings = []
        
        # Validate required fields
        if not template.name:
            errors.append("Template name is required")
        if not template.base_prompt:
            errors.append("Base prompt is required")
        
        # Validate role
        if template.role not in self.factory.AGENT_CLASSES:
            errors.append(f"Unsupported role: {template.role}")
        
        # Validate model preferences
        if template.preferred_models:
            all_models = [m for models in self.factory.SUPPORTED_MODELS.values() for m in models]
            invalid_models = [m for m in template.preferred_models if m not in all_models]
            if invalid_models:
                errors.append(f"Invalid preferred models: {invalid_models}")
        
        # Validate model config
        if template.model_config:
            if "temperature" in template.model_config:
                temp = template.model_config["temperature"]
                if not isinstance(temp, (int, float)) or temp < 0 or temp > 2:
                    errors.append("Temperature must be between 0 and 2")
            
            if "max_tokens" in template.model_config:
                max_tokens = template.model_config["max_tokens"]
                if not isinstance(max_tokens, int) or max_tokens < 1 or max_tokens > 8000:
                    errors.append("max_tokens must be between 1 and 8000")
        
        # Validate capabilities
        if template.capabilities:
            capability_validation = self.factory.validate_capability_combinations(
                template.capabilities, template.role
            )
            if not capability_validation["valid"]:
                errors.extend(capability_validation["errors"])
            warnings.extend(capability_validation["warnings"])
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }
    
    def _process_template(self, template: AgentTemplate) -> AgentTemplate:
        """
        Process template for inheritance, composition, and variable substitution
        
        Args:
            template: Template to process
            
        Returns:
            Processed template
        """
        processed_template = deepcopy(template)
        
        # Process inheritance
        if template.inheritance and template.metadata.parent_template_id:
            parent_template = self.factory.templates.get(template.metadata.parent_template_id)
            if parent_template:
                processed_template = self._apply_inheritance(processed_template, parent_template)
        
        # Process composition
        if template.composition:
            processed_template = self._apply_composition(processed_template)
        
        # Process variables
        if template.variables:
            processed_template = self._apply_variables(processed_template)
        
        return processed_template
    
    def _apply_inheritance(self, template: AgentTemplate, parent: AgentTemplate) -> AgentTemplate:
        """Apply inheritance from parent template"""
        # Inherit capabilities if not overridden
        if not template.capabilities:
            template.capabilities = parent.capabilities.copy()
        
        # Inherit model config if not overridden
        if not template.model_config:
            template.model_config = parent.model_config.copy()
        
        # Inherit preferred models if not overridden
        if not template.preferred_models:
            template.preferred_models = parent.preferred_models.copy()
        
        return template
    
    def _apply_composition(self, template: AgentTemplate) -> AgentTemplate:
        """Apply composition from other templates"""
        for template_id in template.composition:
            composed_template = self.factory.templates.get(template_id)
            if composed_template:
                # Merge capabilities
                template.capabilities.extend(composed_template.capabilities)
                template.capabilities = list(set(template.capabilities))  # Remove duplicates
                
                # Merge model config
                template.model_config.update(composed_template.model_config)
        
        return template
    
    def _apply_variables(self, template: AgentTemplate) -> AgentTemplate:
        """Apply variable substitution to template"""
        # Replace variables in base prompt
        prompt = template.base_prompt
        for var_name, var_value in template.variables.items():
            prompt = prompt.replace(f"{{{var_name}}}", str(var_value))
        template.base_prompt = prompt
        
        return template
    
    def _increment_version(self, version: str, is_breaking: bool = False) -> str:
        """Increment version number"""
        parts = version.split('.')
        if len(parts) != 3:
            return "1.0.0"
        
        major, minor, patch = map(int, parts)
        
        if is_breaking:
            major += 1
            minor = 0
            patch = 0
        else:
            minor += 1
            patch = 0
        
        return f"{major}.{minor}.{patch}"
    
    def _template_to_dict(self, template: AgentTemplate) -> Dict[str, Any]:
        """Convert template to dictionary for serialization"""
        return {
            "id": template.id,
            "name": template.name,
            "metadata": {
                "author": template.metadata.author,
                "version": template.metadata.version,
                "description": template.metadata.description,
                "category": template.metadata.category.value,
                "tags": list(template.metadata.tags),
                "created_at": template.metadata.created_at.isoformat(),
                "updated_at": template.metadata.updated_at.isoformat(),
                "status": template.metadata.status.value,
                "is_public": template.metadata.is_public,
                "parent_template_id": template.metadata.parent_template_id,
                "derived_from": template.metadata.derived_from,
                "compatibility_version": template.metadata.compatibility_version,
                "license": template.metadata.license
            },
            "role": template.role.value,
            "base_prompt": template.base_prompt,
            "capabilities": template.capabilities,
            "preferred_models": template.preferred_models,
            "model_config": template.model_config,
            "variables": template.variables,
            "conditions": template.conditions,
            "inheritance": template.inheritance,
            "composition": template.composition,
            "validation_rules": template.validation_rules,
            "quality_metrics": template.quality_metrics
        }
    
    def _update_search_index(self, template: AgentTemplate):
        """Update search index for template"""
        search_text = f"{template.name} {template.metadata.description} {' '.join(template.metadata.tags)}"
        self._template_search_index[template.id] = search_text.lower()
    
    def _remove_from_search_index(self, template_id: str):
        """Remove template from search index"""
        if template_id in self._template_search_index:
            del self._template_search_index[template_id]
    
    # Template Search and Discovery Methods
    
    def search_templates(
        self,
        query: str = None,
        role: AgentRole = None,
        category: TemplateCategory = None,
        tags: List[str] = None,
        author: str = None,
        status: TemplateStatus = None,
        min_rating: float = None,
        limit: int = None
    ) -> List[AgentTemplate]:
        """
        Advanced template search with multiple filters
        
        Args:
            query: Search query
            role: Filter by agent role
            category: Filter by template category
            tags: Filter by tags (must have all specified tags)
            author: Filter by author
            status: Filter by template status
            min_rating: Minimum rating threshold
            limit: Maximum number of results
            
        Returns:
            List of matching templates
        """
        results = []
        
        for template in self.factory.templates.values():
            # Status filter
            if status and template.metadata.status != status:
                continue
            
            # Role filter
            if role and template.role != role:
                continue
            
            # Category filter
            if category and template.metadata.category != category:
                continue
            
            # Author filter
            if author and template.metadata.author != author:
                continue
            
            # Tags filter
            if tags and not all(tag in template.metadata.tags for tag in tags):
                continue
            
            # Rating filter
            if min_rating and template.average_rating < min_rating:
                continue
            
            # Query filter
            if query:
                search_text = self._template_search_index.get(template.id, "")
                if query.lower() not in search_text:
                    continue
            
            results.append(template)
        
        # Sort by rating and usage
        results.sort(key=lambda t: (t.average_rating, t.usage_stats.usage_count), reverse=True)
        
        # Apply limit
        if limit:
            results = results[:limit]
        
        return results
    
    def get_templates_by_category(self, category: TemplateCategory) -> List[AgentTemplate]:
        """Get all templates in a specific category"""
        return [
            template for template in self.factory.templates.values()
            if template.metadata.category == category and template.metadata.status == TemplateStatus.ACTIVE
        ]
    
    def get_templates_by_tag(self, tag: str) -> List[AgentTemplate]:
        """Get all templates with a specific tag"""
        template_ids = self.factory.template_tags.get(tag, set())
        return [
            self.factory.templates[tid] for tid in template_ids
            if tid in self.factory.templates and self.factory.templates[tid].metadata.status == TemplateStatus.ACTIVE
        ]
    
    def get_popular_templates(self, limit: int = 10) -> List[AgentTemplate]:
        """Get most popular templates by usage"""
        templates = [
            template for template in self.factory.templates.values()
            if template.metadata.status == TemplateStatus.ACTIVE
        ]
        templates.sort(key=lambda t: t.usage_stats.usage_count, reverse=True)
        return templates[:limit]
    
    def get_highly_rated_templates(self, limit: int = 10) -> List[AgentTemplate]:
        """Get highest rated templates"""
        templates = [
            template for template in self.factory.templates.values()
            if template.metadata.status == TemplateStatus.ACTIVE and template.ratings
        ]
        templates.sort(key=lambda t: t.average_rating, reverse=True)
        return templates[:limit]
    
    def get_template_statistics(self) -> Dict[str, Any]:
        """Get comprehensive template statistics"""
        templates = list(self.factory.templates.values())
        active_templates = [t for t in templates if t.metadata.status == TemplateStatus.ACTIVE]
        
        # Category distribution
        category_counts = {}
        for template in active_templates:
            category = template.metadata.category.value
            category_counts[category] = category_counts.get(category, 0) + 1
        
        # Role distribution
        role_counts = {}
        for template in active_templates:
            role = template.role.value
            role_counts[role] = role_counts.get(role, 0) + 1
        
        # Usage statistics
        total_usage = sum(t.usage_stats.usage_count for t in active_templates)
        avg_rating = sum(t.average_rating for t in active_templates if t.ratings) / max(len([t for t in active_templates if t.ratings]), 1)
        
        return {
            "total_templates": len(templates),
            "active_templates": len(active_templates),
            "category_distribution": category_counts,
            "role_distribution": role_counts,
            "total_usage": total_usage,
            "average_rating": avg_rating,
            "most_popular": self.get_popular_templates(5),
            "highest_rated": self.get_highly_rated_templates(5)
        }
    
    # Template Rating and Community Features
    
    def rate_template(self, template_id: str, user_id: str, rating: int, comment: str = "") -> bool:
        """
        Rate a template
        
        Args:
            template_id: Template ID to rate
            user_id: User providing the rating
            rating: Rating (1-5 stars)
            comment: Optional comment
            
        Returns:
            True if rating was added, False otherwise
        """
        if not 1 <= rating <= 5:
            raise ValueError("Rating must be between 1 and 5")
        
        template = self.factory.templates.get(template_id)
        if not template:
            return False
        
        # Check if user already rated this template
        existing_rating = next(
            (r for r in template.ratings if r.user_id == user_id), None
        )
        
        if existing_rating:
            # Update existing rating
            existing_rating.rating = rating
            existing_rating.comment = comment
            existing_rating.created_at = datetime.utcnow()
        else:
            # Add new rating
            new_rating = TemplateRating(
                template_id=template_id,
                user_id=user_id,
                rating=rating,
                comment=comment
            )
            template.ratings.append(new_rating)
            
            # Update factory ratings collection
            if template_id not in self.factory.template_ratings:
                self.factory.template_ratings[template_id] = []
            self.factory.template_ratings[template_id].append(new_rating)
        
        logger.info(f"User {user_id} rated template '{template.name}': {rating}/5")
        return True
    
    def get_template_ratings(self, template_id: str) -> List[TemplateRating]:
        """Get all ratings for a template"""
        return self.factory.template_ratings.get(template_id, [])
    
    def vote_helpful(self, template_id: str, user_id: str, rating_user_id: str) -> bool:
        """
        Vote a rating as helpful
        
        Args:
            template_id: Template ID
            user_id: User voting
            rating_user_id: User who wrote the rating
            
        Returns:
            True if vote was added, False otherwise
        """
        template = self.factory.templates.get(template_id)
        if not template:
            return False
        
        rating = next(
            (r for r in template.ratings if r.user_id == rating_user_id), None
        )
        
        if rating:
            rating.helpful_votes += 1
            logger.info(f"User {user_id} voted rating by {rating_user_id} as helpful")
            return True
        
        return False
    
    # Template Usage Tracking
    
    def record_template_usage(self, template_id: str, agent_id: str, success: bool = True, performance_score: float = 0.0) -> bool:
        """
        Record template usage statistics
        
        Args:
            template_id: Template ID
            agent_id: Agent ID created from template
            success: Whether usage was successful
            performance_score: Performance score (0.0-1.0)
            
        Returns:
            True if recorded, False otherwise
        """
        template = self.factory.templates.get(template_id)
        if not template:
            return False
        
        stats = template.usage_stats
        stats.usage_count += 1
        stats.last_used = datetime.utcnow()
        stats.created_agents.append(agent_id)
        
        if success:
            stats.success_count += 1
        else:
            stats.failure_count += 1
        
        # Update average performance score
        if stats.usage_count == 1:
            stats.avg_performance_score = performance_score
        else:
            stats.avg_performance_score = (
                (stats.avg_performance_score * (stats.usage_count - 1) + performance_score) / stats.usage_count
            )
        
        logger.info(f"Recorded usage for template '{template.name}' (success: {success})")
        return True
    
    def get_template_usage_stats(self, template_id: str) -> Optional[TemplateUsageStats]:
        """Get usage statistics for a template"""
        template = self.factory.templates.get(template_id)
        return template.usage_stats if template else None
    
    def get_template_version_history(self, template_id: str) -> List[TemplateVersion]:
        """Get version history for a template"""
        return self.factory.template_history.get(template_id, [])
    
    # Template Export/Import
    
    def export_template(self, template_id: str) -> Dict[str, Any]:
        """
        Export template to JSON format
        
        Args:
            template_id: Template ID to export
            
        Returns:
            Template data as dictionary
        """
        template = self.factory.templates.get(template_id)
        if not template:
            raise ValueError(f"Template not found: {template_id}")
        
        export_data = self._template_to_dict(template)
        export_data["export_metadata"] = {
            "export_timestamp": datetime.utcnow().isoformat(),
            "exporter": "template_manager",
            "version": "1.0.0"
        }
        
        return export_data
    
    def import_template(self, template_data: Dict[str, Any], author: str = None) -> AgentTemplate:
        """
        Import template from JSON data
        
        Args:
            template_data: Template data dictionary
            author: Override author (optional)
            
        Returns:
            Imported template
        """
        # Generate new ID to avoid conflicts
        original_id = template_data.get("id")
        new_id = f"imported_{uuid.uuid4().hex[:8]}"
        
        # Create metadata
        metadata_data = template_data.get("metadata", {})
        metadata = TemplateMetadata(
            author=author or metadata_data.get("author", "imported"),
            version=metadata_data.get("version", "1.0.0"),
            description=metadata_data.get("description", ""),
            category=TemplateCategory(metadata_data.get("category", "general_purpose")),
            tags=set(metadata_data.get("tags", [])),
            status=TemplateStatus(metadata_data.get("status", "active")),
            is_public=metadata_data.get("is_public", False),
            license=metadata_data.get("license", "MIT")
        )
        
        # Create template
        template = AgentTemplate(
            id=new_id,
            name=template_data.get("name", "Imported Template"),
            metadata=metadata,
            role=AgentRole(template_data.get("role", "additional")),
            base_prompt=template_data.get("base_prompt", ""),
            capabilities=template_data.get("capabilities", []),
            preferred_models=template_data.get("preferred_models", []),
            model_config=template_data.get("model_config", {}),
            variables=template_data.get("variables", {}),
            conditions=template_data.get("conditions", {}),
            inheritance=template_data.get("inheritance", {}),
            composition=template_data.get("composition", []),
            validation_rules=template_data.get("validation_rules", {}),
            quality_metrics=template_data.get("quality_metrics", {})
        )
        
        # Validate imported template
        validation_result = self.validate_template(template)
        if not validation_result["valid"]:
            raise ValueError(f"Imported template validation failed: {validation_result['errors']}")
        
        # Store template
        self.factory.templates[new_id] = template
        
        # Update indices
        self._update_search_index(template)
        
        # Update tag mapping
        for tag in template.metadata.tags:
            if tag not in self.factory.template_tags:
                self.factory.template_tags[tag] = set()
            self.factory.template_tags[tag].add(new_id)
        
        # Initialize stats
        self.factory.template_usage_stats[new_id] = TemplateUsageStats(template_id=new_id)
        
        logger.info(f"Imported template '{template.name}' with new ID {new_id} (original: {original_id})")
        return template
    
    def duplicate_template(self, template_id: str, new_name: str, author: str) -> Optional[AgentTemplate]:
        """
        Duplicate an existing template
        
        Args:
            template_id: Template ID to duplicate
            new_name: Name for the new template
            author: Author of the duplicate
            
        Returns:
            Duplicated template or None if original not found
        """
        template = self.factory.templates.get(template_id)
        if not template:
            return None
        
        # Export and import to create duplicate
        export_data = self.export_template(template_id)
        export_data["name"] = new_name
        
        return self.import_template(export_data, author)
    
    # Template Utility Methods
    
    def list_all_templates(self, include_archived: bool = False) -> List[AgentTemplate]:
        """List all templates"""
        templates = list(self.factory.templates.values())
        
        if not include_archived:
            templates = [t for t in templates if t.metadata.status != TemplateStatus.ARCHIVED]
        
        return templates
    
    def get_template_names(self) -> List[str]:
        """Get all template names"""
        return [t.name for t in self.factory.templates.values()]
    
    def get_template_categories(self) -> List[TemplateCategory]:
        """Get all template categories in use"""
        categories = set()
        for template in self.factory.templates.values():
            categories.add(template.metadata.category)
        return list(categories)
    
    def get_all_tags(self) -> List[str]:
        """Get all tags used in templates"""
        return list(self.factory.template_tags.keys())
    
    def cleanup_unused_tags(self) -> int:
        """Clean up unused tags and return count of removed tags"""
        removed_count = 0
        tags_to_remove = []
        
        for tag, template_ids in self.factory.template_tags.items():
            # Remove template IDs that no longer exist
            valid_ids = {tid for tid in template_ids if tid in self.factory.templates}
            
            if not valid_ids:
                tags_to_remove.append(tag)
                removed_count += 1
            else:
                self.factory.template_tags[tag] = valid_ids
        
        # Remove empty tags
        for tag in tags_to_remove:
            del self.factory.template_tags[tag]
        
        if removed_count > 0:
            logger.info(f"Cleaned up {removed_count} unused tags")
        
        return removed_count
    
    def get_template_dependencies(self, template_id: str) -> Dict[str, List[str]]:
        """
        Get template dependencies (composition and inheritance)
        
        Args:
            template_id: Template ID
            
        Returns:
            Dict with 'parents', 'children', and 'composed' dependencies
        """
        template = self.factory.templates.get(template_id)
        if not template:
            return {"parents": [], "children": [], "composed": []}
        
        # Find parents (inheritance)
        parents = []
        if template.metadata.parent_template_id:
            parents.append(template.metadata.parent_template_id)
        
        # Find children (templates that inherit from this one)
        children = []
        for t in self.factory.templates.values():
            if t.metadata.parent_template_id == template_id:
                children.append(t.id)
        
        # Find composed templates
        composed = list(template.composition)
        
        return {
            "parents": parents,
            "children": children,
            "composed": composed
        }

class AgentFactory:
    """Factory class for creating and managing agent instances"""
    
    # Agent class registry
    AGENT_CLASSES = {
        AgentRole.PLANNER: PlannerAgent,
        AgentRole.CRITIC: CriticAgent,
        AgentRole.ADDITIONAL: AdditionalAgent,
        AgentRole.CODER: AdditionalAgent,  # Will be specialized later
        AgentRole.DEBUGGER: AdditionalAgent,  # Will be specialized later
        AgentRole.PM: AdditionalAgent,  # Will be specialized later
        AgentRole.UX: AdditionalAgent,  # Will be specialized later
        AgentRole.QA: AdditionalAgent,  # Will be specialized later
        AgentRole.ORCHESTRATOR: PlannerAgent  # Will be specialized later
    }
    
    # Supported models for agent creation
    SUPPORTED_MODELS = {
        'openai': ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
        'anthropic': ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku'],
        'google': ['gemini-pro', 'gemini-pro-vision']
    }
    
    def __init__(self):
        self.active_agents: Dict[str, BaseAgent] = {}
        self.agent_sessions: Dict[str, str] = {}  # session_id -> agent_id mapping
        self.agent_profiles: Dict[str, AgentProfile] = {}  # agent_id -> profile mapping
        
        # Template Management System
        self.templates: Dict[str, 'AgentTemplate'] = {}  # template_id -> template
        self.template_categories: Dict[str, 'TemplateCategory'] = {}  # category_id -> category
        self.template_tags: Dict[str, Set[str]] = {}  # tag -> set of template_ids
        self.template_usage_stats: Dict[str, 'TemplateUsageStats'] = {}  # template_id -> usage stats
        self.template_ratings: Dict[str, List['TemplateRating']] = {}  # template_id -> list of ratings
        self.template_history: Dict[str, List['TemplateVersion']] = {}  # template_id -> version history
        
        # Initialize template manager
        self.template_manager = TemplateManager(self)
        
        # Capability Scoring System
        self.capability_profiles: Dict[str, AgentCapabilityProfile] = {}  # agent_id -> capability profile
        self.capability_benchmarks: Dict[str, CapabilityBenchmark] = {}  # capability -> benchmark
        self.capability_templates: Dict[str, CapabilityTemplate] = {}  # capability -> template
        self.capability_evaluations: List[CapabilityEvaluation] = []  # All evaluations
        self.capability_analytics: Dict[str, Any] = {}  # Analytics cache
        
        # Initialize capability system
        self._initialize_capability_system()
        
    def create_agent(
        self,
        role: AgentRole,
        task_description: str,
        agent_id: Optional[str] = None,
        session_id: Optional[str] = None,
        **kwargs
    ) -> BaseAgent:
        """
        Create a new agent instance
        
        Args:
            role: Agent role (planner, critic, additional)
            task_description: Task description for the agent
            agent_id: Unique identifier for the agent (generated if not provided)
            session_id: Session identifier (generated if not provided)
            **kwargs: Additional configuration parameters
            
        Returns:
            BaseAgent: Configured agent instance
            
        Raises:
            ValueError: If role is not supported
        """
        
        if role not in self.AGENT_CLASSES:
            raise ValueError(f"Unsupported agent role: {role}")
        
        # Generate IDs if not provided
        if not agent_id:
            agent_id = f"{role.value}_{uuid.uuid4().hex[:8]}"
        
        if not session_id:
            session_id = f"session_{uuid.uuid4().hex[:12]}"
        
        # Create agent configuration
        config = AgentConfig(
            role=role,
            agent_id=agent_id,
            session_id=session_id,
            task_description=task_description,
            **kwargs
        )
        
        # Validate configuration
        config_validation = self.validate_agent_config(config)
        if not config_validation["valid"]:
            raise ValueError(f"Agent configuration validation failed: {config_validation['errors']}")
        
        # Log validation warnings
        if config_validation.get("warnings"):
            logger.warning(f"Agent {agent_id} created with warnings: {config_validation['warnings']}")
        
        # Instantiate agent
        agent_class = self.AGENT_CLASSES[role]
        agent = agent_class(config)
        
        # Register agent
        self.active_agents[agent_id] = agent
        self.agent_sessions[session_id] = agent_id
        
        logger.info(f"Created {role.value} agent: {agent_id} (session: {session_id})")
        
        return agent
    
    def get_agent(self, agent_id: str) -> Optional[BaseAgent]:
        """Get agent by ID"""
        return self.active_agents.get(agent_id)
    
    def get_agent_by_session(self, session_id: str) -> Optional[BaseAgent]:
        """Get agent by session ID"""
        agent_id = self.agent_sessions.get(session_id)
        if agent_id:
            return self.active_agents.get(agent_id)
        return None
    
    def get_agents_by_role(self, role: AgentRole) -> Dict[str, BaseAgent]:
        """Get all agents with specified role"""
        return {
            agent_id: agent 
            for agent_id, agent in self.active_agents.items()
            if agent.role == role
        }
    
    def list_active_agents(self) -> Dict[str, Dict[str, Any]]:
        """List all active agents with their status"""
        return {
            agent_id: {
                "role": agent.role.value,
                "session_id": agent.session_id,
                "status": agent.status.value,
                "metrics": agent.get_performance_metrics()
            }
            for agent_id, agent in self.active_agents.items()
        }
    
    def create_agent_team(
        self,
        task_description: str,
        team_size: int = 3,
        task_id: Optional[str] = None,
        **kwargs
    ) -> Dict[AgentRole, BaseAgent]:
        """
        Create a complete agent team for multi-agent collaboration
        
        Args:
            task_description: Task description for all agents
            team_size: Number of agents (2-5, defaults to 3)
            task_id: Task identifier for session management
            **kwargs: Additional configuration parameters
            
        Returns:
            Dict[AgentRole, BaseAgent]: Dictionary of role -> agent mappings
            
        Raises:
            ValueError: If team_size is invalid
        """
        
        if team_size < 2 or team_size > 5:
            raise ValueError("Team size must be between 2 and 5 agents")
        
        if not task_id:
            task_id = f"task_{uuid.uuid4().hex[:8]}"
        
        # Determine team roles based on size
        team_roles = [AgentRole.PLANNER, AgentRole.CRITIC]
        additional_roles = [AgentRole.ADDITIONAL] * (team_size - 2)
        team_roles.extend(additional_roles)
        
        # Validate team composition
        team_validation = self.validate_team_composition(team_roles)
        if not team_validation["valid"]:
            raise ValueError(f"Team composition validation failed: {team_validation['errors']}")
        
        # Log validation warnings
        if team_validation.get("warnings"):
            logger.warning(f"Team {task_id} created with warnings: {team_validation['warnings']}")
        
        team = {}
        
        # Always create planner and critic
        team[AgentRole.PLANNER] = self.create_agent(
            role=AgentRole.PLANNER,
            task_description=task_description,
            agent_id=f"{task_id}_planner",
            session_id=f"{task_id}_planner_session",
            **kwargs
        )
        
        team[AgentRole.CRITIC] = self.create_agent(
            role=AgentRole.CRITIC,
            task_description=task_description,
            agent_id=f"{task_id}_critic",
            session_id=f"{task_id}_critic_session",
            **kwargs
        )
        
        # Create additional agents based on team size
        additional_count = team_size - 2
        for i in range(additional_count):
            agent_id = f"{task_id}_additional_{i+1}"
            session_id = f"{task_id}_additional_{i+1}_session"
            
            team[f"additional_{i+1}"] = self.create_agent(
                role=AgentRole.ADDITIONAL,
                task_description=task_description,
                agent_id=agent_id,
                session_id=session_id,
                **kwargs
            )
        
        logger.info(f"Created agent team for task {task_id}: {team_size} agents")
        
        return team
    
    async def shutdown_agent(self, agent_id: str) -> bool:
        """
        Shutdown and remove an agent
        
        Args:
            agent_id: Agent identifier
            
        Returns:
            bool: True if agent was found and shutdown, False otherwise
        """
        
        agent = self.active_agents.get(agent_id)
        if not agent:
            return False
        
        try:
            await agent.shutdown()
            
            # Remove from registries
            del self.active_agents[agent_id]
            
            # Remove session mapping
            session_to_remove = None
            for session_id, mapped_agent_id in self.agent_sessions.items():
                if mapped_agent_id == agent_id:
                    session_to_remove = session_id
                    break
            
            if session_to_remove:
                del self.agent_sessions[session_to_remove]
            
            logger.info(f"Shutdown agent: {agent_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error shutting down agent {agent_id}: {e}")
            return False
    
    async def shutdown_all_agents(self) -> int:
        """
        Shutdown all active agents
        
        Returns:
            int: Number of agents shutdown
        """
        
        shutdown_count = 0
        agent_ids = list(self.active_agents.keys())
        
        for agent_id in agent_ids:
            if await self.shutdown_agent(agent_id):
                shutdown_count += 1
        
        logger.info(f"Shutdown {shutdown_count} agents")
        return shutdown_count
    
    def get_factory_status(self) -> Dict[str, Any]:
        """Get factory status and statistics"""
        
        role_counts = {}
        for agent in self.active_agents.values():
            role = agent.role.value
            role_counts[role] = role_counts.get(role, 0) + 1
        
        return {
            "total_agents": len(self.active_agents),
            "active_sessions": len(self.agent_sessions),
            "agents_by_role": role_counts,
            "supported_roles": [role.value for role in self.AGENT_CLASSES.keys()]
        }
    
    def validate_team_configuration(self, team_size: int, subscription_tier: str = "free") -> bool:
        """
        Validate team configuration against subscription limits
        
        Args:
            team_size: Requested team size
            subscription_tier: Subscription tier (free, pro, dev)
            
        Returns:
            bool: True if configuration is valid
        """
        
        max_agents = {
            "free": 2,
            "pro": 3,
            "dev": 5,
            "dev+": 5
        }
        
        limit = max_agents.get(subscription_tier.lower(), 2)
        return team_size <= limit
    
    def validate_agent_creation_request(self, request: AgentCreationRequest) -> Dict[str, Any]:
        """
        Validate agent creation request with comprehensive validation
        
        Args:
            request: Agent creation request to validate
            
        Returns:
            Dict containing validation results and errors
        """
        errors = []
        warnings = []
        
        # Validate role
        if request.role not in self.AGENT_CLASSES:
            errors.append(f"Unsupported agent role: {request.role}")
        
        # Validate model
        model_supported = False
        for provider, models in self.SUPPORTED_MODELS.items():
            if request.model in models:
                model_supported = True
                break
        
        if not model_supported:
            errors.append(f"Unsupported model: {request.model}")
            warnings.append(f"Supported models: {[m for models in self.SUPPORTED_MODELS.values() for m in models]}")
        
        # Validate prompt length
        if len(request.prompt) > 10000:
            errors.append("Prompt exceeds maximum length of 10000 characters")
        elif len(request.prompt) < 10:
            warnings.append("Prompt is very short, consider adding more context")
        
        # Validate capabilities
        if len(request.capabilities) > 20:
            warnings.append("Too many capabilities may reduce agent focus")
        
        # Validate temperature
        if request.temperature < 0.1:
            warnings.append("Very low temperature may make agent too deterministic")
        elif request.temperature > 1.5:
            warnings.append("High temperature may make agent too random")
        
        # Validate token limits
        if request.max_tokens > 4000:
            warnings.append("High token limit may increase costs")
        elif request.max_tokens < 100:
            warnings.append("Low token limit may truncate responses")
        
        # Validate timeout
        if request.timeout_seconds > 120:
            warnings.append("Long timeout may impact system responsiveness")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }
    
    def validate_agent_config(self, config: AgentConfig) -> Dict[str, Any]:
        """
        Validate AgentConfig with comprehensive checks
        
        Args:
            config: Agent configuration to validate
            
        Returns:
            Dict containing validation results and errors
        """
        errors = []
        warnings = []
        
        # Validate role
        if config.role not in self.AGENT_CLASSES:
            errors.append(f"Unsupported agent role: {config.role}")
        
        # Validate IDs
        if not config.agent_id or len(config.agent_id) < 3:
            errors.append("Agent ID must be at least 3 characters long")
        
        if not config.session_id or len(config.session_id) < 3:
            errors.append("Session ID must be at least 3 characters long")
        
        # Validate model configuration
        if config.model_name not in [m for models in self.SUPPORTED_MODELS.values() for m in models]:
            errors.append(f"Unsupported model: {config.model_name}")
        
        # Validate temperature
        if config.temperature < 0.0 or config.temperature > 2.0:
            errors.append("Temperature must be between 0.0 and 2.0")
        elif config.temperature < 0.1:
            warnings.append("Very low temperature may make agent too deterministic")
        elif config.temperature > 1.5:
            warnings.append("High temperature may make agent too random")
        
        # Validate token limits
        if config.max_tokens < 1 or config.max_tokens > 8000:
            errors.append("max_tokens must be between 1 and 8000")
        elif config.max_tokens > 4000:
            warnings.append("High token limit may increase costs")
        elif config.max_tokens < 100:
            warnings.append("Low token limit may truncate responses")
        
        # Validate retries
        if config.max_retries < 0 or config.max_retries > 10:
            errors.append("max_retries must be between 0 and 10")
        elif config.max_retries > 5:
            warnings.append("High retry count may cause delays")
        
        # Validate timeout
        if config.timeout_seconds < 1 or config.timeout_seconds > 300:
            errors.append("timeout_seconds must be between 1 and 300")
        elif config.timeout_seconds > 120:
            warnings.append("Long timeout may impact system responsiveness")
        
        # Validate consensus threshold
        if config.consensus_threshold < 0.0 or config.consensus_threshold > 1.0:
            errors.append("consensus_threshold must be between 0.0 and 1.0")
        elif config.consensus_threshold < 0.5:
            warnings.append("Low consensus threshold may reduce decision quality")
        
        # Validate task description
        if not config.task_description or len(config.task_description) < 1:
            errors.append("Task description is required")
        elif len(config.task_description) > 5000:
            warnings.append("Very long task description may impact performance")
        
        # Validate context files
        if len(config.context_files) > 50:
            warnings.append("Large number of context files may impact performance")
        
        # Validate conversation history
        if len(config.conversation_history) > 100:
            warnings.append("Large conversation history may impact memory usage")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }
    
    def validate_capability_combinations(self, capabilities: List[str], role: AgentRole) -> Dict[str, Any]:
        """
        Validate capability combinations for specific agent roles
        
        Args:
            capabilities: List of capabilities to validate
            role: Agent role to validate against
            
        Returns:
            Dict containing validation results
        """
        errors = []
        warnings = []
        
        # Define valid capabilities per role
        valid_capabilities = {
            AgentRole.PLANNER: [
                "task_decomposition", "resource_allocation", "timeline_management",
                "dependency_analysis", "risk_assessment", "priority_setting"
            ],
            AgentRole.CRITIC: [
                "code_review", "quality_assurance", "security_analysis",
                "performance_review", "design_critique", "bug_detection"
            ],
            AgentRole.CODER: [
                "code_generation", "refactoring", "debugging", "testing",
                "documentation", "api_design", "database_design"
            ],
            AgentRole.DEBUGGER: [
                "error_analysis", "stack_trace_analysis", "performance_profiling",
                "memory_debugging", "race_condition_detection", "log_analysis"
            ],
            AgentRole.PM: [
                "stakeholder_management", "requirements_gathering", "sprint_planning",
                "team_coordination", "risk_management", "progress_tracking"
            ],
            AgentRole.UX: [
                "user_research", "wireframing", "prototyping", "usability_testing",
                "accessibility_analysis", "design_systems", "user_journey_mapping"
            ],
            AgentRole.QA: [
                "test_planning", "test_execution", "regression_testing",
                "automation_testing", "performance_testing", "security_testing"
            ],
            AgentRole.ORCHESTRATOR: [
                "agent_coordination", "task_distribution", "consensus_building",
                "conflict_resolution", "workflow_management", "meta_blocking"
            ]
        }
        
        role_capabilities = valid_capabilities.get(role, [])
        
        # Check for invalid capabilities
        for capability in capabilities:
            if capability not in role_capabilities:
                warnings.append(f"Capability '{capability}' may not be suitable for {role.value} role")
        
        # Check for conflicting capabilities
        conflicts = {
            "code_generation": ["code_review"],
            "task_decomposition": ["task_execution"],
            "planning": ["execution"]
        }
        
        for cap1, conflicting_caps in conflicts.items():
            if cap1 in capabilities:
                for cap2 in conflicting_caps:
                    if cap2 in capabilities:
                        warnings.append(f"Conflicting capabilities: {cap1} and {cap2}")
        
        # Check for required capability combinations
        required_combinations = {
            AgentRole.CODER: ["code_generation"],
            AgentRole.DEBUGGER: ["error_analysis"],
            AgentRole.PLANNER: ["task_decomposition"],
            AgentRole.CRITIC: ["code_review"]
        }
        
        required_caps = required_combinations.get(role, [])
        missing_required = [cap for cap in required_caps if cap not in capabilities]
        if missing_required:
            warnings.append(f"Missing recommended capabilities for {role.value}: {missing_required}")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }
    
    def validate_team_creation_request(self, request: TeamCreationRequest) -> Dict[str, Any]:
        """
        Validate team creation request with comprehensive validation
        
        Args:
            request: Team creation request to validate
            
        Returns:
            Dict containing validation results and errors
        """
        errors = []
        warnings = []
        
        # Validate team name
        if not request.name or len(request.name) < 3:
            errors.append("Team name must be at least 3 characters long")
        elif len(request.name) > 100:
            errors.append("Team name must be at most 100 characters long")
        
        # Validate description
        if not request.description:
            warnings.append("Team description is recommended for clarity")
        elif len(request.description) > 1000:
            errors.append("Team description must be at most 1000 characters long")
        
        # Validate max_members
        if request.max_members < 1 or request.max_members > 50:
            errors.append("max_members must be between 1 and 50")
        elif request.max_members > 20:
            warnings.append("Large teams may have coordination challenges")
        
        # Validate initial_members
        if len(request.initial_members) > request.max_members:
            errors.append("Number of initial members cannot exceed max_members")
        
        # Validate communication channel
        if not request.communication_channel or len(request.communication_channel) < 1:
            errors.append("Communication channel is required")
        elif len(request.communication_channel) > 100:
            errors.append("Communication channel name must be at most 100 characters long")
        
        # Validate leader
        if request.leader_id and request.leader_id not in request.initial_members:
            warnings.append("Team leader should be included in initial members")
        
        # Validate objectives
        if len(request.objectives) > 10:
            warnings.append("Too many objectives may reduce team focus")
        
        for objective in request.objectives:
            if len(objective) > 500:
                errors.append("Each objective must be at most 500 characters long")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }
    
    def validate_team_member_assignment(self, team_id: str, agent_id: str, role: TeamRole) -> Dict[str, Any]:
        """
        Validate team member assignment
        
        Args:
            team_id: Team ID
            agent_id: Agent ID to assign
            role: Team role to assign
            
        Returns:
            Dict containing validation results
        """
        errors = []
        warnings = []
        
        # Check if agent exists
        if agent_id not in self.agent_profiles:
            errors.append(f"Agent {agent_id} not found")
        
        # Check if agent is already in another team
        agent_profile = self.agent_profiles.get(agent_id)
        if agent_profile and agent_profile.team_id and agent_profile.team_id != team_id:
            warnings.append(f"Agent {agent_id} is already assigned to team {agent_profile.team_id}")
        
        # Validate role
        if role not in [TeamRole.LEADER, TeamRole.MEMBER, TeamRole.OBSERVER]:
            errors.append(f"Invalid team role: {role}")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }
    
    def validate_team_configuration(self, team: Team) -> Dict[str, Any]:
        """
        Validate team configuration and composition
        
        Args:
            team: Team to validate
            
        Returns:
            Dict containing validation results
        """
        errors = []
        warnings = []
        
        # Check team size
        active_members = team.get_active_members()
        if len(active_members) == 0:
            warnings.append("Team has no active members")
        elif len(active_members) > team.max_members:
            errors.append(f"Team has {len(active_members)} active members, exceeding max of {team.max_members}")
        
        # Check for leader
        leader = team.get_leader()
        if not leader:
            warnings.append("Team has no designated leader")
        elif not leader.is_active:
            warnings.append("Team leader is not active")
        
        # Check for duplicate agents
        agent_ids = [member.agent_id for member in team.members]
        if len(agent_ids) != len(set(agent_ids)):
            errors.append("Team contains duplicate agent assignments")
        
        # Check role distribution
        role_counts = {}
        for member in active_members:
            role_counts[member.role] = role_counts.get(member.role, 0) + 1
        
        if role_counts.get(TeamRole.LEADER, 0) > 1:
            errors.append("Team cannot have multiple leaders")
        
        # Check team composition balance
        if len(active_members) >= 3:
            # Get agent roles from profiles
            agent_roles = []
            for member in active_members:
                if member.agent_id in self.agent_profiles:
                    agent_roles.append(self.agent_profiles[member.agent_id].role)
            
            # Check for role diversity
            unique_roles = set(agent_roles)
            if len(unique_roles) < 2 and len(active_members) >= 3:
                warnings.append("Team lacks role diversity - consider adding agents with different roles")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }
    
    def validate_model_role_compatibility(self, model: str, role: AgentRole) -> Dict[str, Any]:
        """
        Validate model-role compatibility for optimal performance
        
        Args:
            model: Model name to validate
            role: Agent role to validate against
            
        Returns:
            Dict containing validation results
        """
        errors = []
        warnings = []
        
        # Define optimal models for each role
        optimal_models = {
            AgentRole.CODER: ["gpt-4", "claude-3-opus", "claude-3-sonnet"],
            AgentRole.DEBUGGER: ["gpt-4", "claude-3-opus"],
            AgentRole.PLANNER: ["gpt-4", "claude-3-opus", "gemini-pro"],
            AgentRole.CRITIC: ["claude-3-opus", "gpt-4"],
            AgentRole.PM: ["gpt-4", "claude-3-sonnet", "gemini-pro"],
            AgentRole.UX: ["gpt-4", "claude-3-sonnet"],
            AgentRole.QA: ["gpt-4", "claude-3-opus"],
            AgentRole.ORCHESTRATOR: ["gpt-4", "claude-3-opus"]
        }
        
        # Check if model is optimal for role
        optimal_for_role = optimal_models.get(role, [])
        if model not in optimal_for_role:
            warnings.append(f"Model '{model}' may not be optimal for {role.value} role. Consider: {optimal_for_role}")
        
        # Check for model-specific limitations
        model_limitations = {
            "gpt-3.5-turbo": ["Complex reasoning tasks may be challenging"],
            "claude-3-haiku": ["May struggle with very complex code generation"],
            "gemini-pro": ["Code generation may be less reliable than GPT-4 or Claude"]
        }
        
        limitations = model_limitations.get(model, [])
        for limitation in limitations:
            warnings.append(f"Model limitation: {limitation}")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }
    
    def validate_team_composition(self, team_roles: List[AgentRole]) -> Dict[str, Any]:
        """
        Validate team composition for balanced collaboration
        
        Args:
            team_roles: List of agent roles in the team
            
        Returns:
            Dict containing validation results
        """
        errors = []
        warnings = []
        
        # Check for required core roles
        core_roles = {AgentRole.PLANNER, AgentRole.CRITIC}
        missing_core = core_roles - set(team_roles)
        if missing_core:
            errors.append(f"Missing required core roles: {[r.value for r in missing_core]}")
        
        # Check for role balance
        role_counts = {}
        for role in team_roles:
            role_counts[role] = role_counts.get(role, 0) + 1
        
        # Warn about duplicate roles
        for role, count in role_counts.items():
            if count > 1:
                warnings.append(f"Multiple {role.value} agents may cause conflicts")
        
        # Check team size limits
        team_size = len(team_roles)
        if team_size < 2:
            errors.append("Team must have at least 2 agents")
        elif team_size > 5:
            warnings.append("Large teams may have coordination challenges")
        
        # Check for complementary roles
        complementary_pairs = [
            (AgentRole.CODER, AgentRole.DEBUGGER),
            (AgentRole.PLANNER, AgentRole.PM),
            (AgentRole.UX, AgentRole.QA)
        ]
        
        for role1, role2 in complementary_pairs:
            if role1 in team_roles and role2 not in team_roles:
                warnings.append(f"Consider adding {role2.value} to complement {role1.value}")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }
    
    def create_agent_profile(self, request: AgentCreationRequest) -> AgentProfile:
        """
        Create a new agent profile from validated request with comprehensive validation
        
        Args:
            request: Validated agent creation request
            
        Returns:
            AgentProfile: Created agent profile
            
        Raises:
            ValueError: If validation fails
        """
        # Validate request
        validation = self.validate_agent_creation_request(request)
        if not validation["valid"]:
            raise ValueError(f"Agent creation validation failed: {validation['errors']}")
        
        # Additional validation checks
        capability_validation = self.validate_capability_combinations(request.capabilities, request.role)
        if not capability_validation["valid"]:
            raise ValueError(f"Capability validation failed: {capability_validation['errors']}")
        
        model_validation = self.validate_model_role_compatibility(request.model, request.role)
        # Model validation only produces warnings, not errors
        
        # Generate unique agent ID
        agent_id = str(uuid.uuid4())
        
        # Create profile
        profile = AgentProfile(
            id=agent_id,
            name=request.name,
            role=request.role,
            model=request.model,
            prompt=request.prompt,
            capabilities=request.capabilities,
            team_id=request.team_id,
            status=AgentStatus.IDLE,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
            max_retries=request.max_retries,
            timeout_seconds=request.timeout_seconds,
            consensus_threshold=request.consensus_threshold
        )
        
        # Store profile
        self.agent_profiles[agent_id] = profile
        
        # Log validation warnings
        all_warnings = validation.get("warnings", []) + capability_validation.get("warnings", []) + model_validation.get("warnings", [])
        if all_warnings:
            logger.warning(f"Agent profile '{request.name}' created with warnings: {all_warnings}")
        
        logger.info(f"Created agent profile '{request.name}' with ID {agent_id}")
        
        return profile
    
    def get_agent_profile(self, agent_id: str) -> Optional[AgentProfile]:
        """Get agent profile by ID"""
        return self.agent_profiles.get(agent_id)
    
    def update_agent_profile(self, agent_id: str, updates: Dict[str, Any]) -> Optional[AgentProfile]:
        """Update agent profile with new values"""
        profile = self.agent_profiles.get(agent_id)
        if not profile:
            return None
        
        # Update allowed fields
        for key, value in updates.items():
            if hasattr(profile, key):
                setattr(profile, key, value)
        
        # Update timestamp
        from datetime import datetime
        profile.updated_at = datetime.utcnow()
        
        return profile
    
    def delete_agent_profile(self, agent_id: str) -> bool:
        """Delete agent profile"""
        if agent_id in self.agent_profiles:
            del self.agent_profiles[agent_id]
            logger.info(f"Deleted agent profile {agent_id}")
            return True
        return False
    
    def list_agent_profiles(self) -> Dict[str, AgentProfile]:
        """List all agent profiles"""
        return self.agent_profiles.copy()
    
    def get_profiles_by_role(self, role: AgentRole) -> Dict[str, AgentProfile]:
        """Get all profiles with specified role"""
        return {
            agent_id: profile
            for agent_id, profile in self.agent_profiles.items()
            if profile.role == role
        }
    
    def get_profiles_by_team(self, team_id: str) -> Dict[str, AgentProfile]:
        """Get all profiles assigned to a team"""
        return {
            agent_id: profile
            for agent_id, profile in self.agent_profiles.items()
            if profile.team_id == team_id
        }
    
    def clone_agent_profile(self, source_agent_id: str, new_name: str = None) -> Optional[AgentProfile]:
        """
        Clone an existing agent profile
        
        Args:
            source_agent_id: ID of the agent to clone
            new_name: Name for the cloned agent (optional, will append " (Clone)" if not provided)
            
        Returns:
            AgentProfile: Cloned agent profile or None if source not found
        """
        source_profile = self.agent_profiles.get(source_agent_id)
        if not source_profile:
            return None
        
        # Generate new ID and name
        new_id = str(uuid.uuid4())
        if not new_name:
            new_name = f"{source_profile.name} (Clone)"
        
        # Create cloned profile
        cloned_profile = AgentProfile(
            id=new_id,
            name=new_name,
            role=source_profile.role,
            model=source_profile.model,
            prompt=source_profile.prompt,
            capabilities=source_profile.capabilities.copy(),
            team_id=source_profile.team_id,
            status=AgentStatus.IDLE,
            temperature=source_profile.temperature,
            max_tokens=source_profile.max_tokens,
            max_retries=source_profile.max_retries,
            timeout_seconds=source_profile.timeout_seconds,
            consensus_threshold=source_profile.consensus_threshold,
            # Reset performance metrics for new agent
            success_rate=0.0,
            avg_response_time=0.0,
            tasks_completed=0,
            # Copy memory if desired (or start fresh)
            memory=[]
        )
        
        # Store cloned profile
        self.agent_profiles[new_id] = cloned_profile
        
        logger.info(f"Cloned agent profile '{source_profile.name}' to '{new_name}' with ID {new_id}")
        
        return cloned_profile
    
    def batch_clone_agents(self, agent_ids: List[str], name_prefix: str = None) -> List[AgentProfile]:
        """
        Clone multiple agent profiles
        
        Args:
            agent_ids: List of agent IDs to clone
            name_prefix: Prefix for cloned agent names (optional)
            
        Returns:
            List of cloned agent profiles
        """
        cloned_profiles = []
        
        for agent_id in agent_ids:
            source_profile = self.agent_profiles.get(agent_id)
            if not source_profile:
                logger.warning(f"Agent {agent_id} not found for cloning")
                continue
            
            # Generate name for clone
            if name_prefix:
                clone_name = f"{name_prefix} {source_profile.name}"
            else:
                clone_name = None
            
            cloned_profile = self.clone_agent_profile(agent_id, clone_name)
            if cloned_profile:
                cloned_profiles.append(cloned_profile)
        
        logger.info(f"Batch cloned {len(cloned_profiles)} agents")
        
        return cloned_profiles
    
    def create_agent_template(
        self, 
        agent_id: str, 
        template_name: str, 
        author: str = "user",
        description: str = "",
        category: TemplateCategory = TemplateCategory.CUSTOM,
        tags: Set[str] = None,
        is_public: bool = False
    ) -> Optional[AgentTemplate]:
        """
        Create a comprehensive template from an existing agent profile
        
        Args:
            agent_id: ID of the agent to create template from
            template_name: Name for the template
            author: Template author
            description: Template description
            category: Template category
            tags: Template tags
            is_public: Whether template is public
            
        Returns:
            AgentTemplate or None if agent not found
        """
        profile = self.agent_profiles.get(agent_id)
        if not profile:
            return None
        
        # Use the comprehensive template manager
        template = self.template_manager.create_template(
            name=template_name,
            role=profile.role,
            base_prompt=profile.prompt,
            author=author,
            description=description,
            category=category,
            tags=tags or set(),
            capabilities=profile.capabilities,
            preferred_models=[profile.model],
            model_config={
                "temperature": profile.temperature,
                "max_tokens": profile.max_tokens,
                "max_retries": profile.max_retries,
                "timeout_seconds": profile.timeout_seconds,
                "consensus_threshold": profile.consensus_threshold
            },
            is_public=is_public
        )
        
        logger.info(f"Created comprehensive template '{template_name}' from agent {agent_id}")
        return template
    
    def create_agent_from_template(
        self, 
        template: Union[AgentTemplate, str], 
        name: str,
        variables: Dict[str, Any] = None
    ) -> AgentProfile:
        """
        Create a new agent profile from a comprehensive template
        
        Args:
            template: AgentTemplate instance or template ID
            name: Name for the new agent
            variables: Variables to substitute in template
            
        Returns:
            AgentProfile: Created agent profile
        """
        # Handle both template object and template ID
        if isinstance(template, str):
            template_obj = self.template_manager.get_template(template)
            if not template_obj:
                raise ValueError(f"Template not found: {template}")
        else:
            template_obj = template
        
        # Validate template variables if provided
        if variables:
            var_validation = self.template_manager.validate_template_variables(template_obj, variables)
            if not var_validation["valid"]:
                raise ValueError(f"Template variable validation failed: {var_validation['errors']}")
        
        # Process template with variables
        processed_template = self.template_manager._process_template(template_obj)
        
        # Apply variables to processed template
        if variables:
            for var_name, var_value in variables.items():
                placeholder = f"{{{var_name}}}"
                processed_template.base_prompt = processed_template.base_prompt.replace(placeholder, str(var_value))
        
        # Get preferred model or use default
        preferred_model = processed_template.preferred_models[0] if processed_template.preferred_models else "gpt-3.5-turbo"
        
        # Create request from processed template
        request = AgentCreationRequest(
            name=name,
            role=processed_template.role,
            model=preferred_model,
            prompt=processed_template.base_prompt,
            capabilities=processed_template.capabilities,
            temperature=processed_template.model_config.get("temperature", 0.7),
            max_tokens=processed_template.model_config.get("max_tokens", 2000),
            max_retries=processed_template.model_config.get("max_retries", 3),
            timeout_seconds=processed_template.model_config.get("timeout_seconds", 60),
            consensus_threshold=processed_template.model_config.get("consensus_threshold", 0.8)
        )
        
        # Create agent profile
        profile = self.create_agent_profile(request)
        
        # Record template usage
        self.template_manager.record_template_usage(template_obj.id, profile.id)
        
        logger.info(f"Created agent '{name}' from template '{template_obj.name}'")
        
        return profile
    
    # Template Management Convenience Methods
    
    def get_template(self, template_id: str) -> Optional[AgentTemplate]:
        """Get template by ID"""
        return self.template_manager.get_template(template_id)
    
    def search_templates(self, **kwargs) -> List[AgentTemplate]:
        """Search templates with filters"""
        return self.template_manager.search_templates(**kwargs)
    
    def get_popular_templates(self, limit: int = 10) -> List[AgentTemplate]:
        """Get most popular templates"""
        return self.template_manager.get_popular_templates(limit)
    
    def get_templates_by_role(self, role: AgentRole) -> List[AgentTemplate]:
        """Get templates for a specific role"""
        return self.template_manager.get_templates_by_role(role)
    
    def get_templates_by_category(self, category: TemplateCategory) -> List[AgentTemplate]:
        """Get templates in a category"""
        return self.template_manager.get_templates_by_category(category)
    
    def rate_template(self, template_id: str, user_id: str, rating: int, comment: str = "") -> bool:
        """Rate a template"""
        return self.template_manager.rate_template(template_id, user_id, rating, comment)
    
    def get_template_statistics(self) -> Dict[str, Any]:
        """Get template system statistics"""
        return self.template_manager.get_template_statistics()
    
    def validate_template(self, template: AgentTemplate) -> Dict[str, Any]:
        """Validate a template"""
        return self.template_manager.validate_template(template)
    
    def export_template(self, template_id: str) -> Dict[str, Any]:
        """Export template to JSON"""
        return self.template_manager.export_template(template_id)
    
    def import_template(self, template_data: Dict[str, Any], author: str = None) -> AgentTemplate:
        """Import template from JSON"""
        return self.template_manager.import_template(template_data, author)
    
    def create_template_from_agent(
        self, 
        agent_id: str, 
        template_name: str, 
        author: str = "user",
        **kwargs
    ) -> Optional[AgentTemplate]:
        """Create template from agent - alias for create_agent_template"""
        return self.create_agent_template(agent_id, template_name, author, **kwargs)
    
    def create_agent_team_from_template(
        self,
        template_id: str,
        team_name: str,
        team_size: int = 3,
        variables: Dict[str, Any] = None,
        **kwargs
    ) -> Dict[str, AgentProfile]:
        """
        Create a team of agents from a template
        
        Args:
            template_id: Template ID to use
            team_name: Base name for team members
            team_size: Number of agents to create
            variables: Variables to substitute in template
            **kwargs: Additional arguments for agent creation
            
        Returns:
            Dict of agent profiles created
        """
        template = self.template_manager.get_template(template_id)
        if not template:
            raise ValueError(f"Template not found: {template_id}")
        
        team_agents = {}
        
        for i in range(team_size):
            agent_name = f"{team_name}_{i+1}"
            agent_profile = self.create_agent_from_template(template, agent_name, variables)
            team_agents[agent_name] = agent_profile
        
        logger.info(f"Created team of {team_size} agents from template '{template.name}'")
        return team_agents
    
    def get_recommended_templates_for_task(self, task_description: str, limit: int = 5) -> List[AgentTemplate]:
        """
        Get recommended templates for a task based on description
        
        Args:
            task_description: Description of the task
            limit: Maximum number of recommendations
            
        Returns:
            List of recommended templates
        """
        # Simple keyword-based recommendation
        # In a real implementation, this could use more sophisticated NLP
        
        task_lower = task_description.lower()
        scored_templates = []
        
        for template in self.templates.values():
            if template.metadata.status != TemplateStatus.ACTIVE:
                continue
                
            score = 0
            
            # Check template name and description
            if any(word in template.name.lower() for word in task_lower.split()):
                score += 3
            
            if any(word in template.metadata.description.lower() for word in task_lower.split()):
                score += 2
            
            # Check capabilities
            task_keywords = {
                "code": ["code_generation", "refactoring", "debugging"],
                "review": ["code_review", "quality_assurance"],
                "plan": ["task_decomposition", "planning"],
                "test": ["test_execution", "testing"],
                "debug": ["error_analysis", "debugging"],
                "design": ["design_systems", "wireframing"],
                "manage": ["project_management", "coordination"]
            }
            
            for keyword, capabilities in task_keywords.items():
                if keyword in task_lower:
                    for capability in capabilities:
                        if capability in template.capabilities:
                            score += 1
            
            # Bonus for high ratings and usage
            score += template.average_rating * 0.5
            score += min(template.usage_stats.usage_count * 0.1, 2)
            
            if score > 0:
                scored_templates.append((template, score))
        
        # Sort by score and return top templates
        scored_templates.sort(key=lambda x: x[1], reverse=True)
        return [template for template, score in scored_templates[:limit]]
    
    # JSON Export/Import Methods
    
    def _serialize_datetime(self, dt: datetime) -> str:
        """Convert datetime to ISO format string for JSON serialization"""
        if dt is None:
            return None
        return dt.isoformat()
    
    def _deserialize_datetime(self, dt_str: str) -> datetime:
        """Convert ISO format string back to datetime"""
        if dt_str is None:
            return None
        return datetime.fromisoformat(dt_str)
    
    def _get_export_schema_version(self) -> str:
        """Get current export schema version for compatibility"""
        return "1.0.0"
    
    def _validate_import_schema(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate import data schema and version compatibility
        
        Args:
            data: Import data to validate
            
        Returns:
            Dict containing validation results
        """
        errors = []
        warnings = []
        
        # Check for required metadata
        if "metadata" not in data:
            errors.append("Missing required 'metadata' field")
        else:
            metadata = data["metadata"]
            
            # Check schema version
            if "schema_version" not in metadata:
                errors.append("Missing schema_version in metadata")
            else:
                schema_version = metadata["schema_version"]
                current_version = self._get_export_schema_version()
                
                if schema_version != current_version:
                    warnings.append(f"Schema version mismatch: {schema_version} vs {current_version}")
            
            # Check export timestamp
            if "export_timestamp" not in metadata:
                warnings.append("Missing export_timestamp in metadata")
        
        # Check for required profile data
        if "profile" not in data and "profiles" not in data:
            errors.append("Missing required 'profile' or 'profiles' field")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }
    
    def _agent_profile_to_dict(self, profile: AgentProfile) -> Dict[str, Any]:
        """
        Convert AgentProfile to dictionary for JSON serialization
        
        Args:
            profile: AgentProfile to convert
            
        Returns:
            Dict representation of the profile
        """
        return {
            "id": profile.id,
            "name": profile.name,
            "role": profile.role.value,
            "model": profile.model,
            "prompt": profile.prompt,
            "capabilities": profile.capabilities,
            "team_id": profile.team_id,
            "status": profile.status.value,
            "temperature": profile.temperature,
            "max_tokens": profile.max_tokens,
            "max_retries": profile.max_retries,
            "timeout_seconds": profile.timeout_seconds,
            "consensus_threshold": profile.consensus_threshold,
            "success_rate": profile.success_rate,
            "avg_response_time": profile.avg_response_time,
            "tasks_completed": profile.tasks_completed,
            "memory": profile.memory,
            "created_at": self._serialize_datetime(profile.created_at),
            "updated_at": self._serialize_datetime(profile.updated_at)
        }
    
    def _dict_to_agent_profile(self, data: Dict[str, Any]) -> AgentProfile:
        """
        Convert dictionary to AgentProfile
        
        Args:
            data: Dictionary representation of profile
            
        Returns:
            AgentProfile instance
            
        Raises:
            ValueError: If data is invalid
        """
        try:
            return AgentProfile(
                id=data["id"],
                name=data["name"],
                role=AgentRole(data["role"]),
                model=data["model"],
                prompt=data["prompt"],
                capabilities=data.get("capabilities", []),
                team_id=data.get("team_id"),
                status=AgentStatus(data.get("status", "idle")),
                temperature=data.get("temperature", 0.7),
                max_tokens=data.get("max_tokens", 2000),
                max_retries=data.get("max_retries", 3),
                timeout_seconds=data.get("timeout_seconds", 60),
                consensus_threshold=data.get("consensus_threshold", 0.8),
                success_rate=data.get("success_rate", 0.0),
                avg_response_time=data.get("avg_response_time", 0.0),
                tasks_completed=data.get("tasks_completed", 0),
                memory=data.get("memory", []),
                created_at=self._deserialize_datetime(data.get("created_at")),
                updated_at=self._deserialize_datetime(data.get("updated_at"))
            )
        except (KeyError, ValueError, TypeError) as e:
            raise ValueError(f"Invalid profile data: {str(e)}")
    
    def export_agent_profile(self, agent_id: str) -> Dict[str, Any]:
        """
        Export agent profile to JSON-compatible dictionary
        
        Args:
            agent_id: ID of the agent to export
            
        Returns:
            Dict containing the exported profile with metadata
            
        Raises:
            ValueError: If agent not found
        """
        profile = self.agent_profiles.get(agent_id)
        if not profile:
            raise ValueError(f"Agent profile not found: {agent_id}")
        
        export_data = {
            "metadata": {
                "schema_version": self._get_export_schema_version(),
                "export_timestamp": self._serialize_datetime(datetime.utcnow()),
                "export_type": "single_profile",
                "agent_id": agent_id,
                "agent_name": profile.name,
                "agent_role": profile.role.value
            },
            "profile": self._agent_profile_to_dict(profile)
        }
        
        logger.info(f"Exported agent profile: {agent_id} ({profile.name})")
        return export_data
    
    def import_agent_profile(self, json_data: Dict[str, Any]) -> AgentProfile:
        """
        Import agent profile from JSON data
        
        Args:
            json_data: JSON data containing profile information
            
        Returns:
            AgentProfile: Imported agent profile
            
        Raises:
            ValueError: If data is invalid or validation fails
        """
        # Validate schema
        validation = self._validate_import_schema(json_data)
        if not validation["valid"]:
            raise ValueError(f"Import validation failed: {validation['errors']}")
        
        # Log warnings
        if validation["warnings"]:
            logger.warning(f"Import warnings: {validation['warnings']}")
        
        # Extract profile data
        profile_data = json_data.get("profile")
        if not profile_data:
            raise ValueError("No profile data found in import")
        
        # Create profile from data
        profile = self._dict_to_agent_profile(profile_data)
        
        # Generate new ID if one already exists
        original_id = profile.id
        if profile.id in self.agent_profiles:
            profile.id = str(uuid.uuid4())
            logger.info(f"Agent ID collision detected, generated new ID: {profile.id}")
        
        # Store profile
        self.agent_profiles[profile.id] = profile
        
        logger.info(f"Imported agent profile: {profile.id} ({profile.name}) from {original_id}")
        return profile
    
    def export_agent_profiles_batch(self, agent_ids: List[str]) -> Dict[str, Any]:
        """
        Export multiple agent profiles in batch
        
        Args:
            agent_ids: List of agent IDs to export
            
        Returns:
            Dict containing exported profiles with metadata
            
        Raises:
            ValueError: If any agent not found
        """
        # Validate all agents exist
        missing_agents = [agent_id for agent_id in agent_ids if agent_id not in self.agent_profiles]
        if missing_agents:
            raise ValueError(f"Agent profiles not found: {missing_agents}")
        
        # Export profiles
        exported_profiles = {}
        agent_names = []
        agent_roles = []
        
        for agent_id in agent_ids:
            profile = self.agent_profiles[agent_id]
            exported_profiles[agent_id] = self._agent_profile_to_dict(profile)
            agent_names.append(profile.name)
            agent_roles.append(profile.role.value)
        
        export_data = {
            "metadata": {
                "schema_version": self._get_export_schema_version(),
                "export_timestamp": self._serialize_datetime(datetime.utcnow()),
                "export_type": "batch_profiles",
                "agent_count": len(agent_ids),
                "agent_ids": agent_ids,
                "agent_names": agent_names,
                "agent_roles": list(set(agent_roles))  # Unique roles
            },
            "profiles": exported_profiles
        }
        
        logger.info(f"Exported {len(agent_ids)} agent profiles in batch")
        return export_data
    
    def import_agent_profiles_batch(self, json_data: Dict[str, Any]) -> List[AgentProfile]:
        """
        Import multiple agent profiles from JSON data
        
        Args:
            json_data: JSON data containing multiple profiles
            
        Returns:
            List[AgentProfile]: List of imported agent profiles
            
        Raises:
            ValueError: If data is invalid or validation fails
        """
        # Validate schema
        validation = self._validate_import_schema(json_data)
        if not validation["valid"]:
            raise ValueError(f"Import validation failed: {validation['errors']}")
        
        # Log warnings
        if validation["warnings"]:
            logger.warning(f"Import warnings: {validation['warnings']}")
        
        # Extract profiles data
        profiles_data = json_data.get("profiles")
        if not profiles_data:
            raise ValueError("No profiles data found in import")
        
        imported_profiles = []
        id_collisions = []
        
        # Import each profile
        for agent_id, profile_data in profiles_data.items():
            try:
                profile = self._dict_to_agent_profile(profile_data)
                
                # Generate new ID if collision
                original_id = profile.id
                if profile.id in self.agent_profiles:
                    profile.id = str(uuid.uuid4())
                    id_collisions.append(f"{original_id} -> {profile.id}")
                
                # Store profile
                self.agent_profiles[profile.id] = profile
                imported_profiles.append(profile)
                
            except ValueError as e:
                logger.error(f"Failed to import profile {agent_id}: {str(e)}")
                # Continue with other profiles instead of failing completely
                continue
        
        # Log results
        if id_collisions:
            logger.info(f"ID collisions resolved: {id_collisions}")
        
        logger.info(f"Imported {len(imported_profiles)} agent profiles in batch")
        return imported_profiles
    
    def export_agent_profiles_to_json_file(self, agent_ids: List[str], file_path: str) -> bool:
        """
        Export agent profiles to JSON file
        
        Args:
            agent_ids: List of agent IDs to export
            file_path: Path to save the JSON file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if len(agent_ids) == 1:
                export_data = self.export_agent_profile(agent_ids[0])
            else:
                export_data = self.export_agent_profiles_batch(agent_ids)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Exported {len(agent_ids)} agent profiles to {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to export profiles to file {file_path}: {str(e)}")
            return False
    
    def import_agent_profiles_from_json_file(self, file_path: str) -> List[AgentProfile]:
        """
        Import agent profiles from JSON file
        
        Args:
            file_path: Path to the JSON file
            
        Returns:
            List[AgentProfile]: List of imported agent profiles
            
        Raises:
            ValueError: If file cannot be read or data is invalid
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            # Determine import type based on metadata
            metadata = json_data.get("metadata", {})
            export_type = metadata.get("export_type", "single_profile")
            
            if export_type == "single_profile":
                profile = self.import_agent_profile(json_data)
                return [profile]
            else:
                return self.import_agent_profiles_batch(json_data)
                
        except FileNotFoundError:
            raise ValueError(f"File not found: {file_path}")
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON file: {str(e)}")
        except Exception as e:
            raise ValueError(f"Failed to import from file {file_path}: {str(e)}")
    
    def validate_agent_profile_json(self, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate agent profile JSON data without importing
        
        Args:
            json_data: JSON data to validate
            
        Returns:
            Dict containing validation results
        """
        errors = []
        warnings = []
        
        # Validate schema
        schema_validation = self._validate_import_schema(json_data)
        errors.extend(schema_validation["errors"])
        warnings.extend(schema_validation["warnings"])
        
        if schema_validation["valid"]:
            # Validate profile data
            metadata = json_data.get("metadata", {})
            export_type = metadata.get("export_type", "single_profile")
            
            if export_type == "single_profile":
                profile_data = json_data.get("profile", {})
                try:
                    # Test profile creation without storing
                    self._dict_to_agent_profile(profile_data)
                except ValueError as e:
                    errors.append(f"Profile validation failed: {str(e)}")
            else:
                profiles_data = json_data.get("profiles", {})
                invalid_profiles = []
                
                for agent_id, profile_data in profiles_data.items():
                    try:
                        self._dict_to_agent_profile(profile_data)
                    except ValueError as e:
                        invalid_profiles.append(f"{agent_id}: {str(e)}")
                
                if invalid_profiles:
                    errors.extend(invalid_profiles)
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings,
            "schema_version": json_data.get("metadata", {}).get("schema_version"),
            "export_type": json_data.get("metadata", {}).get("export_type"),
            "agent_count": json_data.get("metadata", {}).get("agent_count", 1)
        }
    
    # ==================== CAPABILITY SCORING SYSTEM ====================
    
    def _initialize_capability_system(self):
        """Initialize the capability scoring system with default templates and benchmarks"""
        # Define default capability templates
        default_capabilities = {
            "code_generation": CapabilityTemplate(
                name="code_generation",
                category=CapabilityCategory.TECHNICAL,
                description="Ability to generate clean, functional code",
                required_skills=["programming", "problem_solving", "syntax_knowledge"],
                related_capabilities=["refactoring", "debugging", "testing"],
                benchmarks=["code_gen_basic", "code_gen_advanced"],
                weight=1.0,
                is_core=True
            ),
            "code_review": CapabilityTemplate(
                name="code_review",
                category=CapabilityCategory.TECHNICAL,
                description="Ability to review code for quality, bugs, and improvements",
                required_skills=["code_analysis", "best_practices", "security_awareness"],
                related_capabilities=["bug_detection", "security_analysis"],
                benchmarks=["code_review_basic", "code_review_advanced"],
                weight=1.0,
                is_core=True
            ),
            "task_decomposition": CapabilityTemplate(
                name="task_decomposition",
                category=CapabilityCategory.COGNITIVE,
                description="Ability to break down complex tasks into manageable components",
                required_skills=["analysis", "planning", "prioritization"],
                related_capabilities=["resource_allocation", "timeline_management"],
                benchmarks=["task_decomp_basic", "task_decomp_complex"],
                weight=1.0,
                is_core=True
            ),
            "debugging": CapabilityTemplate(
                name="debugging",
                category=CapabilityCategory.TECHNICAL,
                description="Ability to identify and fix code errors",
                required_skills=["error_analysis", "problem_solving", "testing"],
                related_capabilities=["code_generation", "testing"],
                benchmarks=["debug_basic", "debug_advanced"],
                weight=1.0,
                is_core=True
            ),
            "communication": CapabilityTemplate(
                name="communication",
                category=CapabilityCategory.COMMUNICATION,
                description="Ability to communicate effectively with team members",
                required_skills=["clarity", "empathy", "collaboration"],
                related_capabilities=["stakeholder_management", "team_coordination"],
                benchmarks=["comm_basic", "comm_advanced"],
                weight=0.8,
                is_core=False
            )
        }
        
        # Store capability templates
        self.capability_templates.update(default_capabilities)
        
        # Initialize basic benchmarks
        self._initialize_capability_benchmarks()
        
        logger.info(f"Initialized capability system with {len(self.capability_templates)} templates")
    
    def _initialize_capability_benchmarks(self):
        """Initialize default capability benchmarks"""
        # Code generation benchmarks
        self.capability_benchmarks["code_gen_basic"] = CapabilityBenchmark(
            capability="code_generation",
            category=CapabilityCategory.TECHNICAL,
            test_cases=[
                {"task": "Write a function to calculate factorial", "language": "python"},
                {"task": "Create a simple class with constructor", "language": "python"},
                {"task": "Implement basic sorting algorithm", "language": "python"}
            ],
            expected_outcomes=[
                {"correctness": 1.0, "efficiency": 0.8, "readability": 0.9},
                {"correctness": 1.0, "efficiency": 0.7, "readability": 0.8},
                {"correctness": 1.0, "efficiency": 0.6, "readability": 0.7}
            ],
            scoring_criteria={
                ScoringMetric.ACCURACY: 0.4,
                ScoringMetric.SPEED: 0.2,
                ScoringMetric.CONSISTENCY: 0.2,
                ScoringMetric.COMPLEXITY_HANDLING: 0.2
            },
            difficulty_level=0.3,
            timeout_seconds=300
        )
        
        # Code review benchmarks
        self.capability_benchmarks["code_review_basic"] = CapabilityBenchmark(
            capability="code_review",
            category=CapabilityCategory.TECHNICAL,
            test_cases=[
                {"code": "def func(): return x+1", "issues": ["undefined_variable", "poor_naming"]},
                {"code": "for i in range(1000000): print(i)", "issues": ["performance", "resource_usage"]},
                {"code": "password = input('Enter password: ')", "issues": ["security", "input_validation"]}
            ],
            expected_outcomes=[
                {"issues_found": 2, "accuracy": 0.9, "severity_assessment": 0.8},
                {"issues_found": 2, "accuracy": 0.8, "severity_assessment": 0.9},
                {"issues_found": 2, "accuracy": 0.9, "severity_assessment": 1.0}
            ],
            scoring_criteria={
                ScoringMetric.ACCURACY: 0.5,
                ScoringMetric.CONSISTENCY: 0.3,
                ScoringMetric.COMPLEXITY_HANDLING: 0.2
            },
            difficulty_level=0.4,
            timeout_seconds=180
        )
        
        # Task decomposition benchmarks
        self.capability_benchmarks["task_decomp_basic"] = CapabilityBenchmark(
            capability="task_decomposition",
            category=CapabilityCategory.COGNITIVE,
            test_cases=[
                {"task": "Build a web application", "complexity": "medium"},
                {"task": "Implement user authentication system", "complexity": "high"},
                {"task": "Create API documentation", "complexity": "low"}
            ],
            expected_outcomes=[
                {"subtasks": 8, "logical_flow": 0.9, "completeness": 0.8},
                {"subtasks": 12, "logical_flow": 0.8, "completeness": 0.9},
                {"subtasks": 4, "logical_flow": 0.9, "completeness": 0.9}
            ],
            scoring_criteria={
                ScoringMetric.ACCURACY: 0.3,
                ScoringMetric.COMPLEXITY_HANDLING: 0.4,
                ScoringMetric.CONSISTENCY: 0.3
            },
            difficulty_level=0.5,
            timeout_seconds=240
        )
        
        logger.info(f"Initialized {len(self.capability_benchmarks)} capability benchmarks")
    
    def create_capability_profile(self, agent_id: str, initial_capabilities: List[str] = None) -> AgentCapabilityProfile:
        """
        Create a new capability profile for an agent
        
        Args:
            agent_id: Agent identifier
            initial_capabilities: Initial capabilities to evaluate
            
        Returns:
            AgentCapabilityProfile: Created capability profile
        """
        if agent_id in self.capability_profiles:
            return self.capability_profiles[agent_id]
        
        profile = AgentCapabilityProfile(agent_id=agent_id)
        
        # Initialize scores for provided capabilities
        if initial_capabilities:
            for capability in initial_capabilities:
                if capability in self.capability_templates:
                    template = self.capability_templates[capability]
                    profile.capability_scores[capability] = CapabilityScore(
                        capability=capability,
                        category=template.category,
                        score=0.5,  # Default neutral score
                        level=CapabilityLevel.INTERMEDIATE,
                        confidence=0.1  # Low confidence until evaluated
                    )
        
        self.capability_profiles[agent_id] = profile
        logger.info(f"Created capability profile for agent {agent_id}")
        return profile
    
    def evaluate_agent_capability(self, agent_id: str, capability: str, 
                                 task_id: str, score: float, 
                                 metrics: Dict[ScoringMetric, float],
                                 evaluator: str = "automated",
                                 context: Dict[str, Any] = None) -> bool:
        """
        Evaluate and record an agent's capability performance
        
        Args:
            agent_id: Agent identifier
            capability: Capability being evaluated
            task_id: Task identifier
            score: Performance score (0.0 to 1.0)
            metrics: Detailed metrics for the evaluation
            evaluator: Who performed the evaluation
            context: Additional context about the evaluation
            
        Returns:
            bool: True if evaluation was recorded successfully
        """
        if agent_id not in self.capability_profiles:
            self.create_capability_profile(agent_id, [capability])
        
        profile = self.capability_profiles[agent_id]
        
        # Create evaluation record
        evaluation = CapabilityEvaluation(
            agent_id=agent_id,
            capability=capability,
            task_id=task_id,
            score=score,
            metrics=metrics,
            evaluator=evaluator,
            context=context or {}
        )
        
        # Add to evaluations
        profile.evaluations.append(evaluation)
        self.capability_evaluations.append(evaluation)
        
        # Update capability score
        self._update_capability_score(agent_id, capability, score, metrics)
        
        # Update overall score
        self._update_overall_score(agent_id)
        
        # Update recommendations
        self._update_capability_recommendations(agent_id)
        
        logger.info(f"Recorded capability evaluation for {agent_id}.{capability}: {score}")
        return True
    
    def _update_capability_score(self, agent_id: str, capability: str, 
                                new_score: float, metrics: Dict[ScoringMetric, float]):
        """Update capability score using weighted average"""
        profile = self.capability_profiles[agent_id]
        
        if capability not in profile.capability_scores:
            # Create new capability score
            template = self.capability_templates.get(capability)
            category = template.category if template else CapabilityCategory.TECHNICAL
            
            profile.capability_scores[capability] = CapabilityScore(
                capability=capability,
                category=category,
                score=new_score,
                level=CapabilityLevel.INTERMEDIATE,
                confidence=0.3,
                evidence_count=1,
                metrics=metrics
            )
        else:
            # Update existing score using weighted average
            existing_score = profile.capability_scores[capability]
            evidence_count = existing_score.evidence_count + 1
            
            # Weight recent evaluations more heavily
            weight = min(0.3, 1.0 / evidence_count)
            updated_score = existing_score.score * (1 - weight) + new_score * weight
            
            # Update confidence based on evidence count
            confidence = min(0.95, 0.3 + (evidence_count - 1) * 0.1)
            
            # Update metrics with weighted average
            updated_metrics = {}
            for metric, value in metrics.items():
                if metric in existing_score.metrics:
                    updated_metrics[metric] = existing_score.metrics[metric] * (1 - weight) + value * weight
                else:
                    updated_metrics[metric] = value
            
            # Update score object
            existing_score.score = updated_score
            existing_score.confidence = confidence
            existing_score.evidence_count = evidence_count
            existing_score.metrics = updated_metrics
            existing_score.last_updated = datetime.utcnow()
    
    def _update_overall_score(self, agent_id: str):
        """Update agent's overall capability score"""
        profile = self.capability_profiles[agent_id]
        
        if not profile.capability_scores:
            profile.overall_score = 0.0
            return
        
        # Calculate weighted average based on capability importance
        total_weight = 0.0
        weighted_sum = 0.0
        
        for capability, score in profile.capability_scores.items():
            template = self.capability_templates.get(capability)
            weight = template.weight if template else 1.0
            
            # Core capabilities have higher weight
            if template and template.is_core:
                weight *= 1.5
            
            weighted_sum += score.score * weight * score.confidence
            total_weight += weight * score.confidence
        
        profile.overall_score = weighted_sum / total_weight if total_weight > 0 else 0.0
        profile.last_updated = datetime.utcnow()
    
    def _update_capability_recommendations(self, agent_id: str):
        """Update improvement recommendations for an agent"""
        profile = self.capability_profiles[agent_id]
        
        recommendations = []
        
        # Identify weak areas
        weak_capabilities = [
            cap for cap, score in profile.capability_scores.items()
            if score.score < 0.6 and score.confidence > 0.5
        ]
        
        for capability in weak_capabilities:
            template = self.capability_templates.get(capability)
            if template:
                recommendations.append(f"Focus on improving {capability}: {template.description}")
        
        # Identify inconsistent performance
        inconsistent_caps = [
            cap for cap, score in profile.capability_scores.items()
            if score.confidence < 0.4 and score.evidence_count > 2
        ]
        
        for capability in inconsistent_caps:
            recommendations.append(f"Work on consistency in {capability} - performance varies significantly")
        
        # Suggest complementary capabilities
        strong_caps = [
            cap for cap, score in profile.capability_scores.items()
            if score.score > 0.7 and score.confidence > 0.6
        ]
        
        for capability in strong_caps:
            template = self.capability_templates.get(capability)
            if template:
                for related_cap in template.related_capabilities:
                    if related_cap not in profile.capability_scores:
                        recommendations.append(f"Consider developing {related_cap} to complement your strong {capability} skills")
        
        profile.recommendations = recommendations[:5]  # Limit to top 5
        profile.strengths = profile.get_top_capabilities(5)
        profile.weaknesses = profile.get_improvement_areas(3)
    
    def get_capability_profile(self, agent_id: str) -> Optional[AgentCapabilityProfile]:
        """Get capability profile for an agent"""
        return self.capability_profiles.get(agent_id)
    
    def get_agents_by_capability(self, capability: str, min_score: float = 0.6,
                                min_confidence: float = 0.5) -> List[str]:
        """
        Get agents that excel in a specific capability
        
        Args:
            capability: Capability to search for
            min_score: Minimum capability score required
            min_confidence: Minimum confidence in the score
            
        Returns:
            List[str]: List of agent IDs that meet the criteria
        """
        qualified_agents = []
        
        for agent_id, profile in self.capability_profiles.items():
            if capability in profile.capability_scores:
                score = profile.capability_scores[capability]
                if score.score >= min_score and score.confidence >= min_confidence:
                    qualified_agents.append(agent_id)
        
        # Sort by score descending
        qualified_agents.sort(
            key=lambda aid: self.capability_profiles[aid].capability_scores[capability].score,
            reverse=True
        )
        
        return qualified_agents
    
    def select_best_agent_for_task(self, required_capabilities: List[str],
                                  preferred_capabilities: List[str] = None,
                                  exclude_agents: List[str] = None) -> Optional[str]:
        """
        Select the best agent for a task based on capability requirements
        
        Args:
            required_capabilities: Capabilities that must be present
            preferred_capabilities: Capabilities that are preferred but not required
            exclude_agents: Agents to exclude from consideration
            
        Returns:
            Optional[str]: Best agent ID or None if no suitable agent found
        """
        exclude_agents = exclude_agents or []
        preferred_capabilities = preferred_capabilities or []
        
        candidate_scores = {}
        
        for agent_id, profile in self.capability_profiles.items():
            if agent_id in exclude_agents:
                continue
            
            # Check required capabilities
            meets_requirements = True
            required_score = 0.0
            
            for capability in required_capabilities:
                if capability not in profile.capability_scores:
                    meets_requirements = False
                    break
                
                score = profile.capability_scores[capability]
                if score.score < 0.6 or score.confidence < 0.5:
                    meets_requirements = False
                    break
                
                required_score += score.score * score.confidence
            
            if not meets_requirements:
                continue
            
            # Calculate total score
            total_score = required_score / len(required_capabilities) if required_capabilities else 0.0
            
            # Add bonus for preferred capabilities
            preferred_bonus = 0.0
            for capability in preferred_capabilities:
                if capability in profile.capability_scores:
                    score = profile.capability_scores[capability]
                    preferred_bonus += score.score * score.confidence * 0.5
            
            total_score += preferred_bonus / len(preferred_capabilities) if preferred_capabilities else 0.0
            
            # Factor in overall score
            total_score = total_score * 0.8 + profile.overall_score * 0.2
            
            candidate_scores[agent_id] = total_score
        
        if not candidate_scores:
            return None
        
        # Return agent with highest score
        best_agent = max(candidate_scores, key=candidate_scores.get)
        
        logger.info(f"Selected agent {best_agent} for task requiring {required_capabilities}")
        return best_agent
    
    def run_capability_benchmark(self, agent_id: str, capability: str,
                                benchmark_id: str = None) -> Dict[str, Any]:
        """
        Run a capability benchmark for an agent
        
        Args:
            agent_id: Agent to benchmark
            capability: Capability to test
            benchmark_id: Specific benchmark to run (uses default if not provided)
            
        Returns:
            Dict containing benchmark results
        """
        if benchmark_id is None:
            # Find default benchmark for capability
            for bid, benchmark in self.capability_benchmarks.items():
                if benchmark.capability == capability:
                    benchmark_id = bid
                    break
        
        if benchmark_id not in self.capability_benchmarks:
            return {"error": f"Benchmark not found: {benchmark_id}"}
        
        benchmark = self.capability_benchmarks[benchmark_id]
        
        # Simulate benchmark execution (in real implementation, this would invoke the agent)
        results = {
            "agent_id": agent_id,
            "capability": capability,
            "benchmark_id": benchmark_id,
            "started_at": datetime.utcnow(),
            "test_results": [],
            "overall_score": 0.0,
            "metrics": {}
        }
        
        # For now, simulate results based on existing profile
        profile = self.capability_profiles.get(agent_id)
        if profile and capability in profile.capability_scores:
            base_score = profile.capability_scores[capability].score
            # Add some variation
            import random
            variation = random.uniform(-0.1, 0.1)
            simulated_score = max(0.0, min(1.0, base_score + variation))
            
            results["overall_score"] = simulated_score
            results["metrics"] = {
                ScoringMetric.ACCURACY: simulated_score,
                ScoringMetric.SPEED: random.uniform(0.5, 1.0),
                ScoringMetric.CONSISTENCY: random.uniform(0.6, 1.0)
            }
        else:
            # Default performance for unknown capability
            results["overall_score"] = 0.5
            results["metrics"] = {
                ScoringMetric.ACCURACY: 0.5,
                ScoringMetric.SPEED: 0.6,
                ScoringMetric.CONSISTENCY: 0.7
            }
        
        results["completed_at"] = datetime.utcnow()
        
        # Record the evaluation
        self.evaluate_agent_capability(
            agent_id=agent_id,
            capability=capability,
            task_id=f"benchmark_{benchmark_id}",
            score=results["overall_score"],
            metrics=results["metrics"],
            evaluator="benchmark_system",
            context={"benchmark_id": benchmark_id}
        )
        
        return results
    
    def get_capability_analytics(self, capability: str = None, 
                               agent_id: str = None) -> Dict[str, Any]:
        """
        Get analytics about capability performance
        
        Args:
            capability: Specific capability to analyze (all if None)
            agent_id: Specific agent to analyze (all if None)
            
        Returns:
            Dict containing analytics data
        """
        analytics = {
            "generated_at": datetime.utcnow(),
            "capability": capability,
            "agent_id": agent_id,
            "total_evaluations": 0,
            "average_score": 0.0,
            "score_distribution": {},
            "top_performers": [],
            "improvement_trends": {},
            "recommendations": []
        }
        
        # Filter evaluations
        relevant_evaluations = self.capability_evaluations
        
        if capability:
            relevant_evaluations = [e for e in relevant_evaluations if e.capability == capability]
        
        if agent_id:
            relevant_evaluations = [e for e in relevant_evaluations if e.agent_id == agent_id]
        
        if not relevant_evaluations:
            return analytics
        
        analytics["total_evaluations"] = len(relevant_evaluations)
        
        # Calculate average score
        total_score = sum(e.score for e in relevant_evaluations)
        analytics["average_score"] = total_score / len(relevant_evaluations)
        
        # Score distribution
        score_ranges = {"0.0-0.2": 0, "0.2-0.4": 0, "0.4-0.6": 0, "0.6-0.8": 0, "0.8-1.0": 0}
        for evaluation in relevant_evaluations:
            if evaluation.score <= 0.2:
                score_ranges["0.0-0.2"] += 1
            elif evaluation.score <= 0.4:
                score_ranges["0.2-0.4"] += 1
            elif evaluation.score <= 0.6:
                score_ranges["0.4-0.6"] += 1
            elif evaluation.score <= 0.8:
                score_ranges["0.6-0.8"] += 1
            else:
                score_ranges["0.8-1.0"] += 1
        
        analytics["score_distribution"] = score_ranges
        
        # Top performers (if analyzing specific capability)
        if capability:
            capability_agents = {}
            for agent_id, profile in self.capability_profiles.items():
                if capability in profile.capability_scores:
                    score = profile.capability_scores[capability]
                    capability_agents[agent_id] = score.score
            
            sorted_agents = sorted(capability_agents.items(), key=lambda x: x[1], reverse=True)
            analytics["top_performers"] = sorted_agents[:5]
        
        # Generate recommendations
        if analytics["average_score"] < 0.6:
            analytics["recommendations"].append("Overall performance is below average - consider additional training")
        
        if score_ranges["0.0-0.2"] + score_ranges["0.2-0.4"] > len(relevant_evaluations) * 0.3:
            analytics["recommendations"].append("High number of low-performing evaluations - review task assignments")
        
        return analytics
    
    def export_capability_report(self, agent_id: str = None, 
                               format: str = "json") -> Dict[str, Any]:
        """
        Export comprehensive capability report
        
        Args:
            agent_id: Specific agent to report on (all if None)
            format: Export format (json, csv, html)
            
        Returns:
            Dict containing the report
        """
        report = {
            "generated_at": datetime.utcnow(),
            "report_type": "capability_assessment",
            "agent_id": agent_id,
            "summary": {},
            "detailed_scores": {},
            "recommendations": {},
            "analytics": {}
        }
        
        if agent_id:
            # Single agent report
            profile = self.capability_profiles.get(agent_id)
            if not profile:
                return {"error": f"Agent {agent_id} not found"}
            
            report["summary"] = {
                "overall_score": profile.overall_score,
                "capabilities_count": len(profile.capability_scores),
                "evaluations_count": len(profile.evaluations),
                "strengths": profile.strengths,
                "weaknesses": profile.weaknesses
            }
            
            report["detailed_scores"] = {
                cap: {
                    "score": score.score,
                    "level": score.level.value,
                    "confidence": score.confidence,
                    "evidence_count": score.evidence_count,
                    "metrics": {m.value: v for m, v in score.metrics.items()}
                }
                for cap, score in profile.capability_scores.items()
            }
            
            report["recommendations"] = profile.recommendations
            
        else:
            # All agents summary
            all_profiles = list(self.capability_profiles.values())
            
            if all_profiles:
                avg_overall = sum(p.overall_score for p in all_profiles) / len(all_profiles)
                total_evaluations = sum(len(p.evaluations) for p in all_profiles)
                
                report["summary"] = {
                    "total_agents": len(all_profiles),
                    "average_overall_score": avg_overall,
                    "total_evaluations": total_evaluations,
                    "capabilities_tracked": len(self.capability_templates)
                }
                
                # Top performing agents
                sorted_agents = sorted(all_profiles, key=lambda p: p.overall_score, reverse=True)
                report["top_performers"] = [
                    {"agent_id": p.agent_id, "overall_score": p.overall_score}
                    for p in sorted_agents[:10]
                ]
        
        # Add analytics
        report["analytics"] = self.get_capability_analytics(agent_id=agent_id)
        
        return report
    
    def __repr__(self) -> str:
        return f"<AgentFactory(active_agents={len(self.active_agents)}, sessions={len(self.agent_sessions)})>"


# Global factory instance
agent_factory = AgentFactory()