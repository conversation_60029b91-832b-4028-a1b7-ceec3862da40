"""
Agent-to-Agent Messaging Protocol for Metamorphic Reactor
Implements structured communication protocol between agents
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Union, Callable, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
import uuid

from .message_bus import MessageBus, MessageType, MessagePriority, Message, message_bus

logger = logging.getLogger(__name__)

class ProtocolMessageType(str, Enum):
    """Protocol-specific message types"""
    HANDSHAKE = "handshake"
    ACKNOWLEDGMENT = "acknowledgment"
    REQUEST = "request"
    RESPONSE = "response"
    NOTIFICATION = "notification"
    COLLABORATION_REQUEST = "collaboration_request"
    COLLABORATION_RESPONSE = "collaboration_response"
    TASK_ASSIGNMENT = "task_assignment"
    TASK_UPDATE = "task_update"
    TASK_COMPLETION = "task_completion"
    CONSENSUS_PROPOSAL = "consensus_proposal"
    CONSENSUS_VOTE = "consensus_vote"
    CONSENSUS_RESULT = "consensus_result"
    ERROR = "error"
    HEARTBEAT = "heartbeat"

class ProtocolStatus(str, Enum):
    """Protocol status codes"""
    SUCCESS = "success"
    ERROR = "error"
    TIMEOUT = "timeout"
    REJECTED = "rejected"
    PENDING = "pending"

@dataclass
class ProtocolHeader:
    """Protocol message header"""
    version: str = "1.0"
    message_type: ProtocolMessageType = ProtocolMessageType.NOTIFICATION
    correlation_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    reply_to: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.utcnow)
    ttl: int = 300  # Time to live in seconds
    priority: MessagePriority = MessagePriority.NORMAL
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "version": self.version,
            "message_type": self.message_type.value,
            "correlation_id": self.correlation_id,
            "reply_to": self.reply_to,
            "timestamp": self.timestamp.isoformat(),
            "ttl": self.ttl,
            "priority": self.priority.value
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProtocolHeader':
        return cls(
            version=data.get("version", "1.0"),
            message_type=ProtocolMessageType(data["message_type"]),
            correlation_id=data["correlation_id"],
            reply_to=data.get("reply_to"),
            timestamp=datetime.fromisoformat(data["timestamp"]),
            ttl=data.get("ttl", 300),
            priority=MessagePriority(data.get("priority", MessagePriority.NORMAL))
        )

@dataclass
class ProtocolMessage:
    """Protocol message structure"""
    header: ProtocolHeader
    sender_id: str
    recipient_id: str
    payload: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "header": self.header.to_dict(),
            "sender_id": self.sender_id,
            "recipient_id": self.recipient_id,
            "payload": self.payload,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProtocolMessage':
        return cls(
            header=ProtocolHeader.from_dict(data["header"]),
            sender_id=data["sender_id"],
            recipient_id=data["recipient_id"],
            payload=data.get("payload", {}),
            metadata=data.get("metadata", {})
        )

class MessageProtocol:
    """
    Agent-to-Agent Messaging Protocol
    Provides structured communication between agents
    """
    
    def __init__(self, agent_id: str, message_bus: MessageBus):
        self.agent_id = agent_id
        self.message_bus = message_bus
        
        # Protocol state
        self.active_conversations: Dict[str, Dict[str, Any]] = {}  # correlation_id -> conversation
        self.pending_responses: Dict[str, asyncio.Future] = {}  # correlation_id -> future
        self.message_handlers: Dict[ProtocolMessageType, Callable] = {}
        
        # Protocol configuration
        self.default_timeout = 30  # seconds
        self.max_retries = 3
        self.heartbeat_interval = 60  # seconds
        
        # Register with message bus
        self.message_bus.register_handler(
            handler_id=f"{agent_id}_protocol",
            agent_id=agent_id,
            message_types=[MessageType.DIRECT, MessageType.SYSTEM],
            callback=self._handle_raw_message,
            priority=10
        )
        
        # Start heartbeat
        self.heartbeat_task = None
        
        logger.info(f"MessageProtocol initialized for agent {agent_id}")
    
    async def start(self):
        """Start the protocol handler"""
        self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
        logger.info(f"MessageProtocol started for agent {self.agent_id}")
    
    async def stop(self):
        """Stop the protocol handler"""
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
            try:
                await self.heartbeat_task
            except asyncio.CancelledError:
                pass
        
        # Cancel pending responses
        for future in self.pending_responses.values():
            if not future.done():
                future.cancel()
        
        self.message_bus.unregister_handler(f"{self.agent_id}_protocol")
        logger.info(f"MessageProtocol stopped for agent {self.agent_id}")
    
    def register_handler(self, message_type: ProtocolMessageType, handler: Callable):
        """Register a handler for a specific protocol message type"""
        self.message_handlers[message_type] = handler
        logger.debug(f"Registered handler for {message_type} in agent {self.agent_id}")
    
    async def send_message(
        self,
        recipient_id: str,
        message_type: ProtocolMessageType,
        payload: Dict[str, Any],
        priority: MessagePriority = MessagePriority.NORMAL,
        ttl: int = 300,
        correlation_id: Optional[str] = None,
        reply_to: Optional[str] = None
    ) -> str:
        """Send a protocol message to another agent"""
        
        header = ProtocolHeader(
            message_type=message_type,
            correlation_id=correlation_id or str(uuid.uuid4()),
            reply_to=reply_to,
            priority=priority,
            ttl=ttl
        )
        
        protocol_message = ProtocolMessage(
            header=header,
            sender_id=self.agent_id,
            recipient_id=recipient_id,
            payload=payload
        )
        
        # Send through message bus
        message_id = await self.message_bus.send_message(
            message_type=MessageType.DIRECT,
            sender_id=self.agent_id,
            recipient_id=recipient_id,
            content=json.dumps(protocol_message.to_dict()),
            priority=priority,
            expires_in=timedelta(seconds=ttl),
            correlation_id=header.correlation_id
        )
        
        logger.debug(f"Sent {message_type} message from {self.agent_id} to {recipient_id}")
        return header.correlation_id
    
    async def send_request(
        self,
        recipient_id: str,
        request_type: str,
        payload: Dict[str, Any],
        timeout: int = None
    ) -> Dict[str, Any]:
        """Send a request and wait for response"""
        
        correlation_id = str(uuid.uuid4())
        timeout = timeout or self.default_timeout
        
        # Create future for response
        response_future = asyncio.Future()
        self.pending_responses[correlation_id] = response_future
        
        # Send request
        await self.send_message(
            recipient_id=recipient_id,
            message_type=ProtocolMessageType.REQUEST,
            payload={
                "request_type": request_type,
                **payload
            },
            correlation_id=correlation_id,
            reply_to=self.agent_id
        )
        
        try:
            # Wait for response
            response = await asyncio.wait_for(response_future, timeout=timeout)
            return response
        except asyncio.TimeoutError:
            raise TimeoutError(f"Request to {recipient_id} timed out after {timeout}s")
        finally:
            # Clean up
            self.pending_responses.pop(correlation_id, None)
    
    async def send_response(
        self,
        recipient_id: str,
        correlation_id: str,
        payload: Dict[str, Any],
        status: ProtocolStatus = ProtocolStatus.SUCCESS
    ):
        """Send a response to a request"""
        
        await self.send_message(
            recipient_id=recipient_id,
            message_type=ProtocolMessageType.RESPONSE,
            payload={
                "status": status.value,
                **payload
            },
            correlation_id=correlation_id,
            reply_to=self.agent_id
        )
    
    async def send_notification(
        self,
        recipient_id: str,
        notification_type: str,
        payload: Dict[str, Any]
    ):
        """Send a notification to another agent"""
        
        await self.send_message(
            recipient_id=recipient_id,
            message_type=ProtocolMessageType.NOTIFICATION,
            payload={
                "notification_type": notification_type,
                **payload
            }
        )
    
    async def send_collaboration_request(
        self,
        recipient_id: str,
        task_id: str,
        task_description: str,
        required_capabilities: List[str] = None,
        deadline: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Send a collaboration request"""
        
        payload = {
            "task_id": task_id,
            "task_description": task_description,
            "required_capabilities": required_capabilities or [],
            "deadline": deadline.isoformat() if deadline else None
        }
        
        response = await self.send_request(
            recipient_id=recipient_id,
            request_type="collaboration",
            payload=payload
        )
        
        return response
    
    async def send_collaboration_response(
        self,
        recipient_id: str,
        correlation_id: str,
        accepted: bool,
        message: str = "",
        estimated_completion: Optional[datetime] = None
    ):
        """Send a collaboration response"""
        
        payload = {
            "accepted": accepted,
            "message": message,
            "estimated_completion": estimated_completion.isoformat() if estimated_completion else None
        }
        
        await self.send_response(
            recipient_id=recipient_id,
            correlation_id=correlation_id,
            payload=payload,
            status=ProtocolStatus.SUCCESS if accepted else ProtocolStatus.REJECTED
        )
    
    async def send_task_assignment(
        self,
        recipient_id: str,
        task_id: str,
        task_description: str,
        context: Dict[str, Any] = None,
        deadline: Optional[datetime] = None
    ):
        """Send a task assignment"""
        
        payload = {
            "task_id": task_id,
            "task_description": task_description,
            "context": context or {},
            "deadline": deadline.isoformat() if deadline else None
        }
        
        await self.send_message(
            recipient_id=recipient_id,
            message_type=ProtocolMessageType.TASK_ASSIGNMENT,
            payload=payload,
            priority=MessagePriority.HIGH
        )
    
    async def send_task_update(
        self,
        recipient_id: str,
        task_id: str,
        status: str,
        progress: float,
        message: str = "",
        data: Dict[str, Any] = None
    ):
        """Send a task update"""
        
        payload = {
            "task_id": task_id,
            "status": status,
            "progress": progress,
            "message": message,
            "data": data or {}
        }
        
        await self.send_message(
            recipient_id=recipient_id,
            message_type=ProtocolMessageType.TASK_UPDATE,
            payload=payload
        )
    
    async def send_task_completion(
        self,
        recipient_id: str,
        task_id: str,
        success: bool,
        result: Dict[str, Any] = None,
        message: str = ""
    ):
        """Send task completion notification"""
        
        payload = {
            "task_id": task_id,
            "success": success,
            "result": result or {},
            "message": message
        }
        
        await self.send_message(
            recipient_id=recipient_id,
            message_type=ProtocolMessageType.TASK_COMPLETION,
            payload=payload,
            priority=MessagePriority.HIGH
        )
    
    async def send_consensus_proposal(
        self,
        recipient_id: str,
        proposal_id: str,
        proposal_text: str,
        context: Dict[str, Any] = None,
        voting_deadline: Optional[datetime] = None
    ):
        """Send a consensus proposal"""
        
        payload = {
            "proposal_id": proposal_id,
            "proposal_text": proposal_text,
            "context": context or {},
            "voting_deadline": voting_deadline.isoformat() if voting_deadline else None
        }
        
        await self.send_message(
            recipient_id=recipient_id,
            message_type=ProtocolMessageType.CONSENSUS_PROPOSAL,
            payload=payload,
            priority=MessagePriority.HIGH
        )
    
    async def send_consensus_vote(
        self,
        recipient_id: str,
        proposal_id: str,
        vote: bool,
        reasoning: str = "",
        confidence: float = 1.0
    ):
        """Send a consensus vote"""
        
        payload = {
            "proposal_id": proposal_id,
            "vote": vote,
            "reasoning": reasoning,
            "confidence": confidence
        }
        
        await self.send_message(
            recipient_id=recipient_id,
            message_type=ProtocolMessageType.CONSENSUS_VOTE,
            payload=payload,
            priority=MessagePriority.HIGH
        )
    
    async def send_heartbeat(self, recipient_id: str):
        """Send a heartbeat to another agent"""
        
        payload = {
            "agent_id": self.agent_id,
            "timestamp": datetime.utcnow().isoformat(),
            "status": "active"
        }
        
        await self.send_message(
            recipient_id=recipient_id,
            message_type=ProtocolMessageType.HEARTBEAT,
            payload=payload,
            priority=MessagePriority.LOW
        )
    
    async def _handle_raw_message(self, message: Message):
        """Handle raw messages from the message bus"""
        try:
            # Parse protocol message
            protocol_message = ProtocolMessage.from_dict(json.loads(message.content))
            
            # Check if message is for this agent
            if protocol_message.recipient_id != self.agent_id:
                return
            
            # Handle based on message type
            await self._handle_protocol_message(protocol_message)
            
        except Exception as e:
            logger.error(f"Failed to handle raw message: {e}")
    
    async def _handle_protocol_message(self, protocol_message: ProtocolMessage):
        """Handle protocol messages"""
        message_type = protocol_message.header.message_type
        
        # Handle responses to pending requests
        if message_type == ProtocolMessageType.RESPONSE:
            correlation_id = protocol_message.header.correlation_id
            if correlation_id in self.pending_responses:
                future = self.pending_responses[correlation_id]
                if not future.done():
                    future.set_result(protocol_message.payload)
                return
        
        # Handle with registered handlers
        if message_type in self.message_handlers:
            try:
                handler = self.message_handlers[message_type]
                if asyncio.iscoroutinefunction(handler):
                    await handler(protocol_message)
                else:
                    handler(protocol_message)
            except Exception as e:
                logger.error(f"Handler for {message_type} failed: {e}")
                
                # Send error response if this was a request
                if message_type == ProtocolMessageType.REQUEST:
                    await self.send_response(
                        recipient_id=protocol_message.sender_id,
                        correlation_id=protocol_message.header.correlation_id,
                        payload={"error": str(e)},
                        status=ProtocolStatus.ERROR
                    )
        else:
            logger.warning(f"No handler for message type {message_type}")
    
    async def _heartbeat_loop(self):
        """Background heartbeat loop"""
        while True:
            try:
                # Send heartbeat to all connected agents
                # This would be implemented based on your specific requirements
                await asyncio.sleep(self.heartbeat_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Heartbeat loop error: {e}")
                await asyncio.sleep(5)
    
    def get_conversation_history(self, correlation_id: str) -> List[Dict[str, Any]]:
        """Get conversation history for a correlation ID"""
        return self.active_conversations.get(correlation_id, {}).get("messages", [])
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get protocol statistics"""
        return {
            "active_conversations": len(self.active_conversations),
            "pending_responses": len(self.pending_responses),
            "registered_handlers": len(self.message_handlers),
            "agent_id": self.agent_id
        }

class TeamCommunicationChannel:
    """
    Team communication channel for group messaging
    """
    
    def __init__(self, team_id: str, name: str, message_bus: MessageBus):
        self.team_id = team_id
        self.name = name
        self.message_bus = message_bus
        self.members: Set[str] = set()
        self.channel_id = f"team_{team_id}"
        self.message_history: List[Dict[str, Any]] = []
        self.created_at = datetime.utcnow()
        self.is_active = True
        
        # Channel settings
        self.max_history_size = 1000
        self.auto_archive_days = 30
        
        # Create channel in message bus
        self.message_bus.create_channel(self.channel_id, list(self.members))
        self.message_bus.set_team_channel(self.team_id, self.channel_id)
        
        logger.info(f"Created team communication channel {self.channel_id} for team {team_id}")
    
    def add_member(self, agent_id: str) -> bool:
        """Add a member to the team channel"""
        if agent_id not in self.members:
            self.members.add(agent_id)
            self.message_bus.join_channel(self.channel_id, agent_id)
            
            # Send welcome message
            self._add_system_message(f"Agent {agent_id} joined the channel")
            
            logger.info(f"Added agent {agent_id} to team channel {self.channel_id}")
            return True
        return False
    
    def remove_member(self, agent_id: str) -> bool:
        """Remove a member from the team channel"""
        if agent_id in self.members:
            self.members.remove(agent_id)
            self.message_bus.leave_channel(self.channel_id, agent_id)
            
            # Send departure message
            self._add_system_message(f"Agent {agent_id} left the channel")
            
            logger.info(f"Removed agent {agent_id} from team channel {self.channel_id}")
            return True
        return False
    
    async def send_message(
        self,
        sender_id: str,
        content: str,
        message_type: str = "text",
        data: Dict[str, Any] = None
    ) -> str:
        """Send a message to the team channel"""
        if sender_id not in self.members:
            raise ValueError(f"Agent {sender_id} is not a member of this channel")
        
        # Send message through message bus
        message_id = await self.message_bus.send_message(
            message_type=MessageType.TEAM,
            sender_id=sender_id,
            content=content,
            team_id=self.team_id,
            data=data or {},
            priority=MessagePriority.NORMAL
        )
        
        # Add to channel history
        self._add_message_to_history({
            "id": message_id,
            "sender_id": sender_id,
            "content": content,
            "message_type": message_type,
            "data": data or {},
            "timestamp": datetime.utcnow().isoformat()
        })
        
        return message_id
    
    async def broadcast_announcement(
        self,
        sender_id: str,
        announcement: str,
        priority: MessagePriority = MessagePriority.HIGH
    ) -> str:
        """Broadcast an announcement to all team members"""
        message_id = await self.message_bus.send_message(
            message_type=MessageType.TEAM,
            sender_id=sender_id,
            content=announcement,
            team_id=self.team_id,
            data={"announcement": True},
            priority=priority
        )
        
        # Add to channel history
        self._add_message_to_history({
            "id": message_id,
            "sender_id": sender_id,
            "content": announcement,
            "message_type": "announcement",
            "data": {"announcement": True},
            "timestamp": datetime.utcnow().isoformat()
        })
        
        return message_id
    
    def get_message_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent message history"""
        return self.message_history[-limit:]
    
    def get_member_list(self) -> List[str]:
        """Get list of channel members"""
        return list(self.members)
    
    def get_channel_info(self) -> Dict[str, Any]:
        """Get channel information"""
        return {
            "team_id": self.team_id,
            "name": self.name,
            "channel_id": self.channel_id,
            "members": list(self.members),
            "member_count": len(self.members),
            "created_at": self.created_at.isoformat(),
            "is_active": self.is_active,
            "message_count": len(self.message_history)
        }
    
    def _add_message_to_history(self, message: Dict[str, Any]):
        """Add message to channel history"""
        self.message_history.append(message)
        
        # Trim history if too long
        if len(self.message_history) > self.max_history_size:
            self.message_history.pop(0)
    
    def _add_system_message(self, content: str):
        """Add a system message to the history"""
        self._add_message_to_history({
            "id": str(uuid.uuid4()),
            "sender_id": "system",
            "content": content,
            "message_type": "system",
            "data": {},
            "timestamp": datetime.utcnow().isoformat()
        })
    
    def archive_channel(self):
        """Archive the channel"""
        self.is_active = False
        self._add_system_message("Channel archived")
        logger.info(f"Archived team channel {self.channel_id}")
    
    def reactivate_channel(self):
        """Reactivate the channel"""
        self.is_active = True
        self._add_system_message("Channel reactivated")
        logger.info(f"Reactivated team channel {self.channel_id}")

class AgentMessagingService:
    """
    Service for managing agent messaging protocols and team channels
    """
    
    def __init__(self, message_bus: MessageBus):
        self.message_bus = message_bus
        self.agent_protocols: Dict[str, MessageProtocol] = {}
        self.team_channels: Dict[str, TeamCommunicationChannel] = {}
    
    async def register_agent(self, agent_id: str) -> MessageProtocol:
        """Register an agent with the messaging service"""
        if agent_id in self.agent_protocols:
            return self.agent_protocols[agent_id]
        
        protocol = MessageProtocol(agent_id, self.message_bus)
        await protocol.start()
        
        self.agent_protocols[agent_id] = protocol
        
        logger.info(f"Registered agent {agent_id} with messaging service")
        return protocol
    
    async def unregister_agent(self, agent_id: str):
        """Unregister an agent from the messaging service"""
        if agent_id in self.agent_protocols:
            protocol = self.agent_protocols.pop(agent_id)
            await protocol.stop()
            
            logger.info(f"Unregistered agent {agent_id} from messaging service")
    
    def get_protocol(self, agent_id: str) -> Optional[MessageProtocol]:
        """Get the protocol for an agent"""
        return self.agent_protocols.get(agent_id)
    
    async def broadcast_message(
        self,
        sender_id: str,
        message_type: ProtocolMessageType,
        payload: Dict[str, Any],
        exclude_agents: List[str] = None
    ):
        """Broadcast a message to all registered agents"""
        exclude_agents = exclude_agents or []
        
        for agent_id, protocol in self.agent_protocols.items():
            if agent_id != sender_id and agent_id not in exclude_agents:
                try:
                    await protocol.send_message(
                        recipient_id=agent_id,
                        message_type=message_type,
                        payload=payload
                    )
                except Exception as e:
                    logger.error(f"Failed to send broadcast to {agent_id}: {e}")
    
    def create_team_channel(
        self,
        team_id: str,
        channel_name: str,
        initial_members: List[str] = None
    ) -> TeamCommunicationChannel:
        """Create a team communication channel"""
        if team_id in self.team_channels:
            raise ValueError(f"Team channel for {team_id} already exists")
        
        channel = TeamCommunicationChannel(team_id, channel_name, self.message_bus)
        
        # Add initial members
        if initial_members:
            for member_id in initial_members:
                channel.add_member(member_id)
        
        self.team_channels[team_id] = channel
        
        logger.info(f"Created team channel {channel_name} for team {team_id}")
        return channel
    
    def get_team_channel(self, team_id: str) -> Optional[TeamCommunicationChannel]:
        """Get team communication channel"""
        return self.team_channels.get(team_id)
    
    def delete_team_channel(self, team_id: str) -> bool:
        """Delete a team communication channel"""
        if team_id in self.team_channels:
            channel = self.team_channels.pop(team_id)
            channel.archive_channel()
            
            logger.info(f"Deleted team channel for team {team_id}")
            return True
        return False
    
    async def add_agent_to_team_channel(self, team_id: str, agent_id: str) -> bool:
        """Add an agent to a team channel"""
        channel = self.team_channels.get(team_id)
        if channel:
            return channel.add_member(agent_id)
        return False
    
    async def remove_agent_from_team_channel(self, team_id: str, agent_id: str) -> bool:
        """Remove an agent from a team channel"""
        channel = self.team_channels.get(team_id)
        if channel:
            return channel.remove_member(agent_id)
        return False
    
    async def send_team_message(
        self,
        team_id: str,
        sender_id: str,
        content: str,
        message_type: str = "text",
        data: Dict[str, Any] = None
    ) -> Optional[str]:
        """Send a message to a team channel"""
        channel = self.team_channels.get(team_id)
        if channel:
            return await channel.send_message(sender_id, content, message_type, data)
        return None
    
    async def broadcast_team_announcement(
        self,
        team_id: str,
        sender_id: str,
        announcement: str,
        priority: MessagePriority = MessagePriority.HIGH
    ) -> Optional[str]:
        """Broadcast an announcement to a team channel"""
        channel = self.team_channels.get(team_id)
        if channel:
            return await channel.broadcast_announcement(sender_id, announcement, priority)
        return None
    
    def get_team_message_history(self, team_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get message history for a team channel"""
        channel = self.team_channels.get(team_id)
        if channel:
            return channel.get_message_history(limit)
        return []
    
    def get_team_members(self, team_id: str) -> List[str]:
        """Get members of a team channel"""
        channel = self.team_channels.get(team_id)
        if channel:
            return channel.get_member_list()
        return []
    
    def get_team_channel_info(self, team_id: str) -> Optional[Dict[str, Any]]:
        """Get information about a team channel"""
        channel = self.team_channels.get(team_id)
        if channel:
            return channel.get_channel_info()
        return None
    
    def list_team_channels(self) -> List[Dict[str, Any]]:
        """List all team channels"""
        return [channel.get_channel_info() for channel in self.team_channels.values()]
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get service statistics"""
        return {
            "registered_agents": len(self.agent_protocols),
            "team_channels": len(self.team_channels),
            "protocols": {agent_id: protocol.get_statistics() 
                        for agent_id, protocol in self.agent_protocols.items()},
            "channels": {team_id: channel.get_channel_info() 
                        for team_id, channel in self.team_channels.items()}
        }

# Global messaging service
messaging_service = AgentMessagingService(message_bus)