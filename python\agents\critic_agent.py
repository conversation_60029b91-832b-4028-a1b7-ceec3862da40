"""
Critic Agent implementation for Metamorphic Reactor
Responsible for analyzing plans and providing constructive feedback
"""

import logging
from typing import List
from datetime import datetime

from .base_agent import BaseAgent
from .agent_types import AgentConfig, AgentResponse, TaskContext, ConversationHistory

logger = logging.getLogger(__name__)

class CriticAgent(BaseAgent):
    """
    Critic Agent - Analyzes and critiques implementation plans
    
    Responsibilities:
    - Evaluate plan feasibility and completeness
    - Identify potential issues, risks, and gaps
    - Suggest improvements and alternatives
    - Ensure quality standards and best practices
    - Challenge assumptions and validate approaches
    """
    
    def __init__(self, config: AgentConfig):
        super().__init__(config)
        self.evaluation_frameworks = {
            "feasibility": self._evaluate_feasibility,
            "completeness": self._evaluate_completeness,
            "quality": self._evaluate_quality,
            "risks": self._evaluate_risks,
            "best_practices": self._evaluate_best_practices
        }
    
    async def generate_response(
        self, 
        task_context: TaskContext, 
        conversation_history: ConversationHistory,
        current_proposals: List[AgentResponse]
    ) -> AgentResponse:
        """Generate constructive critique of current proposals"""
        
        logger.info(f"Critic {self.agent_id} evaluating {len(current_proposals)} proposals")
        
        # Find planner proposals to critique
        planner_proposals = [
            p for p in current_proposals 
            if p.role.value == "planner"
        ]
        
        if not planner_proposals:
            # No plan to critique yet
            critique_content = self._generate_waiting_response(task_context)
            consensus_score = 0.0
            agrees = False
        else:
            # Analyze and critique the latest plan
            latest_plan = planner_proposals[-1]
            critique_content = await self._analyze_plan(
                latest_plan, task_context, current_proposals
            )
            consensus_score = self._calculate_critique_consensus(latest_plan, current_proposals)
            agrees = consensus_score > 0.7
        
        response = AgentResponse(
            agent_id=self.agent_id,
            session_id=self.session_id,
            role=self.config.role,
            message_id=f"critique_{int(datetime.utcnow().timestamp())}",
            content=critique_content,
            reasoning=self._get_critique_reasoning(current_proposals),
            confidence=self._calculate_critique_confidence(current_proposals),
            agrees_with_proposal=agrees,
            consensus_score=consensus_score,
            suggested_changes=self._generate_improvement_suggestions(planner_proposals),
            token_count=len(critique_content.split()),
            response_time_ms=0  # Will be set by base class
        )
        
        return response
    
    def get_role_prompt(self, task_context: TaskContext) -> str:
        """Get critic-specific system prompt"""
        
        return f"""You are an expert Critic Agent in the Metamorphic Reactor system.

Your role is to analyze implementation plans and provide constructive, actionable feedback.

TASK: {task_context.description}

RESPONSIBILITIES:
1. Evaluate plan feasibility and technical soundness
2. Identify gaps, risks, and potential issues
3. Suggest concrete improvements and alternatives
4. Ensure adherence to best practices and standards
5. Challenge assumptions and validate approaches

EVALUATION CRITERIA:
- Technical feasibility and implementation complexity
- Completeness and thoroughness of planning
- Risk identification and mitigation strategies
- Resource allocation and timeline realism
- Quality standards and best practices adherence
- Scalability and maintainability considerations

FEEDBACK PRINCIPLES:
- Be constructive and solution-oriented
- Provide specific, actionable suggestions
- Consider multiple perspectives and alternatives
- Balance thoroughness with practicality
- Support suggestions with reasoning and evidence

CONTEXT FILES: {', '.join(task_context.context_files) if task_context.context_files else 'None'}

Current round: {task_context.current_round}/{task_context.max_rounds}

Analyze the current proposals and provide detailed, constructive feedback."""
    
    def _generate_waiting_response(self, task_context: TaskContext) -> str:
        """Generate response when no plan is available to critique"""
        
        return f"""# CRITIC ANALYSIS - AWAITING PLAN

## Current Status
Currently waiting for implementation plan from Planner agent.

## Task Understanding
**Task**: {task_context.description}

## Preliminary Considerations
Based on the task description, I anticipate the following areas will require careful evaluation:

### Technical Feasibility
- Implementation complexity and resource requirements
- Technology stack and dependency considerations
- Scalability and performance implications

### Risk Assessment  
- Potential blockers and mitigation strategies
- Timeline and resource constraints
- Quality assurance and testing approaches

### Best Practices
- Industry standards and conventions
- Security and maintainability considerations
- Documentation and knowledge transfer

## Ready to Evaluate
Once a plan is proposed, I will provide detailed analysis across:
- Feasibility and technical soundness
- Completeness and thoroughness
- Risk identification and mitigation
- Improvement suggestions and alternatives

**Awaiting planner proposal for detailed critique...**"""
    
    async def _analyze_plan(
        self, 
        plan: AgentResponse, 
        task_context: TaskContext,
        all_proposals: List[AgentResponse]
    ) -> str:
        """Perform comprehensive plan analysis"""
        
        # Extract plan content for analysis
        plan_content = plan.content
        
        # Run evaluation frameworks
        evaluations = {}
        for framework_name, framework_func in self.evaluation_frameworks.items():
            evaluations[framework_name] = framework_func(plan_content, task_context)
        
        # Generate comprehensive critique
        critique = self._format_critique_response(evaluations, plan, task_context)
        
        return critique
    
    def _evaluate_feasibility(self, plan_content: str, task_context: TaskContext) -> dict:
        """Evaluate plan feasibility"""
        
        feasibility_score = 0.8  # Base score
        issues = []
        suggestions = []
        
        # Check for timeline realism
        if "timeline" not in plan_content.lower() and "phase" in plan_content.lower():
            issues.append("Timeline estimates not clearly specified")
            suggestions.append("Add specific timeline estimates for each phase")
            feasibility_score -= 0.1
        
        # Check for resource consideration
        if "resource" not in plan_content.lower():
            issues.append("Resource requirements not addressed")
            suggestions.append("Include resource allocation and requirements analysis")
            feasibility_score -= 0.1
        
        # Check for complexity consideration
        if len(task_context.description.split()) > 50 and "complex" not in plan_content.lower():
            issues.append("Plan may underestimate task complexity")
            suggestions.append("Consider breaking down complex requirements further")
            feasibility_score -= 0.05
        
        return {
            "score": max(0.0, feasibility_score),
            "issues": issues,
            "suggestions": suggestions
        }
    
    def _evaluate_completeness(self, plan_content: str, task_context: TaskContext) -> dict:
        """Evaluate plan completeness"""
        
        completeness_score = 0.7  # Base score
        issues = []
        suggestions = []
        
        required_sections = [
            ("requirements", "Requirements analysis"),
            ("implementation", "Implementation strategy"),
            ("testing", "Testing approach"),
            ("risks", "Risk assessment")
        ]
        
        for keyword, section_name in required_sections:
            if keyword not in plan_content.lower():
                issues.append(f"Missing {section_name.lower()}")
                suggestions.append(f"Add detailed {section_name.lower()}")
                completeness_score -= 0.1
            else:
                completeness_score += 0.05
        
        # Check for context file utilization
        if task_context.context_files and "context" not in plan_content.lower():
            issues.append("Context files not incorporated into plan")
            suggestions.append("Analyze and integrate context files into planning")
            completeness_score -= 0.1
        
        return {
            "score": max(0.0, min(1.0, completeness_score)),
            "issues": issues,
            "suggestions": suggestions
        }
    
    def _evaluate_quality(self, plan_content: str, task_context: TaskContext) -> dict:
        """Evaluate plan quality"""
        
        quality_score = 0.8  # Base score
        issues = []
        suggestions = []
        
        # Check for structure and organization
        if plan_content.count("#") < 3:  # Less than 3 headers
            issues.append("Plan lacks clear structure and organization")
            suggestions.append("Use clear headings and structured sections")
            quality_score -= 0.1
        
        # Check for specificity
        vague_indicators = ["etc", "and so on", "various", "multiple"]
        if any(indicator in plan_content.lower() for indicator in vague_indicators):
            issues.append("Plan contains vague or non-specific elements")
            suggestions.append("Replace general terms with specific, actionable items")
            quality_score -= 0.05
        
        # Check for actionability
        if plan_content.count("-") < 5:  # Less than 5 bullet points
            issues.append("Plan lacks specific actionable items")
            suggestions.append("Break down phases into specific, actionable tasks")
            quality_score -= 0.1
        
        return {
            "score": max(0.0, quality_score),
            "issues": issues,
            "suggestions": suggestions
        }
    
    def _evaluate_risks(self, plan_content: str, task_context: TaskContext) -> dict:
        """Evaluate risk identification and mitigation"""
        
        risk_score = 0.6  # Base score
        issues = []
        suggestions = []
        
        # Check for risk identification
        if "risk" in plan_content.lower():
            risk_score += 0.2
        else:
            issues.append("No risk assessment included")
            suggestions.append("Add comprehensive risk analysis and mitigation strategies")
            risk_score -= 0.2
        
        # Check for mitigation strategies
        if "mitigation" in plan_content.lower() or "contingency" in plan_content.lower():
            risk_score += 0.1
        else:
            issues.append("Risk mitigation strategies not specified")
            suggestions.append("Include specific risk mitigation and contingency plans")
            risk_score -= 0.1
        
        # Check for dependency analysis
        if "dependency" in plan_content.lower() or "dependencies" in plan_content.lower():
            risk_score += 0.1
        else:
            issues.append("Dependencies not identified")
            suggestions.append("Identify and plan for external dependencies")
            risk_score -= 0.05
        
        return {
            "score": max(0.0, risk_score),
            "issues": issues,
            "suggestions": suggestions
        }
    
    def _evaluate_best_practices(self, plan_content: str, task_context: TaskContext) -> dict:
        """Evaluate adherence to best practices"""
        
        practices_score = 0.7  # Base score
        issues = []
        suggestions = []
        
        # Check for documentation consideration
        if "documentation" in plan_content.lower():
            practices_score += 0.1
        else:
            issues.append("Documentation strategy not addressed")
            suggestions.append("Include comprehensive documentation planning")
            practices_score -= 0.1
        
        # Check for version control/collaboration
        if any(term in plan_content.lower() for term in ["version", "git", "collaboration"]):
            practices_score += 0.05
        else:
            issues.append("Version control and collaboration not considered")
            suggestions.append("Plan for version control and team collaboration")
            practices_score -= 0.05
        
        # Check for iterative approach
        if any(term in plan_content.lower() for term in ["iterative", "feedback", "review"]):
            practices_score += 0.1
        else:
            issues.append("Plan lacks iterative refinement approach")
            suggestions.append("Include feedback loops and iterative improvement")
            practices_score -= 0.1
        
        return {
            "score": max(0.0, practices_score),
            "issues": issues,
            "suggestions": suggestions
        }
    
    def _format_critique_response(
        self, 
        evaluations: dict, 
        plan: AgentResponse, 
        task_context: TaskContext
    ) -> str:
        """Format comprehensive critique response"""
        
        # Calculate overall score
        overall_score = sum(eval_data["score"] for eval_data in evaluations.values()) / len(evaluations)
        
        # Determine overall assessment
        if overall_score >= 0.8:
            assessment = "STRONG - Plan is well-structured and comprehensive"
        elif overall_score >= 0.6:
            assessment = "GOOD - Plan is solid with room for improvement"
        elif overall_score >= 0.4:
            assessment = "FAIR - Plan needs significant improvements"
        else:
            assessment = "NEEDS WORK - Plan requires major revision"
        
        critique = f"""# CRITIC ANALYSIS - ROUND {task_context.current_round}

## OVERALL ASSESSMENT: {assessment}
**Overall Score**: {overall_score:.2f}/1.00

## DETAILED EVALUATION

"""
        
        # Add detailed evaluations
        for framework_name, eval_data in evaluations.items():
            critique += f"""### {framework_name.upper()} ANALYSIS
**Score**: {eval_data["score"]:.2f}/1.00

"""
            if eval_data["issues"]:
                critique += "**Issues Identified**:\n"
                for issue in eval_data["issues"]:
                    critique += f"- {issue}\n"
                critique += "\n"
            
            if eval_data["suggestions"]:
                critique += "**Suggestions**:\n"
                for suggestion in eval_data["suggestions"]:
                    critique += f"- {suggestion}\n"
                critique += "\n"
        
        # Add consensus assessment
        consensus_level = "HIGH" if overall_score >= 0.7 else "MEDIUM" if overall_score >= 0.5 else "LOW"
        critique += f"""## CONSENSUS ASSESSMENT
**Consensus Level**: {consensus_level}
**Agreement with Plan**: {"YES" if overall_score >= 0.7 else "CONDITIONAL" if overall_score >= 0.5 else "NO"}

"""
        
        # Add next steps
        if overall_score >= 0.8:
            critique += """## NEXT STEPS
Plan is ready for implementation with minor refinements. Recommend proceeding with execution while addressing specific suggestions above."""
        elif overall_score >= 0.6:
            critique += """## NEXT STEPS
Plan requires moderate improvements before implementation. Address identified issues and enhance weak areas. Consider additional planning iteration."""
        else:
            critique += """## NEXT STEPS
Plan needs significant revision. Recommend major restructuring to address fundamental issues. Additional planning rounds strongly advised."""
        
        return critique
    
    def _calculate_critique_consensus(
        self, 
        plan: AgentResponse, 
        all_proposals: List[AgentResponse]
    ) -> float:
        """Calculate consensus score based on critique analysis"""
        
        # Simple scoring based on plan quality assessment
        plan_length = len(plan.content.split())
        structure_score = min(1.0, plan.content.count("#") / 5)  # Up to 5 headers
        detail_score = min(1.0, plan_length / 500)  # Normalize to 500 words
        
        return (structure_score + detail_score) / 2
    
    def _get_critique_reasoning(self, current_proposals: List[AgentResponse]) -> str:
        """Generate reasoning for critique approach"""
        
        planner_count = len([p for p in current_proposals if p.role.value == "planner"])
        
        if planner_count == 0:
            return "Waiting for implementation plan to provide constructive critique"
        else:
            return f"Analyzing implementation plan quality across feasibility, completeness, and best practices"
    
    def _calculate_critique_confidence(self, current_proposals: List[AgentResponse]) -> float:
        """Calculate confidence in critique quality"""
        
        base_confidence = 0.85
        
        # Higher confidence when there's a substantial plan to critique
        planner_proposals = [p for p in current_proposals if p.role.value == "planner"]
        if planner_proposals:
            latest_plan = planner_proposals[-1]
            plan_length = len(latest_plan.content.split())
            if plan_length > 200:
                base_confidence += 0.05
            if plan_length < 100:
                base_confidence -= 0.1
        
        return min(0.95, max(0.6, base_confidence))
    
    def _generate_improvement_suggestions(self, planner_proposals: List[AgentResponse]) -> List[str]:
        """Generate specific improvement suggestions"""
        
        if not planner_proposals:
            return ["Awaiting implementation plan for detailed feedback"]
        
        suggestions = [
            "Add specific timeline estimates for each implementation phase",
            "Include detailed risk assessment and mitigation strategies",
            "Specify resource requirements and allocation",
            "Enhance testing and validation approaches",
            "Consider alternative implementation strategies"
        ]
        
        return suggestions[:3]  # Return top 3 suggestions