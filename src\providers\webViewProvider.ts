import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { serviceRegistry } from '../utils/serviceRegistry';

export class WebViewProvider implements vscode.Disposable {
  private panel: vscode.WebviewPanel | undefined;
  private disposables: vscode.Disposable[] = [];

  constructor(private context: vscode.ExtensionContext) {}

  public async createOrShow(): Promise<void> {
    const column = vscode.window.activeTextEditor
      ? vscode.window.activeTextEditor.viewColumn
      : undefined;

    // If panel exists, just reveal it
    if (this.panel) {
      this.panel.reveal(column);
      return;
    }

    // Create new panel
    this.panel = vscode.window.createWebviewPanel(
      'metamorphic-reactor',
      'Metamorphic Reactor',
      column || vscode.ViewColumn.One,
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [
          vscode.Uri.file(path.join(this.context.extensionPath, 'webview')),
          vscode.Uri.file(path.join(this.context.extensionPath, 'out'))
        ]
      }
    );

    // Set panel icon
    this.panel.iconPath = vscode.Uri.file(path.join(this.context.extensionPath, 'resources', 'icon.png'));

    // Set webview content
    this.panel.webview.html = this.getWebviewContent();

    // Setup event handlers
    this.setupEventHandlers();
    
    console.log('WebView panel created and initialized');
  }

  private setupEventHandlers(): void {
    if (!this.panel) return;

    // Handle messages from the webview
    this.panel.webview.onDidReceiveMessage(
      (message) => this.handleWebviewMessage(message),
      undefined,
      this.disposables
    );

    // Handle panel disposal
    this.panel.onDidDispose(
      () => {
        console.log('WebView panel disposed');
        this.panel = undefined;
        this.cleanup();
      },
      undefined,
      this.disposables
    );

    // Handle panel state changes
    this.panel.onDidChangeViewState(
      (e) => {
        console.log('WebView panel state changed:', e.webviewPanel.active ? 'active' : 'inactive');
        if (e.webviewPanel.active) {
          this.refreshAgentStatus();
        }
      },
      undefined,
      this.disposables
    );
  }

  private cleanup(): void {
    // Clean up any running processes or connections
    console.log('Cleaning up WebView provider resources');
  }

  public get isActive(): boolean {
    return this.panel?.active ?? false;
  }

  public get isVisible(): boolean {
    return this.panel?.visible ?? false;
  }

  private getWebviewContent(): string {
    const webviewUri = this.panel!.webview.asWebviewUri(
      vscode.Uri.file(path.join(this.context.extensionPath, 'webview', 'dist'))
    );

    // Read the built HTML file
    const htmlPath = path.join(this.context.extensionPath, 'webview', 'dist', 'index.html');

    try {
      let htmlContent = fs.readFileSync(htmlPath, 'utf8');

      // Replace relative paths with webview URIs
      htmlContent = htmlContent.replace(
        /src="\/assets\//g,
        `src="${webviewUri}/assets/`
      );
      htmlContent = htmlContent.replace(
        /href="\/assets\//g,
        `href="${webviewUri}/assets/`
      );

      return htmlContent;
    } catch (error) {
      console.error('Failed to read built webview content:', error);

      // Fallback to a simple error message
      return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Metamorphic Reactor - Error</title>
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: var(--vscode-font-family);
      background-color: var(--vscode-editor-background);
      color: var(--vscode-editor-foreground);
      text-align: center;
    }
    .error-container {
      max-width: 600px;
      margin: 50px auto;
      padding: 20px;
      border: 1px solid var(--vscode-errorForeground);
      border-radius: 8px;
      background-color: var(--vscode-inputValidation-errorBackground);
    }
  </style>
</head>
<body>
  <div class="error-container">
    <h1>🧬 Metamorphic Reactor</h1>
    <h2>Build Required</h2>
    <p>The webview application needs to be built. Please run:</p>
    <pre>cd webview && npm run build</pre>
    <p>Error: ${error}</p>
  </div>
</body>
</html>`;
    }
  }

  private async handleWebviewMessage(message: any): Promise<void> {
    console.log('Received webview message:', message.command);
    
    try {
      switch (message.command) {
        case 'startTask':
          await this.handleStartTask(message.taskDescription, message.contextFiles);
          break;
        case 'stopTask':
          await this.handleStopTask();
          break;
        case 'getAgentStatus':
          await this.refreshAgentStatus();
          break;
        case 'getWorkspaceInfo':
          await this.handleGetWorkspaceInfo();
          break;
        case 'selectFiles':
          await this.handleSelectFiles();
          break;
        case 'clearOutput':
          this.sendMessage({ command: 'clearOutput' });
          break;
        default:
          console.warn('Unknown webview message:', message);
      }
    } catch (error) {
      console.error('Error handling webview message:', error);
      this.sendMessage({
        command: 'error',
        message: `Error: ${error}`
      });
    }
  }

  private async handleStartTask(taskDescription: string, contextFiles?: string[]): Promise<void> {
    try {
      console.log('Starting task:', taskDescription);
      console.log('Context files:', contextFiles || []);
      
      // Get WorkerThreadManager from service registry
      const workerThreadManager = serviceRegistry.getWorkerThreadManager();
      
      if (workerThreadManager) {
        // Generate task ID
        const taskId = `task-${Date.now()}`;
        
        // Spawn agents
        const agentIds = await workerThreadManager.spawnAgents(taskId);
        console.log('Spawned agents:', agentIds);
        
        // Get agent status and send to webview
        const agentStatus = workerThreadManager.getWorkerStatus();
        this.sendMessage({
          command: 'updateAgentStatus',
          agents: agentStatus
        });
        
        // Send task started notification
        this.sendMessage({
          command: 'taskStarted',
          taskId,
          taskDescription
        });
        
        // TODO: Start AutoGen orchestration service
        
      } else {
        throw new Error('WorkerThreadManager not available');
      }
      
    } catch (error) {
      console.error('Error starting task:', error);
      this.sendMessage({
        command: 'error',
        message: `Failed to start task: ${error}`
      });
    }
  }

  private async handleStopTask(): Promise<void> {
    try {
      console.log('Stopping task');
      
      // Get WorkerThreadManager from service registry
      const workerThreadManager = serviceRegistry.getWorkerThreadManager();
      
      if (workerThreadManager) {
        // Terminate all workers
        await workerThreadManager.terminateWorkers();
        
        // Send task stopped notification
        this.sendMessage({
          command: 'taskStopped'
        });
        
        // Clear agent status
        this.sendMessage({
          command: 'updateAgentStatus',
          agents: []
        });
        
      } else {
        console.warn('WorkerThreadManager not available');
      }
      
    } catch (error) {
      console.error('Error stopping task:', error);
      this.sendMessage({
        command: 'error',
        message: `Failed to stop task: ${error}`
      });
    }
  }

  private async refreshAgentStatus(): Promise<void> {
    try {
      const workerThreadManager = serviceRegistry.getWorkerThreadManager();
      
      if (workerThreadManager) {
        const agentStatus = workerThreadManager.getWorkerStatus();
        this.sendMessage({
          command: 'updateAgentStatus',
          agents: agentStatus
        });
      }
    } catch (error) {
      console.error('Error refreshing agent status:', error);
    }
  }

  private async handleGetWorkspaceInfo(): Promise<void> {
    try {
      const workspaceInfo = {
        projectType: 'TypeScript/Node.js',
        isGitRepository: vscode.workspace.workspaceFolders?.[0] ?
          await this.checkIfGitRepository(vscode.workspace.workspaceFolders[0].uri.fsPath) : false,
        rootPath: vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '',
        name: vscode.workspace.name || 'Unknown'
      };

      this.sendMessage({
        command: 'workspaceInfo',
        info: workspaceInfo
      });
    } catch (error) {
      console.error('Error getting workspace info:', error);
      this.sendMessage({
        command: 'error',
        message: `Failed to get workspace info: ${error}`
      });
    }
  }

  private async handleSelectFiles(): Promise<void> {
    try {
      const options: vscode.OpenDialogOptions = {
        canSelectMany: true,
        canSelectFiles: true,
        canSelectFolders: false,
        openLabel: 'Select Context Files',
        filters: {
          'All Files': ['*'],
          'TypeScript': ['ts', 'tsx'],
          'JavaScript': ['js', 'jsx'],
          'JSON': ['json'],
          'Markdown': ['md'],
          'Text': ['txt']
        }
      };

      const fileUris = await vscode.window.showOpenDialog(options);

      if (fileUris && fileUris.length > 0) {
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '';
        const relativePaths = fileUris.map(uri => {
          const fullPath = uri.fsPath;
          return workspaceRoot ? path.relative(workspaceRoot, fullPath) : fullPath;
        });

        this.sendMessage({
          command: 'filesSelected',
          files: relativePaths
        });
      } else {
        this.sendMessage({
          command: 'filesSelected',
          files: []
        });
      }
    } catch (error) {
      console.error('Error selecting files:', error);
      this.sendMessage({
        command: 'error',
        message: `Failed to select files: ${error}`
      });
    }
  }

  private async checkIfGitRepository(workspacePath: string): Promise<boolean> {
    try {
      const gitPath = path.join(workspacePath, '.git');
      return fs.existsSync(gitPath);
    } catch (error) {
      return false;
    }
  }

  public sendMessage(message: any): void {
    if (this.panel) {
      this.panel.webview.postMessage(message);
    }
  }

  public dispose(): void {
    if (this.panel) {
      this.panel.dispose();
    }
    
    this.disposables.forEach(disposable => disposable.dispose());
    this.disposables = [];
  }
}